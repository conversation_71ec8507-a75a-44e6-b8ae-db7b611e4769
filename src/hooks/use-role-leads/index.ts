import { useAppSelector } from "hooks/use-redux";
import { useMemo } from "react";
import { grantPermissionLeads } from "services/leads";

const useRoleLeads = () => {
  const { designation } = useAppSelector((state) => state.rootReducer.user);

  let nameRole = useMemo(() => {
    return grantPermissionLeads.find((item) => Object.values(item.children).includes(designation));
  }, [designation])?.role;

  return nameRole;
};

export default useRoleLeads;
