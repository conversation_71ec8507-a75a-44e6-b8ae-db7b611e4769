import axios from "axios";
import { useCallback, useEffect, useState } from "react";

const useApi = (api) => {
  const [response, setResponse] = useState({});
  const [cancelToken, setCancelToken] = useState(axios.CancelToken.source());

  useEffect(() => {
    return () => cancelToken.cancel();
  }, [cancelToken]);

  const action = useCallback(
    async (body, successResponse, onUploadProgress) => {
      let cancelToken = axios.CancelToken.source();
      setCancelToken(cancelToken);
      setResponse({ loading: true });
      try {
        const { data } = await api(body, cancelToken.token, onUploadProgress);
        setResponse({ data, ...successResponse });
      } catch (e) {
        console.error(e);
        setResponse({ error: e });
      }
    },
    [api]
  );

  return [response, action];
};

export default useApi;
