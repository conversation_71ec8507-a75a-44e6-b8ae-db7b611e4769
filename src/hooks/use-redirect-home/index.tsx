import { useRouter } from "next/router";
import { useEffect } from "react";

interface UseRedirectHomeProps {
  condition: any;
  dependency: any;
}

const useRedirectHome = ({ condition, dependency }: UseRedirectHomeProps) => {
  const router = useRouter();
  const { query } = router;

  useEffect(() => {
    if (dependency) {
      if (condition) {
        router.push({
          pathname: "/trang-chu",
          query: query,
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dependency, condition]);
};

export default useRedirectHome;
