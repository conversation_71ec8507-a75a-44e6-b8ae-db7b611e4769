import useDebounce from "hooks/use-debounce";
import { get } from "lodash";
import { useEffect, useState } from "react";
import { removeVietnameseTones } from "services/untils";

interface UseSearchProps {
  keySearch: string;
  searchType: "equal" | "includes";
  searchValue: string;
  data: any[];
}

const onSearchIncludes = (list: any[], searchValue: string, keySearch: string) => {
  return list?.filter((item) =>
    removeVietnameseTones(get(item, keySearch)?.toLowerCase().replace(/ /g, "") ?? "").includes(
      removeVietnameseTones(searchValue.toLowerCase().replace(/ /g, "") ?? "")
    )
  );
};

const onSearchDataEqual = (list: any[], searchValue: string, keySearch: string) => {
  return list?.filter(
    (item) =>
      removeVietnameseTones(get(item, keySearch)?.toLowerCase().replace(/ /g, "") ?? "").indexOf(
        removeVietnameseTones(searchValue.toLowerCase().replace(/ /g, "") ?? "")
      ) > -1
  );
};

const useSearch = ({ keySearch, searchType, searchValue, data }: UseSearchProps) => {
  const [dataAfterSearch, setDataAfterSearch] = useState(data ?? []);
  const debounceSearchValue = useDebounce(searchValue);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (debounceSearchValue) {
      timer = setTimeout(() => {
        if (searchType === "equal") {
          setDataAfterSearch(onSearchDataEqual(data, debounceSearchValue, keySearch));
        } else {
          setDataAfterSearch(onSearchIncludes(data, debounceSearchValue, keySearch));
        }
      }, 300);
    } else {
      setDataAfterSearch(data);
    }
    return () => clearTimeout(timer);
  }, [debounceSearchValue, data, searchType, keySearch]);

  return dataAfterSearch;
};

export default useSearch;
