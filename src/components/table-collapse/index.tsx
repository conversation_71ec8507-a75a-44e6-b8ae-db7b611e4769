import React from "react";
import { TableCollapseWrapper, TableCustom } from "./styled";
import { THEAD, TBODY, TH, TR } from "styles/table";
import TableRow from "./table-row";
import LoadingSection from "components/loading";
import { TableType } from "@custom-types/config-table";
import { TextAlign } from "@custom-types";

export interface ChildrenData {
  rowKey?: string;
  label: string;
  value?: number | string;
  detail?: string;
  children?: ChildrenData[];
  colSpan?: number;
  type?: TableType;
}
export interface TableCollapseData {
  label?: string;
  value?: number | string;
  children?: ChildrenData[];
  colSpan?: number;
  isHide?: boolean;
  type?: TableType;
}
export interface HeadTableCollapse {
  label: string;
  colSpan?: number;
  textAlign?: TextAlign;
}
export interface TableCollapse {
  head: HeadTableCollapse[][];
  body?: TableCollapseData[];
  loading: string | boolean;
  onViewDetail?: (item: any) => void;
}

const TableCollapse = ({ head, body, loading, onViewDetail }: TableCollapse) => {
  return (
    <TableCollapseWrapper className="hide-mobile">
      <TableCustom>
        <THEAD>
          {head.map((row, rowIndex) => (
            <TR key={rowIndex}>
              {row.map((cell, cellIndex) => (
                <TH key={cellIndex} colSpan={cell.colSpan} textAlign={cell.textAlign}>
                  {cell.label}
                </TH>
              ))}
            </TR>
          ))}
        </THEAD>
        <TBODY>
          {body?.map((data, index) => (
            <TableRow key={index} data={data} defaultOpen={true} onViewDetail={onViewDetail} />
          ))}
        </TBODY>
      </TableCustom>
      <LoadingSection loading={Boolean(loading)} isFullContent />
    </TableCollapseWrapper>
  );
};

export default TableCollapse;
