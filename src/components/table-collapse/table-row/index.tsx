import React, { Fragment, useState, useCallback, useEffect } from "react";
import { TD, TR } from "styles/table";
import { ButtonSecondary } from "styles/buttons";
import Icons from "components/icons";
import { TableCollapseData } from "../index";
import { <PERSON><PERSON><PERSON>ollap<PERSON>, TRCustom, SPAN } from "./styled";
import { formatValue } from "services/format-value";
import { isNil } from "lodash";

interface TableRow {
  data: TableCollapseData;
  defaultOpen?: boolean;
  onViewDetail?: (item: any) => void;
}

const TableRow = ({ data, defaultOpen, onViewDetail }: TableRow) => {
  const [isCollapse, setIsCollapse] = useState(false);

  useEffect(() => {
    if (defaultOpen) {
      setIsCollapse((state) => !state);
    }
  }, [defaultOpen]);

  const handleCollapse = useCallback(() => {
    setIsCollapse((state) => !state);
  }, [setIsCollapse]);

  return (
    <Fragment>
      {data.isHide ? (
        <TRCustom>
          {data.label && <TD colSpan={data.colSpan} dangerouslySetInnerHTML={{ __html: data.label }} />}
          {data.value && (
            <TD textAlign={data.type === "number" || data.type === "percent" ? "right" : "left"}>
              {formatValue(data.value, data.type)}
            </TD>
          )}
        </TRCustom>
      ) : (
        <TRCustom>
          <TD colSpan={data.colSpan} dangerouslySetInnerHTML={{ __html: data.label }} />
          <TD textAlign={data.type === "number" || data.type === "percent" ? "right" : "left"}>
            {formatValue(data.value, data.type)}
          </TD>
          <TD style={{ padding: 9 }}>
            {data.children ? (
              <ButtonCollapse open={isCollapse} onClick={handleCollapse}>
                <Icons icon="arrow-top" />
              </ButtonCollapse>
            ) : null}
          </TD>
        </TRCustom>
      )}
      {isCollapse &&
        data.children
          ?.filter((childrenItem) => !isNil(childrenItem))
          ?.map((child, childIndex) => (
            <Fragment key={childIndex}>
              <TR>
                <TD colSpan={child.colSpan} dangerouslySetInnerHTML={{ __html: child.label }} />
                <TD textAlign={child.type === "number" || child.type === "percent" ? "right" : "left"}>
                  {formatValue(child.value, child.type)}
                </TD>
                <TD style={{ padding: 9 }}>
                  {child.detail && (
                    <ButtonSecondary
                      size="tiny"
                      onClick={() =>
                        typeof onViewDetail === "function" && child.rowKey ? onViewDetail(child.rowKey) : null
                      }
                    >
                      {child.detail}
                    </ButtonSecondary>
                  )}
                </TD>
              </TR>
              {child.children?.map((item, index) => (
                <TR key={index}>
                  <TD colSpan={item.colSpan}>
                    <SPAN
                      className={index === 0 ? "short-span" : ""}
                      dangerouslySetInnerHTML={{ __html: item.label }}
                    />
                  </TD>
                  <TD textAlign={item.type === "number" || item.type === "percent" ? "right" : "left"}>
                    {formatValue(item.value, item.type)}
                  </TD>
                  <TD style={{ padding: 9 }}>
                    {item.detail && (
                      <ButtonSecondary
                        size="tiny"
                        onClick={() =>
                          typeof onViewDetail === "function" && item.rowKey ? onViewDetail(item.rowKey) : null
                        }
                      >
                        {item.detail}
                      </ButtonSecondary>
                    )}
                  </TD>
                </TR>
              ))}
            </Fragment>
          ))}
    </Fragment>
  );
};

export default TableRow;
