import { useState } from "react";
import { ImagePercentWrapper } from "styles";
import { ContentWrap, IconWrap, Title, VideoWrapper } from "./styled";

interface VideoItemProps {
  src: string;
  imgSrc: string;
  title: string;
  numberItem?: number;
}
const VideoItem = ({ src, imgSrc, title, numberItem }: VideoItemProps) => {
  const [show, setShow] = useState(false);

  return (
    <ContentWrap>
      <VideoWrapper>
        <ImagePercentWrapper percent={53} radius={12} percentMobile={58}>
          <img src={imgSrc} alt="bg" />
        </ImagePercentWrapper>
        {show ? (
          <iframe
            id="ytplayer"
            typeof="text/html"
            title="corporate"
            src={src}
            allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          ></iframe>
        ) : (
          <IconWrap onClick={() => setShow(!show)} className="icon">
            <img src={`${process.env.basePath}/img/bieu-mau-va-bao-cao/ic-play.svg`} alt="bg" />
          </IconWrap>
        )}
      </VideoWrapper>
      <Title className="h7" title={title}>
        {title}
      </Title>
    </ContentWrap>
  );
};

export default VideoItem;
