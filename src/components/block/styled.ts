import styled from "styled-components";
import { ButtonIcon } from "styles/buttons";
import { device } from "styles/media";

export const BlockWrapper = styled.div<{ background?: string; border?: boolean }>`
  width: 100%;
  padding: 20px;
  margin-top: 16px;

  border-radius: 16px;
  background: ${({ background }) => background ?? "#ffffff"};

  border: ${({ border }) => border && `1px solid #DBDFE1 `};

  @media ${device.mobile} {
    margin-top: 16px;
    padding: 16px;
  }
`;

export const TitleWrapper = styled.div<{ isCollapse: boolean }>`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;

  cursor: ${({ isCollapse }) => (isCollapse ? "pointer" : "")};
`;

export const Title = styled.h6`
  font-weight: 700;
  font-size: 16px;
  line-height: 125%;
`;

export const ShowMore = styled.p`
  font-weight: 700;
  font-size: 14px;
  line-height: 125%;
  white-space: nowrap;

  color: ${({ theme }) => theme.color.status.primary};
  cursor: pointer;
`;

export const ButtonCollapse = styled(ButtonIcon)<{ open: boolean }>`
  svg {
    transform: ${({ open }) => (open ? "rotate(0deg)" : "rotate(-180deg)")};
  }
`;

export const BlockContentWrapper = styled.div`
  margin-top: 20px;

  @media ${device.mobile} {
    margin-top: 13px;
  }
`;

export const BLockContent = styled.div<{ open: boolean; isCollapse: boolean; overflow: string }>`
  height: auto;
  overflow: ${({ overflow }) => overflow};
  transition: max-height 0.3s ease-in-out;
`;
