import styled from "styled-components";
import { FadeReverseStyled, FadeStyled } from "styles/fade-animation";

export const FuncHeaderSOAWrapper = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;

  flex: 1;
`;

export const TooltipContent = styled.div<{ show: boolean; isTooltip: boolean }>`
  width: 100%;
  padding: 8px 16px;

  position: absolute;
  top: calc(100% - 6px);
  right: 0;
  z-index: 1;

  background: #ffffff;
  filter: drop-shadow(0px 0px 8px rgba(0, 0, 0, 0.25));
  border-radius: 4px;

  &:after {
    content: "";
    display: ${({ isTooltip }) => (isTooltip ? "block" : "none")};

    position: absolute;
    bottom: 100%;
    left: calc(100% - 26px);
    transform: translateX(-50%);

    border-width: 10px;
    border-style: solid;
    border-color: transparent transparent white transparent;
  }

  ${({ show }) => (show ? FadeStyled : FadeReverseStyled)}
`;

export const RowDataTooltip = styled.div`
  margin: 6px 0px;
`;

export const RowDataDropdown = styled.div`
  padding: 8px 0px;

  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
`;

export const DropdownTitle = styled.h6`
  color: #ffffff !important;
`;
