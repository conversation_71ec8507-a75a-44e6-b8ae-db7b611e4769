import styled from "styled-components";
import { TH, THEAD, TableStyle } from "styles/table";

export const TableDesktopSOAWrapper = styled.div`
  width: 100%;

  position: relative;

  border-radius: 16px;
  border: 1px solid ${({ theme }) => theme.color.status.grey};
`;

export const TableCustom = styled(TableStyle)`
  table-layout: fixed;
  width: 100%;
`;

export const THEADCustom = styled(THEAD)`
  .${TH.styledComponentId} {
    white-space: normal;
  }

  .uppercase {
    text-transform: uppercase;
  }
`;

export const TableSOANote = styled.div`
  background-color: ${({ theme }) => theme.color.status.yellow_20};
  border-radius: 8px;
  padding: 16px;

  & > div:first-child {
    margin-top: 0;
  }

  & > div:not(:first-child) {
    margin-top: 12px;
  }

  ul {
    padding-left: 44px;
    margin-top: 8px;
  }
`;

export const TableSOANoteItem = styled.div`
  display: flex;
  align-items: center;

  p {
    margin-left: 10px;
  }
`;
