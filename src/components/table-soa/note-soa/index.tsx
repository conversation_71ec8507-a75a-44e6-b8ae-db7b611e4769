import Icons from "components/icons";
import { Fragment } from "react";
import { NoteContent, NoteI<PERSON>, NoteSOAWrapper } from "./styled";

interface NoteSOA {
  title?: string;
  children?: string[];
}

interface NoteSOAProps {
  data?: NoteSOA[];
  note?: string;
}

const NoteSOA = ({ data, note }: NoteSOAProps) => {
  return (
    <NoteSOAWrapper>
      <NoteContent>
        {data?.map((item, index) => (
          <Fragment key={index}>
            <NoteItem>
              <Icons icon="tick" />
              <p className="body-4">{item.title}</p>
            </NoteItem>
            {item.children && (
              <ul>
                {item.children.map((child, i) => (
                  <li key={i}>
                    <p className="body-4">{child}</p>
                  </li>
                ))}
              </ul>
            )}
          </Fragment>
        ))}
        {note ? <p className="body-4">{note}</p> : null}
      </NoteContent>
    </NoteSOAWrapper>
  );
};

export default NoteSOA;
