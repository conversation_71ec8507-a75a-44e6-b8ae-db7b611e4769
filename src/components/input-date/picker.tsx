import moment, { isDate } from "moment";
import { useCallback, useEffect, useState } from "react";
import IconArrowLeft from "./icons/arrow-left";
import IconArrowRight from "./icons/arrow-right";
import {
  ButtonIcon,
  DatePickerContent,
  DatePicker<PERSON>eader,
  DatePickerWrapper,
  DayButton,
  DaysWrapper,
  HeaderButton,
  MonthButton,
  MonthsWrapper,
  WeekTitleWrapper,
  WeekWrapper,
} from "./styled";

const weekTitle = ["Mon", "<PERSON><PERSON>", "<PERSON>", "Thu", "Fri", "Sat", "Sun"];

const WeekRender = ({
  date,
  month,
  value,
  enableFuture,
  enableCurrentMonth,
  disablePast,
  disableCurrentDate,
  onChange,
}: {
  date: Date;
  month: number;
  value?: Date;
  enableFuture: boolean;
  enableCurrentMonth: boolean;
  disablePast: boolean;
  disableCurrentDate: boolean;
  onChange: (date: Date) => void;
}) => {
  const monday = moment(date).startOf("isoWeek");
  const week = [monday.toDate()];
  const today = moment().toDate();

  for (var i = 0; i < 6; i++) {
    week.push(monday.add(1, "day").toDate());
  }

  const disabled = useCallback(
    (dateInWeek: Date) => {
      if (disablePast) {
        //add 1 day: because isBefore <= today
        return moment(dateInWeek).add(1, "day").isBefore(today);
      }

      if (disableCurrentDate) {
        const isToday = moment(dateInWeek).isSame(today, "day");
        if (isToday) return true;
      }

      if (enableFuture) {
        return false;
      }

      if (enableCurrentMonth) {
        const lastDateOfMonth = moment(today).endOf("month");

        return moment(dateInWeek).isAfter(lastDateOfMonth);
      }

      return today.getTime() < dateInWeek.getTime();
    },
    [enableCurrentMonth, enableFuture, disablePast, today, disableCurrentDate]
  );

  return (
    <WeekWrapper>
      {week.map((dateInWeek) => (
        <DayButton
          key={dateInWeek.toJSON()}
          currentMonth={dateInWeek.getMonth() === month}
          dateActive={value?.getTime() === dateInWeek.getTime()}
          disabled={disabled(dateInWeek)}
          onClick={() => onChange(dateInWeek)}
        >
          {dateInWeek.getDate()}
        </DayButton>
      ))}
    </WeekWrapper>
  );
};

const MonthRender = ({
  year,
  month,
  value,
  enableFuture,
  enableCurrentMonth,
  disablePast,
  disableCurrentDate,
  onChange,
}: {
  year: number;
  month: number;
  value?: Date;
  enableFuture: boolean;
  enableCurrentMonth: boolean;
  disablePast: boolean;
  disableCurrentDate: boolean;
  onChange: (date: Date) => void;
}) => {
  const date = moment([year, month]);
  const weeks = [];

  while (date.get("month") === month || date.startOf("isoWeek").get("month") === month) {
    weeks.push(
      <WeekRender
        value={value}
        key={date.startOf("isoWeek").format()}
        date={date.toDate()}
        month={month}
        enableFuture={enableFuture}
        disableCurrentDate={disableCurrentDate}
        enableCurrentMonth={enableCurrentMonth}
        disablePast={disablePast}
        onChange={onChange}
      />
    );
    date.add(7, "day");
  }

  return <>{weeks}</>;
};

const DatePicker = ({
  value,
  enableFuture,
  enableCurrentMonth,
  disablePast,
  disableCurrentDate,
  onChange,
}: {
  value: Date;
  enableFuture: boolean;
  enableCurrentMonth: boolean;
  disablePast: boolean;
  disableCurrentDate: boolean;
  onChange: (date: Date) => void;
}) => {
  const today = isDate(value) && moment(value).isValid() ? value : new Date();

  const [picker, setPicker] = useState("day");
  const [month, setMonth] = useState(today.getMonth());
  const [year, setYear] = useState(today.getFullYear());

  const handleChange = useCallback(
    (date: Date) => {
      onChange(date);
      setMonth(date.getMonth());
      setYear(date.getFullYear());
    },
    // eslint-disable-next-line
    [month, year]
  );

  useEffect(() => {
    const date = isDate(value) && moment(value).isValid() ? value : new Date();

    setMonth(date.getMonth());
    setYear(date.getFullYear());
  }, [value]);

  return (
    <DatePickerWrapper>
      <DatePickerContent>
        <DatePickerHeader>
          <ButtonIcon
            onClick={() => {
              if (picker === "day") {
                let date = moment([year, month]).subtract(1, "month");
                setYear(date.get("year"));
                setMonth(date.get("month"));
              } else {
                setYear(year - 1);
              }
            }}
          >
            <IconArrowLeft />
          </ButtonIcon>
          <HeaderButton
            onClick={() => {
              setPicker((pre) => (pre === "day" ? "month" : "day"));
            }}
          >
            <h6>{picker === "day" ? `Tháng ${month + 1} năm ${year}` : year}</h6>
          </HeaderButton>
          <ButtonIcon
            onClick={() => {
              if (picker === "day") {
                let date = moment([year, month]).add(1, "month");
                setYear(date.get("year"));
                setMonth(date.get("month"));
              } else {
                setYear(year + 1);
              }
            }}
          >
            <IconArrowRight />
          </ButtonIcon>
        </DatePickerHeader>
        {picker === "day" ? (
          <DaysWrapper>
            <WeekTitleWrapper>
              {weekTitle.map((e, index) => (
                <p key={index}>{e}</p>
              ))}
            </WeekTitleWrapper>
            <MonthRender
              year={year}
              month={month}
              value={value}
              enableFuture={enableFuture}
              disableCurrentDate={disableCurrentDate}
              enableCurrentMonth={enableCurrentMonth}
              disablePast={disablePast}
              onChange={handleChange}
            />
          </DaysWrapper>
        ) : (
          <MonthsWrapper>
            {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11].map((m) => (
              <MonthButton
                key={m}
                active={month === m}
                onClick={() => {
                  setMonth(m);
                  setPicker("day");
                }}
              >
                {`Tháng ${m + 1}`}
              </MonthButton>
            ))}
          </MonthsWrapper>
        )}
      </DatePickerContent>
    </DatePickerWrapper>
  );
};

export default DatePicker;
