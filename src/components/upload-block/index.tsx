import { ERROR, ERROR_API_MESSAGE, WARNING } from "@constants/message";
import { File, FileStatusType } from "@custom-types";
import { <PERSON>ading<PERSON><PERSON> } from "@custom-types/loading";
import { Alert } from "components/alert";
import Icons from "components/icons";
import { UploadLoading } from "components/loading/styled";
import { cloneDeep, isArray } from "lodash";
import { ChangeEvent, DragEvent, useCallback, useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { setLoading } from "redux/root-reducer";
import { getBase64, resizeImageBase64 } from "services/format-file";
import { sizeFile } from "services/untils";
import { ImagePercentWrapper } from "styles";
import { ButtonIcon } from "styles/buttons";
import { Error, WrapperError } from "styles/input-styled";
import {
  BlockWrapper,
  ButtonUpload,
  DragWrapper,
  FileListWrapper,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  UploadBlockWrapper,
} from "./styled";

interface UploadBlockProps {
  title: string;
  value?: any[];
  require?: boolean;
  disabled?: boolean;
  limit?: number;
  error?: string;
  isGreyScale?: boolean;
  accept?: string[];
  acceptAll?: string;
  maxSize?: number;
  onChange?: (file: File[]) => void;
}

type FileChange = DragEvent<HTMLDivElement> & ChangeEvent<HTMLInputElement>;

const UploadBlock = ({
  title,
  value,
  require,
  disabled,
  limit,
  error,
  isGreyScale,
  accept = [],
  acceptAll,
  maxSize = 0,
  onChange,
}: UploadBlockProps) => {
  const [fileShow, setFileShow] = useState<File[]>();
  const [dragEnter, setDragEnter] = useState(false);
  const [uploadLoading, setUploadLoading] = useState(false);

  const ref = useRef(null);
  const dispatch = useDispatch();

  useEffect(() => {
    setFileShow(value);
  }, [value]);

  const onDragEnter = useCallback(() => {
    setDragEnter(true);
  }, []);

  const onDragLeave = useCallback(() => {
    setDragEnter(false);
  }, []);

  const handleDragOver = useCallback((e: DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleUpload = useCallback(() => {
    if (disabled || uploadLoading) {
      return;
    }

    ref.current.click();
  }, [disabled, uploadLoading]);

  const handleChangeFile = useCallback(
    async (e: FileChange, type: "drop" | "click") => {
      e.preventDefault();
      e.stopPropagation();

      try {
        const fileEvent = type === "click" ? Array.from(e.target.files) : Array.from(e.dataTransfer.files);
        e.target.value = null;
        const fileList: File[] = cloneDeep(value) ?? [];

        dispatch(setLoading({ uploadFileLoading: "local" } as LoadingKey));
        setUploadLoading(true);

        for (let i = 0; i < fileEvent.length; i++) {
          //validate for type all: example: audio/*, image/*,...
          if (acceptAll) {
            const splitAcceptAll = acceptAll.split("/")[0];
            const splitFileName = fileEvent[i].type.split("/")[0];

            if (splitAcceptAll !== splitFileName) {
              continue;
            }
          }

          //validate for specify type, example: .mp3, .png, .jpg,...
          if (accept.length) {
            const splitFileName = fileEvent[i].name.split(".");

            if (!accept.includes("." + splitFileName[splitFileName.length - 1])) {
              continue;
            }
          }

          let base64 = "";
          let status: FileStatusType = "";

          if (fileEvent[i].type.includes("image")) {
            base64 = await resizeImageBase64(fileEvent[i], isGreyScale);
          } else {
            base64 = await getBase64(fileEvent[i]);
          }

          if (maxSize && sizeFile(fileEvent[i].size) > maxSize) {
            Alert(WARNING, `Tài liệu ${fileEvent[i].name} đã vượt kích thước quy định ${maxSize}MB`);
            status = "FAIL";
          } else {
            status = "SUCCESS";
          }

          const formatFile = {
            id: new Date().getTime(),
            fileContent: base64.split(",")[1],
            fileName: fileEvent[i].name,
            mime: fileEvent[i].type,
            size: fileEvent[i].size,
            status,
          };

          setFileShow((pre) => {
            let newFileList = isArray(pre) ? [...pre] : [];
            newFileList.push(formatFile);

            return newFileList;
          });

          fileList.push(formatFile);
        }

        if (fileList.length) {
          const limitFile = fileList.slice(0, limit);
          onChange(limitFile);
        }
      } catch (error) {
        console.log(error);
        Alert(ERROR, ERROR_API_MESSAGE);
      } finally {
        setDragEnter(false);
        setUploadLoading(false);
        dispatch(setLoading({ uploadFileLoading: "" } as LoadingKey));
      }
    },
    [value, limit, isGreyScale, maxSize, accept, acceptAll, onChange, dispatch]
  );

  const handleRemove = useCallback(
    (item: File) => {
      if (disabled || uploadLoading) {
        return;
      }

      const filter = value.filter((valueItem) => valueItem.id !== item.id);

      if (filter) {
        onChange(filter);
        setFileShow(filter);
      }
    },
    [value, disabled, uploadLoading, onChange]
  );

  return (
    <UploadBlockWrapper>
      <BlockWrapper onDragEnter={onDragEnter} error={Boolean(error)}>
        <Header>
          <label className="label-1">
            {title} {require && <span>(*)</span>}
          </label>
        </Header>
        <FileListWrapper>
          {fileShow?.length > 0
            ? fileShow.map((item: File, index) => (
                <FileWrapper key={index} error={item.status === "FAIL"}>
                  <ButtonIcon onClick={() => handleRemove(item)}>
                    <Icons icon="icon-close-border" />
                  </ButtonIcon>
                  <ImagePercentWrapper percent={100}>
                    <img
                      src={
                        item.mime === "application/pdf"
                          ? `${process.env.basePath}/img/icon-pdf-file-40px.svg`
                          : ["video", "audio"].includes(item.mime?.split("/")[0])
                          ? `${process.env.basePath}/img/icon-micro-file.svg`
                          : `data:image/jpg;base64, ${item.fileContent}`
                      }
                      alt=""
                    />
                  </ImagePercentWrapper>
                </FileWrapper>
              ))
            : null}
          <ButtonUpload width={fileShow?.length ? "60px" : "100%"} dragEnter={dragEnter} onClick={handleUpload}>
            {uploadLoading ? <UploadLoading /> : <Icons icon="icon-upload-add" />}
            {!fileShow?.length ? <label className="label-1">Tải file lên</label> : null}
            <input
              type="file"
              name="photo"
              ref={ref}
              onChange={(e) => handleChangeFile(e as FileChange, "click")}
              multiple
              accept={acceptAll ?? accept.join()}
            />
          </ButtonUpload>
        </FileListWrapper>
        {dragEnter && (
          <DragWrapper
            onDrop={(e) => handleChangeFile(e as FileChange, "drop")}
            onDragLeave={onDragLeave}
            onDragOver={handleDragOver}
          />
        )}
      </BlockWrapper>
      {error && (
        <WrapperError>
          <Icons icon="error-icon" />
          <Error>{error}</Error>
        </WrapperError>
      )}
    </UploadBlockWrapper>
  );
};

export default UploadBlock;
