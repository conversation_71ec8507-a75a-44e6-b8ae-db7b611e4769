import styled, { css } from "styled-components";
import { ButtonIcon } from "styles/buttons";

export const UploadBlockWrapper = styled.div`
  display: flex;
  flex-direction: column;

  position: relative;
`;

export const BlockWrapper = styled.div<{ error: boolean }>`
  width: 100%;
  padding: 16px;
  padding-bottom: 12px;

  position: relative;

  border: 1px solid ${({ theme, error }) => (error ? theme.color.status.red : theme.color.status.grey)};
  border-radius: 12px;

  input {
    opacity: 0;
    position: absolute;
    z-index: -1;
  }
`;

export const Header = styled.div`
  padding-bottom: 16px;
  margin-bottom: 12px;

  border-bottom: 1px solid ${({ theme }) => theme.color.status.grey};

  label {
    span {
      color: ${({ theme }) => theme.color.status.red};
    }
  }
`;

const DragEnterStyle = css`
  /* transition: all 0.3s ease-in-out; */

  background: ${({ theme }) => theme.color.status.primary_5};
  border-color: ${({ theme }) => theme.color.status.primary};
`;

const DragOutStyle = css`
  /* transition: all 0.3s ease-in-out; */

  background: ${({ theme }) => theme.color.status.grey_20};
  border-color: ${({ theme }) => theme.color.status.grey_20};
`;

export const ButtonFirstUpload = styled.div<{ dragEnter: boolean }>`
  min-height: 60px;
  margin: 4px 0px;

  display: flex;
  justify-content: center;
  align-items: center;

  background: ${({ theme }) => theme.color.status.grey_20};
  border: 1px solid ${({ theme }) => theme.color.status.grey_20};
  border-radius: 8px;

  cursor: pointer;

  label {
    margin-left: 6px;
    color: ${({ theme }) => theme.color.status.primary};

    cursor: pointer;
  }

  ${({ dragEnter }) => (dragEnter ? DragEnterStyle : DragOutStyle)}
`;

export const FileListWrapper = styled.div`
  margin-left: -4px;
  margin-right: -4px;

  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
`;

export const FileWrapper = styled.div<{ error?: boolean }>`
  width: 60px;
  height: 60px;
  margin: 4px;

  position: relative;
  overflow: hidden;

  background: ${({ theme }) => theme.color.status.grey_20};
  border: 1px solid ${({ theme, error }) => (error ? theme.color.status.red : theme.color.status.grey_50)};
  border-radius: 8px;

  .${ButtonIcon.styledComponentId} {
    position: absolute;
    top: 2px;
    right: 2px;
    z-index: 2;
  }
`;

export const ButtonUpload = styled(FileWrapper)<{ dragEnter: boolean; width: string }>`
  width: ${({ width }) => width};

  display: flex;
  justify-content: center;
  align-items: center;

  cursor: pointer;

  label {
    margin-left: 6px;
    color: ${({ theme }) => theme.color.status.primary};

    cursor: pointer;
  }

  ${({ dragEnter }) => (dragEnter ? DragEnterStyle : DragOutStyle)}
`;

export const DragWrapper = styled.div`
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  user-select: none;
  cursor: pointer;
`;
