import styled from "styled-components";
import { ScrollBarStyle } from "styles";
import { FadeWithTransformReverseStyled, FadeWithTransformStyled } from "styles/fade-animation";

export const DropDownHierarchyWrapper = styled.div`
  width: 100%;
  position: relative;
`;

export const DropDownFiled = styled.button<{ mobile: boolean }>`
  width: 100%;
  max-height: 48px;
  padding: 8px 40px 8px 16px;

  display: flex;
  align-items: center;

  position: relative;

  border: 1px solid ${({ mobile }) => (mobile ? "#DBDFE1" : "#ffff")};
  border-radius: 4px;
  background-color: transparent;

  svg {
    width: 24px;
    height: 24px;

    position: absolute;
    top: 50%;
    right: 0px;
    transform: translate(-50%, -50%);
  }

  p {
    font-weight: 400;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-transform: uppercase;

    color: ${({ mobile }) => (mobile ? "#333333" : "#ffff")};
  }
`;

export const List = styled.div<{ show: boolean }>`
  max-height: 400px;
  padding: 0px 9px 9px 9px;

  position: absolute;
  width: 100%;
  top: 100%;
  left: 0;
  z-index: 100;
  overflow-x: hidden;

  border-radius: 4px;
  background-color: #ffffff;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);

  ${ScrollBarStyle}

  ${({ show }) => (show ? FadeWithTransformStyled : FadeWithTransformReverseStyled)}
`;

//Item
export const AgentItemWrapper = styled.div`
  &:last-child {
    margin-bottom: 0;
  }
`;

export const SubItemWrapper = styled.div`
  padding-left: 13px;
  margin-left: 20px;

  border-left: 1px solid ${({ theme }) => theme.color.text.primary};
`;

export const ItemMenu = styled.button<{ active: boolean }>`
  width: 100%;
  padding: 8px;
  padding-left: 32px;

  display: flex;
  align-items: center;

  position: relative;

  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  text-align: left;
  text-transform: uppercase;

  border-radius: 4px;
  color: ${({ active, theme }) => (active ? theme.color.text.primary : theme.color.text.body)};
  background: ${({ active, theme }) => (active ? theme.color.status.primary_20 : "ffffff")};

  transition: all 0.3s ease-in-out;
  cursor: pointer;

  :hover {
    color: ${({ theme }) => theme.color.status.primary};
  }
`;

export const Triangle = styled.span<{ active: boolean; show: boolean }>`
  width: 24px;
  height: 24px;

  display: ${({ show }) => (show ? "flex" : "none")};
  align-items: center;
  justify-content: center;

  position: absolute;
  top: 50%;
  left: 8px;
  transform: translateY(-50%);

  svg {
    transform: ${({ active }) => (active ? "rotate(90deg)" : null)};
  }
`;

export const SubItemMenu = styled.button`
  margin-bottom: 20px;

  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #333333;

  cursor: pointer;

  &:last-child {
    margin-bottom: 0;
  }

  position: relative;
`;

export const SearchHierarchyWrapper = styled.div`
  padding: 9px 0px 0px 0px;
  margin-bottom: 8px;
  width: 100%;
  position: sticky;
  left: 0;
  top: 0px;
  z-index: 10;
  background: #ffffff;
  box-shadow: 3px 3px 34px 0px rgba(0, 0, 0, 0.05);
`;
