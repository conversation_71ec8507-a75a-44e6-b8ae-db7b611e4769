import { HierarchyData, HierarchyInput, HierarchyOutput } from "@custom-types/team";
import { getHierarchyList } from "api/team";
import InputSearch from "components/input-search";
import LoadingSection from "components/loading";
import ScrollBar from "components/scroll-bar";
import useActionApi from "hooks/use-action-api";
import useClickAway from "hooks/use-click-away";
import useDebounce from "hooks/use-debounce";
import { useAppSelector } from "hooks/use-redux";
import { useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { setCurrentAgentOfTeam, setHierarchy } from "screens/team/performance/slice";
import { removeVietnameseTones } from "services/untils";
import { NoData } from "styles";
import Icons from "../icons";
import AgentItem from "./agent-item";
import { DropDownFiled, DropDownHierarchyWrapper, List, SearchHierarchyWrapper } from "./styled";
import { cloneDeep, sortBy } from "lodash";

interface DropDownHierarchyProps {
  mobile?: boolean;
}

export function formatAgentList(agentList: any[], agentId: string): any[] {
  const children: any[] = [];
  for (const agent of agentList) {
    if (agent?.supervisorId === agentId) {
      const agentWithInfo = {
        ...agent,
        agentInfo: `${agent.agentCode} - ${agent.designationCd} - ${agent.agentName}`,
        children: formatAgentList(agentList, agent.agentCode),
      };
      children.push(agentWithInfo);
    }
  }
  return children;
}

function DropDownHierarchy({ mobile }: DropDownHierarchyProps) {
  const [show, setShow] = useState(false);
  const [label, setLabel] = useState("Họ tên TVTC");
  const [inputSearch, setInputSearch] = useState("");
  const [baseDataList, setBaseDataList] = useState<HierarchyData[]>([]);
  const [dataFilterList, setDataFilterList] = useState<HierarchyData[]>([]);
  const debouncedValue = useDebounce(inputSearch, 500);

  const ref = useRef();
  const dispatch = useDispatch();

  const {
    user: { username, designation, name },
    loading: { getHierarchyLoading },
  } = useAppSelector((state) => state.rootReducer);
  const { hierarchyList, currentAgentOfTeam } = useAppSelector((state) => state.teamPerformanceReducer);

  const actionGetHierarchy = useActionApi<HierarchyInput, HierarchyOutput>(getHierarchyList);

  useEffect(() => {
    if (username && !hierarchyList.length) {
      actionGetHierarchy({
        body: { agentId: username },
        loading: {
          type: "local",
          name: "getHierarchyLoading",
        },
      })
        .then(({ data }) => {
          if (data?.listData.length) {
            const currentAgent = data.listData.find((item) => item.agentCode === username);
            const parentAgentCode = currentAgent.supervisorId ?? "";
            dispatch(setHierarchy(formatAgentList(data.listData, parentAgentCode)));
          }
        })
        .catch((error) => {
          console.log("error", error);
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [username, hierarchyList]);

  useEffect(() => {
    if (username) {
      //ignore children, because children have many data
      dispatch(
        setCurrentAgentOfTeam({
          agentCode: username,
          agentName: name,
          designationCd: designation,
          level: "",
          manager: true,
          supervisorId: "",
          branchLvl: 0,
        })
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [username, name, designation]);

  useEffect(() => {
    if (username && hierarchyList.length) {
      const initAgentOfTeam = hierarchyList.find((item) => item.agentCode === username);
      handleSelectItem(initAgentOfTeam);
      setBaseDataList(sortData(hierarchyList));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hierarchyList, username]);

  useClickAway(ref, (e) => {
    setShow(false);
  });

  const sortData = (dataSort: HierarchyData[]): HierarchyData[] => {
    return dataSort.map((item) => {
      const children =
        item.children && item.children.length > 0 ? sortData(sortBy(cloneDeep(item.children), "branchLvl")) : [];
      return { ...item, children };
    });
  };

  const handleSelectItem = (value: HierarchyData) => {
    setShow(false);
    //ignore children, because children have many data
    dispatch(
      setCurrentAgentOfTeam({
        agentCode: value.agentCode,
        agentName: value.agentName,
        designationCd: value.designationCd,
        level: value.level,
        manager: value.manager,
        supervisorId: value.supervisorId,
        branchLvl: value.branchLvl,
      })
    );
    setLabel(`${value.agentCode} - ${value.designationCd} - ${value.agentName}`);
  };

  const onShowList = () => {
    setShow(true);
  };

  useEffect(() => {
    if (debouncedValue?.length) {
      findAgentByCode(debouncedValue, baseDataList);
    } else {
      setDataFilterList(baseDataList);
    }
  }, [debouncedValue, baseDataList]);

  const findAgentByCode = (inputSearch: string, data: HierarchyData[]) => {
    const result: HierarchyData[] = [];
    // Hàm đệ quy để tìm kiếm agentCode trong mảng data và các phần tử con
    const filterAgent = (agent: HierarchyData, input: string) => {
      if (
        agent.agentCode.includes(input) ||
        removeVietnameseTones(agent.agentName).toLowerCase().includes(removeVietnameseTones(input).toLowerCase())
      ) {
        result.push(agent);
      }
      // Nếu có children, tiếp tục tìm kiếm trong children
      if (agent.children && agent.children.length > 0) {
        for (let i = 0; i < agent.children.length; i++) {
          filterAgent(agent.children[i], input);
        }
      }
    };

    // Duyệt qua mỗi phần tử trong mảng data để tìm kiếm
    for (let i = 0; i < data.length; i++) {
      filterAgent(data[i], inputSearch);
    }

    setDataFilterList(result);
  };

  return (
    <DropDownHierarchyWrapper>
      <DropDownFiled onClick={onShowList} mobile={mobile}>
        <p className="body-4">{label}</p>
        <Icons icon="icon-up-down" fill={`${mobile ? "#E87722" : "#ffff"}`} />
      </DropDownFiled>
      <List ref={ref} show={show}>
        <SearchHierarchyWrapper>
          <InputSearch
            value={inputSearch}
            onChange={(e) => setInputSearch(e.target.value)}
            type="text"
            placeholder="Nhập thông tin cần tìm kiếm"
          />
        </SearchHierarchyWrapper>
        <ScrollBar>
          <div className="relative">
            {dataFilterList?.length ? (
              dataFilterList?.map((item, index) => (
                <AgentItem
                  key={index}
                  item={item}
                  currentValue={currentAgentOfTeam?.agentCode}
                  onSelectItem={handleSelectItem}
                />
              ))
            ) : (
              <NoData>Không có dữ liệu</NoData>
            )}
          </div>
        </ScrollBar>
      </List>
      <LoadingSection loading={Boolean(getHierarchyLoading)} isFullContent color={mobile ? "#E87722" : "#ffff"} />
    </DropDownHierarchyWrapper>
  );
}

export default DropDownHierarchy;
