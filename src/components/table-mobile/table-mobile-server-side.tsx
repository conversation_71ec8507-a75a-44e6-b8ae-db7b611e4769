import { ValueLabel } from "@custom-types";
import { DataTable, TableConfig } from "@custom-types/config-table";
import CheckBox from "components/checkbox";
import LoadingSection from "components/loading";
import { TablePaginationProps } from "components/table/table-sever-side";
import { useCallback, useState } from "react";
import { FlexBox } from "styles";
import { DescriptionWrapper, TableMobileWrapper } from "./styled";
import ItemTable from "./item-table";

interface TableMobileProp {
  config: TableConfig[];
  data: DataTable[][];
  description?: ValueLabel[];
  showCollapse?: boolean;
  multiSelect?: any[];
  customEmptyData?: any;
  loading?: boolean;
  pagination?: TablePaginationProps;
  onChangeMultiSelect?: (data: any) => void;
  onChangePage?: (page: number, pageSize: number) => void;
}

const TableMobileSeverSide = ({
  data,
  config,
  description,
  showCollapse = true,
  multiSelect,
  customEmptyData,
  loading,
  pagination,
  onChangeMultiSelect,
  onChangePage,
}: TableMobileProp) => {
  const [isSelectAll, setIsSelectAll] = useState(false);

  const handleSelectAll = useCallback(
    (isSelectAll: boolean) => {
      if (!data.length) {
        return;
      }

      setIsSelectAll(isSelectAll);

      if (isSelectAll) {
        const originData = data.map((item) => item[0].originData);

        onChangeMultiSelect(originData);
      } else {
        onChangeMultiSelect([]);
      }
    },
    [data, onChangeMultiSelect]
  );

  const handleChangePage = useCallback(() => {
    onChangePage(1, pagination.pageSize + 10);
  }, [pagination, onChangePage]);

  if (typeof window !== "undefined" && window.innerWidth > 768) {
    return null;
  }

  return (
    <TableMobileWrapper className="hide-desktop">
      {description?.length > 0 ? (
        <DescriptionWrapper>
          {description.map((item, index) => (
            <h6 key={index}>
              {item.label} : <span>{item.value}</span>
            </h6>
          ))}
        </DescriptionWrapper>
      ) : null}
      {typeof onChangeMultiSelect === "function" ? (
        <CheckBox
          label="Chọn tất cả"
          className="mb-16"
          middle={isSelectAll && multiSelect.length !== data.length}
          checked={isSelectAll}
          onChange={() => handleSelectAll(!isSelectAll)}
        />
      ) : null}
      {data?.length ? (
        <>
          {data.map((item, index) => (
            <ItemTable
              key={index}
              index={index + 1}
              data={item}
              config={config}
              showCollapse={showCollapse}
              multiSelect={multiSelect}
              onChangeMultiSelect={onChangeMultiSelect}
            />
          ))}
          {data.length !== pagination.totalItem ? (
            !loading ? (
              <FlexBox justifyContent="center">
                <p className="color-primary text-medium pointer" onClick={handleChangePage}>
                  Xem thêm
                </p>
              </FlexBox>
            ) : (
              <LoadingSection loading={loading} />
            )
          ) : null}
        </>
      ) : customEmptyData ? (
        customEmptyData
      ) : loading ? (
        <LoadingSection loading={loading} />
      ) : (
        <ItemTable index={0} data={[]} config={config} />
      )}
    </TableMobileWrapper>
  );
};

export default TableMobileSeverSide;
