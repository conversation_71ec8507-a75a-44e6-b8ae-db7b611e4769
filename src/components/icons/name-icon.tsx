import React from "react";
import { convertNameToCharacter } from "services/untils";

const IconName = ({ name }: { name: string }) => {
  return (
    <svg width="34" height="35" viewBox="0 0 34 35" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g filter="url(#filter0_d_3837_24500)">
        <circle cx="17" cy="15.5" r="12" fill="#E87722" />
        <circle cx="17" cy="15.5" r="12.5" stroke="white" />
      </g>
      <text
        fontFamily="FWD, sans-serif"
        style={{
          fontSize: "10px",
          textAlign: "center",
          transform: "translate(30%,55%)",
        }}
        fill="white"
      >
        {convertNameToCharacter(name ?? "")}
      </text>
      <defs>
        <filter
          id="filter0_d_3837_24500"
          x="0"
          y="0.5"
          width="34"
          height="34"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="2" />
          <feGaussianBlur stdDeviation="2" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3837_24500" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3837_24500" result="shape" />
        </filter>
      </defs>
    </svg>
  );
};

export default IconName;
