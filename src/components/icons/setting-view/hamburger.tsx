import { IconProperty } from "@custom-types";
import React from "react";

const SettingViewHamburger = ({ width = 32, height = 32, fill = "#E87722" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_1914_42712)">
        <path d="M26.6663 12H5.33301V14.6667H26.6663V12ZM5.33301 20H26.6663V17.3333H5.33301V20Z" fill={fill} />
      </g>
      <defs>
        <clipPath id="clip0_1914_42712">
          <rect width="32" height="32" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default SettingViewHamburger;
