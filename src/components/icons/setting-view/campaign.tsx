import { IconProperty } from "@custom-types";
import React from "react";

const SettingViewCampaign = ({ width = 24, height = 24, fill = "#E87722" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M20.7759 19.3228C20.7759 19.9175 20.2917 20.4017 19.697 20.4017H3.47879C2.88366 20.4017 2.3999 19.9175 2.3999 19.3228V4.67948C2.3999 4.08478 2.88366 3.60059 3.47879 3.60059C4.07349 3.60059 4.55768 4.08478 4.55768 4.67948V18.2439H19.697C20.2917 18.2439 20.7759 18.7277 20.7759 19.3228Z"
        fill={fill}
      />
      <path
        d="M20.7943 6.43143V11.4235C20.7943 12.0186 20.3101 12.5024 19.7154 12.5024C19.1202 12.5024 18.6365 12.0186 18.6365 11.4235V9.05122L14.9522 12.7203C14.531 13.1393 13.8476 13.1384 13.4282 12.7186L12.2606 11.5514L7.09976 16.7122C6.89616 16.9158 6.62513 17.0285 6.3367 17.0285C6.04827 17.0285 5.77768 16.9158 5.57365 16.7122C5.37049 16.5086 5.25781 16.2376 5.25781 15.9496C5.25781 15.6612 5.37005 15.3901 5.57408 15.1865L11.498 9.26308C11.9182 8.8424 12.6025 8.8424 13.0232 9.26265L14.1922 10.432L17.1256 7.51032H14.7233C14.1286 7.51032 13.6444 7.02656 13.6444 6.43143C13.6444 5.83673 14.1286 5.35254 14.7233 5.35254H19.7154C20.3101 5.35254 20.7943 5.83673 20.7943 6.43143Z"
        fill={fill}
      />
    </svg>
  );
};

export default SettingViewCampaign;
