import { IconProperty } from "@custom-types";
import React from "react";

const IconCardReject = ({ width = 12, height = 12, fill = "#FED141" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M6 12C9.31371 12 12 9.31371 12 6C12 2.68629 9.31371 0 6 0C2.68629 0 0 2.68629 0 6C0 9.31371 2.68629 12 6 12Z"
        fill={fill}
      />
      <path d="M6 4V6.5L7.5 8" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
};

export default IconCardReject;
