import { IconProperty } from "@custom-types";
import React from "react";

const DownloadIconBorder = ({ width = 32, height = 32, fill = "#FED141" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 34 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="2" y="1" width="30" height="30" rx="3" fill="white" />
      <path
        d="M12.0816 15.85C11.6395 15.4 11.6395 14.6875 12.0816 14.275L12.3395 14.0125L16.2816 17.95V8.5H17.7553V17.95L21.6605 13.975L21.9184 14.2375C22.3605 14.6875 22.3605 15.4 21.9184 15.8125L17.0184 20.8375L12.0816 15.85ZM21.7711 22H12.2289C12.0079 22 11.8605 22.15 11.8605 22.375V23.125C11.8605 23.35 12.0079 23.5 12.2289 23.5H21.7711C21.9921 23.5 22.1395 23.35 22.1395 23.125V22.375C22.1395 22.1875 21.9553 22 21.7711 22Z"
        fill="#E87722"
      />
      <rect x="2" y="1" width="30" height="30" rx="3" stroke="#E87722" strokeWidth="2" />
    </svg>
  );
};

export default DownloadIconBorder;
