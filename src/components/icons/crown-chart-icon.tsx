import { IconProperty } from "@custom-types";
import React from "react";

const CrownChartIcon = ({ width = 34, height = 34, fill = "#F3BB90" }: IconProperty) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} viewBox="0 0 34 34" fill="none">
      <g filter="url(#filter0_d_182_20719)">
        <circle cx={17} cy={15} r={12} fill={fill} />
        <circle cx={17} cy={15} r="12.5" stroke="white" />
      </g>
      <path
        d="M23.1515 11C22.6837 11 22.303 11.3756 22.303 11.8372C22.303 12.0681 22.3982 12.2772 22.5519 12.4289L19.9698 14.9766L17.484 12.5241C17.7041 12.3727 17.8485 12.1216 17.8485 11.8375C17.8485 11.3756 17.4679 11 17 11C16.5321 11 16.1515 11.3756 16.1515 11.8372C16.1515 12.1213 16.2962 12.3725 16.516 12.5238L14.0302 14.9766L11.4484 12.4289C11.6021 12.2772 11.6972 12.0679 11.6972 11.8372C11.697 11.3756 11.3163 11 10.8485 11C10.3806 11 10 11.3756 10 11.8372C10 12.2265 10.271 12.5535 10.6363 12.6469V20H23.3634V12.6469C23.729 12.5535 24 12.2265 24 11.8372C24 11.3756 23.6194 11 23.1515 11Z"
        fill="white"
      />
      <defs>
        <filter
          id="filter0_d_182_20719"
          x={0}
          y={0}
          width={34}
          height={34}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy={2} />
          <feGaussianBlur stdDeviation={2} />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_182_20719" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_182_20719" result="shape" />
        </filter>
      </defs>
    </svg>
  );
};

export default CrownChartIcon;
