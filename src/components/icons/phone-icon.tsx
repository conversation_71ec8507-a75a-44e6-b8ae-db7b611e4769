import { IconProperty } from "@custom-types";
import React from "react";

const PhoneIcon = ({ width = 19, height = 18, fill = "#E87722" }: IconProperty) => {
  return (
    <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_2930_12999)">
        <circle cx="24" cy="24" r="24" fill="#E87722" />
        <path
          d="M32.1816 34.3818C31.3089 35.2749 29.8907 35.7214 28.6907 35.2749C26.2907 34.6051 22.3634 32.9307 18.7634 29.3586C15.1634 25.6749 13.5271 21.7679 12.8725 19.4237C12.5453 18.1958 12.8725 16.8563 13.7453 15.8516L16.5816 13.0609C17.2362 12.3912 18.4362 12.3912 19.0907 13.0609L23.018 16.9679C23.6725 17.6377 23.6725 18.8656 23.018 19.5353L21.3816 21.2098C21.4907 21.8795 21.9271 23.3307 23.3453 24.6702C24.7634 26.1214 26.1816 26.5679 26.8362 26.6795L28.3634 25.1167C29.018 24.447 30.218 24.447 30.8725 25.1167L34.9089 29.0237C35.5634 29.6935 35.5634 30.9214 34.9089 31.5912L32.1816 34.3818ZM15.4907 17.4144C15.0544 17.8609 14.8362 18.5307 15.0544 19.2005C15.5998 21.0981 17.018 24.447 20.2907 27.6842C23.5634 31.033 26.9453 32.3725 28.7998 32.9307C29.4544 33.1539 30.1089 32.9307 30.5453 32.4842L32.7271 30.2516L29.6725 27.126L28.3634 28.4656C27.9271 28.9121 27.3816 29.0237 26.8362 29.0237C25.6362 28.8005 23.4544 28.2423 21.5998 26.3446C19.7453 24.5586 19.1998 22.326 18.9816 21.0981C18.8725 20.54 19.0907 19.9818 19.5271 19.5353L20.8362 18.1958L17.7816 15.0702L15.4907 17.4144Z"
          fill="white"
        />
        <path
          d="M24 48C10.8 48 0 37.2837 0 24C0 10.7163 10.8 0 24 0C37.2 0 48 10.7163 48 24C48 37.2837 37.2 48 24 48ZM24 2.45581C12.1091 2.45581 2.4 12.1674 2.4 24C2.4 35.9442 12.1091 45.5442 24 45.5442C35.8909 45.5442 45.6 35.8326 45.6 24C45.6 12.0558 35.8909 2.45581 24 2.45581Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_2930_12999">
          <rect width="48" height="48" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default PhoneIcon;
