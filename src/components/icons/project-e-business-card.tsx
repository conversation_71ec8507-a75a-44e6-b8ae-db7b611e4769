import { IconProperty } from "@custom-types";
import React from "react";

const ProjectEBusinessCard = ({ width = 50, height = 50, fill = "#E87722", stroke = "#F3BB91" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M44.8218 42.3333C44.8218 43.7011 43.7012 44.8217 42.3334 44.8217H7.48838C6.12054 44.8217 5 43.7011 5 42.3333V19.5167C5 18.1489 6.12054 17.0283 7.48838 17.0283H42.3295C43.6974 17.0283 44.8179 18.1489 44.8179 19.5167V42.3333H44.8218Z"
        fill={fill}
      />
      <path
        d="M41.9159 40.6714C41.9159 41.3553 41.3557 41.9156 40.6717 41.9156H9.14971C8.46579 41.9156 7.90552 41.3553 7.90552 40.6714V21.1778C7.90552 20.4939 8.46579 19.9336 9.14971 19.9336H40.6756C41.3595 19.9336 41.9198 20.4939 41.9198 21.1778V40.6714H41.9159Z"
        fill="white"
      />
      <path
        d="M37.976 29.6133H28.0225C27.6786 29.6133 27.4004 29.3351 27.4004 28.9912C27.4004 28.6473 27.6786 28.3691 28.0225 28.3691H37.976C38.3199 28.3691 38.5981 28.6473 38.5981 28.9912C38.5981 29.3351 38.3199 29.6133 37.976 29.6133Z"
        fill={stroke}
      />
      <path
        d="M37.976 25.3594H28.0225C27.6786 25.3594 27.4004 25.0812 27.4004 24.7373C27.4004 24.3934 27.6786 24.1152 28.0225 24.1152H37.976C38.3199 24.1152 38.5981 24.3934 38.5981 24.7373C38.5981 25.0812 38.3199 25.3594 37.976 25.3594Z"
        fill={stroke}
      />
      <path
        d="M37.976 33.6778H28.0225C27.6786 33.6778 27.4004 33.3996 27.4004 33.0557C27.4004 32.7118 27.6786 32.4336 28.0225 32.4336H37.976C38.3199 32.4336 38.5981 32.7118 38.5981 33.0557C38.5981 33.3996 38.3199 33.6778 37.976 33.6778Z"
        fill={stroke}
      />
      <path
        d="M32.5858 37.7696H28.0225C27.6786 37.7696 27.4004 37.4914 27.4004 37.1475C27.4004 36.8036 27.6786 36.5254 28.0225 36.5254H32.5858C32.9297 36.5254 33.2079 36.8036 33.2079 37.1475C33.2079 37.4914 32.9297 37.7696 32.5858 37.7696Z"
        fill={stroke}
      />
      <path
        d="M34.0608 17.6085C34.5051 18.6595 33.9333 19.5211 32.7934 19.5211H17.0285C15.8886 19.5211 15.3168 18.6595 15.7611 17.6085L17.469 13.5513C17.9134 12.5003 19.2078 11.6387 20.3476 11.6387H29.4743C30.6141 11.6387 31.9085 12.5003 32.3529 13.5513L34.0608 17.6085Z"
        fill={fill}
      />
      <path
        d="M29.8881 15.1618C29.8881 15.9617 29.1423 16.6147 28.2305 16.6147H21.5922C20.6803 16.6147 19.9346 15.9617 19.9346 15.1618C19.9346 14.362 20.6803 13.709 21.5922 13.709H28.2305C29.1423 13.709 29.8881 14.362 29.8881 15.1618Z"
        fill="white"
      />
      <path d="M26.9862 5H22.8363L21.1787 7.07494V14.9535H28.6438V7.07494L26.9862 5Z" fill="#183028" />
      <path
        d="M24.9108 9.9764C25.8263 9.9764 26.5684 9.23425 26.5684 8.31876C26.5684 7.40328 25.8263 6.66113 24.9108 6.66113C23.9953 6.66113 23.2532 7.40328 23.2532 8.31876C23.2532 9.23425 23.9953 9.9764 24.9108 9.9764Z"
        fill="white"
      />
      <path
        d="M22.1328 32.9089C22.0478 32.8896 21.9667 32.8664 21.8855 32.8393L19.088 31.8115H15.4559C14.8145 32.0665 13.2535 32.6577 12.7782 32.8393C12.6855 32.8741 12.5889 32.8973 12.4884 32.9243C11.6654 33.1291 11.0742 33.8749 11.0742 34.7365V35.9769C11.0742 36.8231 11.762 37.5108 12.6082 37.5108H22.0246C22.8708 37.5108 23.5586 36.8231 23.5586 35.9769V34.7288C23.5586 33.8556 22.9636 33.1098 22.1328 32.9089Z"
        fill={fill}
      />
      <path
        d="M17.2263 32.2211C15.2402 32.2211 13.6135 30.5944 13.6135 28.6083V28.1099C13.6135 26.1238 15.2402 24.4971 17.2263 24.4971C19.2124 24.4971 20.8391 26.1238 20.8391 28.1099V28.6083C20.8352 30.5982 19.2124 32.2211 17.2263 32.2211Z"
        fill={stroke}
      />
    </svg>
  );
};

export default ProjectEBusinessCard;
