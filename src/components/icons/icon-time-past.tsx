import { IconProperty } from "@custom-types";
import * as React from "react";

function IconTimePast({ width = 14, height = 14, fill = "#8B8E8F" }: IconProperty) {
  return (
    <svg width={width} height={height} viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M7.489 1.75C4.58043 1.75 2.99968 3.58367 2.43061 5.00634V3.33075H2.11446C1.57701 3.33075 1.16602 3.74174 1.16602 4.27919V7.12454H4.01136C4.54881 7.12454 4.95981 6.71354 4.95981 6.17609V5.85994H3.4739C3.69521 5.06957 4.54881 3.55205 6.41409 3.14106C8.88005 2.57199 11.3776 4.27919 11.5673 6.77677C11.757 9.21112 9.86012 11.2345 7.45738 11.2345C5.71856 11.2345 4.16943 10.128 3.56875 8.48398C3.50552 8.3259 3.34744 8.26267 3.18937 8.29429L2.58869 8.45236C2.399 8.51559 2.30415 8.70528 2.36738 8.86336C3.2526 11.3925 5.93987 13.0049 8.72198 12.3094C10.8402 11.8035 12.4842 10.0015 12.7687 7.82006C13.2745 4.59534 10.7137 1.75 7.489 1.75Z"
        fill={fill}
      />
      <path
        d="M6.54102 4.59534V7.75683H8.75406C9.29151 7.75683 9.70251 7.34583 9.70251 6.80838V6.49223H7.80561V5.54378C7.80561 5.00633 7.39462 4.59534 6.85716 4.59534H6.54102Z"
        fill={fill}
      />
    </svg>
  );
}

export default IconTimePast;
