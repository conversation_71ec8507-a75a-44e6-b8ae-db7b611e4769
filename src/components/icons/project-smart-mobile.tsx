import { IconProperty } from "@custom-types";
import React from "react";

const ProjectSmartMobile = ({ width = 48, height = 48, fill = "#E87722", stroke = "#F3BB91" }: IconProperty) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} viewBox="0 0 48 48" fill="none">
      <circle cx={24} cy={24} r={23} fill={fill} stroke="#F3BB90" strokeWidth={2} />
      <path
        d="M24.4602 12.4531H11.3916C10.8438 12.4531 10.4004 12.8966 10.4004 13.4444V34.9644C10.4004 35.5122 10.8438 35.9557 11.3916 35.9557H28.0338C28.5816 35.9557 29.025 35.5122 29.025 34.9644V13.8095C29.025 13.0792 28.4251 12.4531 27.6686 12.4531H24.4602Z"
        fill="white"
      />
      <path
        d="M24.3013 20.5405H15.1194C14.8324 20.5405 14.5977 20.3057 14.5977 20.0188C14.5977 19.7318 14.8324 19.4971 15.1194 19.4971H24.3013C24.5882 19.4971 24.823 19.7318 24.823 20.0188C24.849 20.3057 24.6143 20.5405 24.3013 20.5405Z"
        fill={fill}
      />
      <path
        d="M24.3013 23.2529H15.1194C14.8324 23.2529 14.5977 23.0181 14.5977 22.7312C14.5977 22.4442 14.8324 22.2095 15.1194 22.2095H24.3013C24.5882 22.2095 24.823 22.4442 24.823 22.7312C24.849 23.0181 24.6143 23.2529 24.3013 23.2529Z"
        fill={fill}
      />
      <path
        d="M24.3013 25.9912H15.1194C14.8324 25.9912 14.5977 25.7564 14.5977 25.4695C14.5977 25.1825 14.8324 24.9478 15.1194 24.9478H24.3013C24.5882 24.9478 24.823 25.1825 24.823 25.4695C24.849 25.7564 24.6143 25.9912 24.3013 25.9912Z"
        fill={fill}
      />
      <path d="M29.3779 21.3582L28.5293 22.2065L32.0702 25.7484L32.9187 24.9001L29.3779 21.3582Z" fill={fill} />
      <path
        d="M37.0313 17.2261C38.0225 18.2173 37.6834 20.1215 36.3009 21.504L32.9099 24.895L29.3623 21.3475L32.7794 17.9304C34.1359 16.5739 36.0661 16.2348 37.0313 17.2261Z"
        fill={stroke}
      />
      <path
        d="M30.9044 17.8781C31.4522 17.826 32 17.9825 32.3912 18.3477L32.7825 17.9564C32.8869 17.8521 32.9912 17.7477 33.0955 17.6695C32.4956 17.0956 31.6609 16.8347 30.8262 16.913C28.6089 17.1478 27.8003 19.078 27.0438 20.7736C26.5743 21.8431 26.1569 22.8343 25.4527 23.4342C25.0092 23.8255 24.2788 23.7212 23.7571 23.5125C23.4963 23.4082 23.2354 23.5386 23.1311 23.7733C23.0267 24.0081 23.1572 24.295 23.3919 24.3994C23.8093 24.582 24.2527 24.6602 24.644 24.6602C25.1918 24.6602 25.6874 24.5037 26.0787 24.1646C26.9656 23.4342 27.4612 22.2604 27.9568 21.1388C28.6089 19.5737 29.2611 18.0607 30.9044 17.8781Z"
        fill="#183028"
      />
      <path d="M22.7109 29.1978L25.0586 31.5454L32.0494 25.7546L28.5018 22.207L22.7109 29.1978Z" fill={stroke} />
      <path d="M20.9922 32.2772L21.9834 33.2685L25.0614 31.5469L22.7138 29.1992L20.9922 32.2772Z" fill={stroke} />
      <path
        d="M21.2526 32.5376L20.5744 33.2158C20.444 33.3462 20.444 33.5549 20.5744 33.6853C20.6526 33.7636 20.7309 33.7897 20.8091 33.7897C20.8874 33.7897 20.9917 33.7636 21.0439 33.6853L21.7221 33.0071L21.2526 32.5376Z"
        fill="#183028"
      />
    </svg>
  );
};

export default ProjectSmartMobile;
