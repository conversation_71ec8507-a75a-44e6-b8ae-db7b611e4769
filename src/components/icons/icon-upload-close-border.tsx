import { IconProperty } from "@custom-types";

const IconCloseBorder = ({ width = 16, height = 16, fill = "#183028" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.99935 15.3334C12.0494 15.3334 15.3327 12.0502 15.3327 8.00008C15.3327 3.94999 12.0494 0.666748 7.99935 0.666748C3.94926 0.666748 0.666016 3.94999 0.666016 8.00008C0.666016 12.0502 3.94926 15.3334 7.99935 15.3334Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.4229 5.55636L8.99741 7.98186L11.4229 10.4074L11.1637 10.6666C10.7343 11.096 10.0376 11.096 9.60828 10.6666L7.96048 9.01879L6.31268 10.6666C5.88331 11.096 5.18664 11.096 4.75728 10.6666L4.49804 10.4074L6.92354 7.98186L4.49804 5.55636L4.75728 5.29712C5.18664 4.86776 5.88331 4.86776 6.31268 5.29712L7.96048 6.94492L9.60828 5.29712C10.0376 4.86776 10.7343 4.86776 11.1637 5.29712L11.4229 5.55636Z"
        fill="white"
      />
    </svg>
  );
};

export default IconCloseBorder;
