import { IconProperty } from "@custom-types";
import * as React from "react";

function IconImage({ width = 24, height = 24, fill = "#E87722" }: IconProperty) {
  return (
    <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M19.5 21H4.5C3.67 21 3 20.33 3 19.5V4.5C3 3.67 3.67 3 4.5 3H19.5C20.33 3 21 3.67 21 4.5V19.5C21 20.33 20.33 21 19.5 21ZM5 19H19V5H5V19Z"
        fill={fill}
      />
      <path
        d="M8.975 5.99426H6.5C6.225 5.99426 6 6.21926 6 6.49426V8.88426C7.635 8.92926 8.97 7.61426 8.97 5.98926L8.975 5.99426Z"
        fill={fill}
      />
      <path d="M6 12.15V10.15C8.29 10.15 10.15 8.29 10.15 6H12.15C12.15 9.39 9.39 12.15 6 12.15Z" fill={fill} />
      <path
        d="M9.35013 14.3496L10.5901 15.5896C10.8051 15.8046 11.1601 15.7796 11.3451 15.5346L13.6101 12.5096C13.8051 12.2496 14.1951 12.2396 14.4001 12.4996L17.3451 16.1846C17.6051 16.5096 17.3751 16.9946 16.9551 16.9946H7.20513C6.76013 16.9946 6.53513 16.4546 6.85013 16.1396L8.64513 14.3446C8.84013 14.1496 9.15513 14.1496 9.35013 14.3446V14.3496Z"
        fill={fill}
      />
    </svg>
  );
}

export default IconImage;
