import { IconProperty } from "@custom-types";
import React from "react";

const FYPIcon = ({ width = 40, height = 40 }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="24" cy="24" r="24" fill="#FFF4EE" />
      <g clipPath="url(#clip0_10110_96806)">
        <path
          d="M33.4782 32.0452C33.4782 32.0151 33.4782 32.0151 33.4782 31.9849V24.9346C33.4782 22.5242 32.5647 20.1138 30.7671 18.2457C28.9696 16.4078 26.6121 15.5039 24.2252 15.5039C21.8678 15.5039 19.5104 16.4379 17.6833 18.2759C15.8858 20.1138 14.9723 22.5242 14.9723 24.9647V32.0151C14.9723 32.0452 14.9723 32.0452 14.9723 32.0753L13.5578 35.721C13.1453 36.7756 13.9114 37.9205 15.0018 37.9205H33.4782C34.5979 37.9205 35.3346 36.7756 34.9221 35.721L33.4782 32.0452Z"
          fill="#E87722"
        />
        <path
          d="M26.4943 13.9365H21.9562C21.6026 13.9365 21.3374 14.2378 21.3374 14.5693V15.3225C21.3374 15.6841 21.6321 15.9552 21.9562 15.9552H26.4943C26.8479 15.9552 27.1131 15.6539 27.1131 15.3225V14.5693C27.1426 14.2077 26.8479 13.9365 26.4943 13.9365Z"
          fill="#183028"
        />
        <path
          d="M27.3194 13.7259L29.3232 11.6771C29.5884 11.4059 29.5884 10.9841 29.3232 10.7431L28.8517 10.261C28.5865 9.98982 28.174 9.98982 27.9382 10.261L27.4668 10.7431C27.2015 11.0142 26.789 11.0142 26.5532 10.7431L26.0818 10.261C25.8165 9.98982 25.404 9.98982 25.1683 10.261L24.6968 10.7431C24.4316 11.0142 24.019 11.0142 23.7833 10.7431L23.3118 10.261C23.0466 9.98982 22.634 9.98982 22.3983 10.261L21.9268 10.7431C21.6616 11.0142 21.249 11.0142 21.0133 10.7431L20.5418 10.261C20.2766 9.98982 19.864 9.98982 19.6283 10.261L19.1568 10.7431C18.8916 11.0142 18.8916 11.4361 19.1568 11.6771L21.1606 13.7259C21.2785 13.8464 21.4553 13.9067 21.6321 13.9067H26.8774C27.0247 13.9368 27.2015 13.8464 27.3194 13.7259Z"
          fill="#F3BB91"
        />
        <path
          d="M19.864 26.7122C19.864 24.2415 21.8089 22.2529 24.2253 22.2529C26.6416 22.2529 28.5865 24.2415 28.5865 26.7122C28.5865 29.1828 26.6416 31.1714 24.2253 31.1714C21.8384 31.1714 19.864 29.1527 19.864 26.7122Z"
          fill="white"
        />
        <path
          d="M24.0189 29.303V28.6401C23.8126 28.61 23.6358 28.5497 23.459 28.4593C23.3116 28.3689 23.1643 28.2786 23.0759 28.158C22.958 28.0375 22.8991 27.917 22.8402 27.7664C22.7812 27.6157 22.7518 27.4952 22.7223 27.3445L23.4885 27.1638C23.4885 27.2541 23.5179 27.3445 23.5474 27.4349C23.5769 27.5253 23.6358 27.6157 23.7242 27.676C23.7831 27.7362 23.8715 27.7965 23.9894 27.8567C24.0778 27.8869 24.2252 27.917 24.343 27.917C24.5493 27.917 24.6966 27.8869 24.8145 27.7965C24.9324 27.7061 24.9913 27.5856 24.9913 27.4651C24.9913 27.3445 24.9619 27.2541 24.8734 27.1939C24.785 27.1035 24.6966 27.0734 24.5198 27.0131L23.9599 26.8926C23.6358 26.8323 23.3706 26.6817 23.1643 26.4708C22.958 26.2599 22.8696 26.0188 22.8696 25.7175C22.8696 25.5367 22.8991 25.3861 22.958 25.2656C23.017 25.1149 23.1054 24.9944 23.1938 24.904C23.3116 24.7835 23.4295 24.6931 23.5474 24.6328C23.6947 24.5726 23.8421 24.5123 23.9894 24.4822V23.8193H24.6377V24.5123C24.8145 24.5425 24.9619 24.6027 25.0797 24.663C25.1976 24.7232 25.3155 24.8136 25.4039 24.9341C25.4923 25.0245 25.5512 25.1451 25.6101 25.2354C25.6691 25.356 25.6986 25.4463 25.728 25.5669L24.9913 25.7476C24.9913 25.6874 24.9619 25.6271 24.9324 25.5669C24.9029 25.5066 24.8734 25.4463 24.8145 25.3861C24.7556 25.3258 24.6966 25.2656 24.6082 25.2354C24.5198 25.2053 24.4314 25.1752 24.2841 25.1752C24.0778 25.1752 23.9305 25.2354 23.8421 25.3258C23.7242 25.4162 23.6947 25.5367 23.6947 25.6573C23.6947 25.7476 23.7242 25.838 23.8126 25.9284C23.8715 25.9887 23.9894 26.0489 24.1368 26.0791L24.6966 26.1996C25.0797 26.29 25.3744 26.4406 25.5512 26.6515C25.728 26.8625 25.8164 27.1336 25.8164 27.4048C25.8164 27.5554 25.787 27.676 25.7575 27.8266C25.6986 27.9471 25.6396 28.0676 25.5512 28.1882C25.4628 28.3087 25.3449 28.3991 25.1976 28.4593C25.0503 28.5497 24.9029 28.5799 24.6966 28.61V29.2728H24.0189V29.303Z"
          fill="#183028"
        />
      </g>
      <defs>
        <clipPath id="clip0_10110_96806">
          <rect width="21.6" height="27.84" fill="white" transform="translate(13.4399 10.0801)" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default FYPIcon;
