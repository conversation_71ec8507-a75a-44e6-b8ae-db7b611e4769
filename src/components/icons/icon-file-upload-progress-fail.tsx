import { IconProperty } from "@custom-types";

const IconFileUploadProgressFail = ({ width = 24, height = 24, fill = "#E87722" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12.0042 21C10.5743 21 9.17922 20.6342 7.91727 19.9387C7.61625 19.7708 7.50626 19.381 7.6915 19.0812L8.30511 18.0739C8.46141 17.816 8.79137 17.7082 9.05186 17.8521C9.96071 18.3498 10.9738 18.6076 11.9984 18.6076C15.5122 18.6076 18.3718 15.6456 18.3718 12.006H20.6873C20.6873 16.9587 16.7915 21 12.0042 21Z"
        fill={fill}
      />
      <path
        d="M5.63068 12H3.31516C3.31516 7.03531 7.21102 3 12.0041 3C13.434 3 14.8349 3.36577 16.091 4.0613C16.392 4.22919 16.502 4.61889 16.3168 4.91869L15.7032 5.92598C15.5469 6.1838 15.2169 6.2918 14.9564 6.1479C14.0476 5.65023 13.0345 5.3924 12.0099 5.3924C8.49034 5.39839 5.63068 8.36043 5.63068 12Z"
        fill={fill}
      />
      <path
        d="M22.4181 12H16.6293C16.1546 12 15.8825 11.4363 16.1662 11.0406L19.0664 7.04132C19.2979 6.72353 19.761 6.72353 19.9926 7.04132L22.8812 11.0406C23.1706 11.4363 22.8985 12 22.4181 12Z"
        fill={fill}
      />
      <path
        d="M4.00391 16.9527L1.11531 12.9533C0.831661 12.5575 1.10374 11.994 1.57842 11.994H7.36721C7.84189 11.994 8.11396 12.5575 7.83031 12.9533L4.93013 16.9527C4.69857 17.2765 4.23547 17.2765 4.00391 16.9527Z"
        fill={fill}
      />
    </svg>
  );
};

export default IconFileUploadProgressFail;
