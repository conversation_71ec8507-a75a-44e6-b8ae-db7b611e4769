import { IconProperty } from "@custom-types";
import React from "react";

const IconConsider = ({ width = 14, height = 15, fill = "#FED141" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M9.43106 13.3333H4.56995C4.11085 13.3333 3.75977 12.9542 3.75977 12.4583V10.1833L6.24433 7.49999L3.75977 4.81666V2.54166C3.75977 2.04582 4.11085 1.66666 4.56995 1.66666H9.43106C9.89017 1.66666 10.2412 2.04582 10.2412 2.54166V4.81666L7.75668 7.49999L10.2412 10.1833V12.4583C10.2412 12.9542 9.89017 13.3333 9.43106 13.3333ZM4.84001 12.1667H9.161V10.65L7.00051 8.31666L4.84001 10.65V12.1667ZM4.84001 4.34999L7.00051 6.68332L9.161 4.34999V2.83332H4.84001V4.34999Z"
        fill="#FED141"
      />
    </svg>
  );
};

export default IconConsider;
