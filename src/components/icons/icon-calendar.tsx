import { IconProperty } from "@custom-types";
import React from "react";

const IconCalendar = ({ width = 24, height = 24, fill = "#E87722" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M18.2235 4.07407V2.51852C18.2235 2.23333 17.9902 2 17.705 2H16.668C16.3828 2 16.1495 2.23333 16.1495 2.51852V4.07407H7.31908V2.51852C7.31908 2.23333 7.08575 2 6.80056 2H5.76352C5.47834 2 5.24501 2.23333 5.24501 2.51852V4.07407H4.22352C3.36278 4.07407 2.66797 4.76889 2.66797 5.62963V20.1481C2.66797 21.0089 3.36278 21.7037 4.22352 21.7037H19.7791C20.6398 21.7037 21.3346 21.0089 21.3346 20.1481V5.62963C21.3346 4.76889 20.6398 4.07407 19.7791 4.07407H18.2235ZM19.2606 8.22222H4.74204V6.14815H19.2606V8.22222ZM4.74204 10.2963H19.2606V19.6296H4.74204V10.2963Z"
        fill={fill}
      />
      <path
        d="M13.5576 13.4082H16.6687C16.9539 13.4082 17.1872 13.6415 17.1872 13.9267V16.0008C17.1872 16.8615 16.4924 17.5564 15.6317 17.5564H13.5576C13.2724 17.5564 13.0391 17.323 13.0391 17.0378V13.9267C13.0391 13.6364 13.2672 13.4082 13.5576 13.4082Z"
        fill={fill}
      />
    </svg>
  );
};

export default IconCalendar;
