import { IconProperty } from "@custom-types";
import React from "react";

const IconPolicyConfirm = ({ width = 24, height = 24, fill = "#F3BB90" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M19.5415 21.5812H4.50423C3.67189 21.5812 3 20.9093 3 20.0769V6.03743C3 5.20509 3.67189 4.5332 4.50423 4.5332H19.5466C20.3789 4.5332 21.0508 5.20509 21.0508 6.03743V20.0769C21.0508 20.9093 20.3789 21.5812 19.5466 21.5812H19.5415ZM5.00063 19.5755H19.0401V6.53885H5.00564V19.5755H5.00063Z"
        fill={fill}
      />
      <path
        d="M9.0196 5.63107C9.00957 5.50071 8.91932 4.33744 9.72659 3.44493C10.0976 3.03377 10.7996 2.53737 12.013 2.52734H12.0481C13.2365 2.52734 13.9284 3.0087 14.2995 3.40983C15.1218 4.30735 15.0365 5.49569 15.0215 5.62606L13.0259 5.44054C13.0259 5.44054 13.0459 5.00431 12.8153 4.75862C12.6699 4.60318 12.4091 4.52296 12.0281 4.52797C11.642 4.52797 11.3712 4.61321 11.2208 4.77366C10.9952 5.01935 11.0152 5.44555 11.0152 5.45057L9.0196 5.62606V5.63107Z"
        fill={fill}
      />
      <path
        d="M9.70867 17.333L7.95373 15.8037C7.32697 15.2572 7.26179 14.3095 7.80832 13.6827C7.98883 13.4721 8.30472 13.4521 8.51531 13.6326L9.60337 14.5853C9.80394 14.7608 10.1048 14.7457 10.2903 14.5602L14.6074 10.1829C15.1891 9.59123 16.1418 9.58621 16.7334 10.1678C16.929 10.3634 16.934 10.6793 16.7334 10.8748L10.3856 17.313C10.2001 17.5035 9.8992 17.5135 9.69864 17.338L9.70867 17.333Z"
        fill={fill}
      />
    </svg>
  );
};

export default IconPolicyConfirm;
