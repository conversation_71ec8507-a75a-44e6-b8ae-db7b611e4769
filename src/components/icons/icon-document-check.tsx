import { IconProperty } from "@custom-types";
import * as React from "react";

function IconDocumentCheck({ width = 24, height = 24, fill = "#183028" }: IconProperty) {
  return (
    <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M21 15.001L15.285 20.701L12.5778 18.001L12.9288 17.651C13.5303 17.051 14.4828 17.051 15.0343 17.651L15.285 17.901L18.5435 14.651C19.1451 14.051 20.0976 14.051 20.6491 14.651L21 15.001ZM11.4749 20.001H6.96306V7.00098H15.9868V12.501C15.9868 12.801 16.1873 13.001 16.4881 13.001H17.4908C17.7916 13.001 17.9921 12.801 17.9921 12.501V6.50098C17.9921 5.65098 17.3404 5.00098 16.4881 5.00098H6.46174C5.6095 5.00098 4.95778 5.65098 4.95778 6.50098V20.501C4.95778 21.351 5.6095 22.001 6.46174 22.001H11.4749C11.7757 22.001 11.9763 21.801 11.9763 21.501V20.501C11.9763 20.251 11.7757 20.001 11.4749 20.001ZM14.0317 3.50098C14.0317 2.65098 13.3799 2.00098 12.5277 2.00098H3.50396C2.65171 2.00098 2 2.65098 2 3.50098V16.501C2 17.351 2.65171 18.001 3.50396 18.001H4.00528V4.00098H14.0317V3.50098ZM8.96834 9.00098C8.66755 9.00098 8.46702 9.20098 8.46702 9.50098V10.501C8.46702 10.801 8.66755 11.001 8.96834 11.001H13.9815C14.2823 11.001 14.4828 10.801 14.4828 10.501V9.50098C14.4828 9.20098 14.2823 9.00098 13.9815 9.00098H8.96834ZM12.4776 13.001H8.96834C8.66755 13.001 8.46702 13.201 8.46702 13.501V14.501C8.46702 14.801 8.66755 15.001 8.96834 15.001H10.9736C11.8259 15.001 12.4776 14.351 12.4776 13.501V13.001Z"
        fill={fill}
      />
    </svg>
  );
}

export default IconDocumentCheck;
