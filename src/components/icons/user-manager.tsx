import { IconProperty } from "@custom-types";
import React from "react";

const IconUserManager = ({ width = 14, height = 14, fill = "#FED141" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M7.00065 6.99993C5.39648 6.99993 4.08398 5.68743 4.08398 4.08326C4.08398 2.4791 5.39648 1.1666 7.00065 1.1666C8.60482 1.1666 9.91732 2.4791 9.91732 4.08326C9.91732 5.68743 8.63398 6.99993 7.00065 6.99993ZM7.00065 2.33326C6.03815 2.33326 5.25065 3.12076 5.25065 4.08326C5.25065 5.04576 6.03815 5.83326 7.00065 5.83326C7.96315 5.83326 8.75065 5.04576 8.75065 4.08326C8.75065 3.12076 7.99232 2.33326 7.00065 2.33326Z"
        fill={fill}
      />
      <path
        d="M12.25 11.6666H11.9583C11.4625 11.6666 11.0833 11.2874 11.0833 10.7916V9.9166C11.0833 8.31243 9.77083 6.99993 8.16667 6.99993H5.83333C4.22917 6.99993 2.91667 8.31243 2.91667 9.9166V10.7916C2.91667 11.2874 2.5375 11.6666 2.04167 11.6666H1.75V9.9166C1.75 7.67077 3.5875 5.83327 5.83333 5.83327H8.16667C10.4125 5.83327 12.25 7.67077 12.25 9.9166V11.6666Z"
        fill={fill}
      />
      <path
        d="M6.99987 13.0958L5.3082 11.4041C5.24987 11.3458 5.2207 11.2291 5.2207 11.1125L6.4457 6.29996L7.5832 6.59162L6.50404 10.9083L7.02904 11.4333L7.52487 10.9375L7.0582 9.56662L7.3207 9.47912C7.78737 9.33329 8.2832 9.56663 8.42904 10.0333L8.77904 11.1125C8.8082 11.2291 8.77904 11.3458 8.7207 11.4041L6.99987 13.0958Z"
        fill={fill}
      />
    </svg>
  );
};

export default IconUserManager;
