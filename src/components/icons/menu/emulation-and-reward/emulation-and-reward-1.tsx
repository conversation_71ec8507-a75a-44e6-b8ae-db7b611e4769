import { IconProperty } from "@custom-types";
import React from "react";

const MenuEmulationAndReward1 = ({ width = 32, height = 32, fill = "#F3BB90" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M21.9854 25.2891H10.0091C9.64164 25.2891 9.34375 25.587 9.34375 25.9544V27.2851C9.34375 27.6526 9.64164 27.9505 10.0091 27.9505H21.9854C22.3529 27.9505 22.6508 27.6526 22.6508 27.2851V25.9544C22.6508 25.587 22.3529 25.2891 21.9854 25.2891Z"
        fill={fill}
      />
      <path d="M17.324 20.1914H14.6626V26.3858H17.324V20.1914Z" fill={fill} />
      <path
        d="M15.9973 21.2972C11.0737 21.2972 9.47682 16.5865 9.41029 16.3869L9.34375 16.1873V4.6634C9.34375 4.29745 9.64316 3.99805 10.0091 3.99805H21.9854C22.3513 3.99805 22.6508 4.29745 22.6508 4.6634V16.1873L22.5842 16.3869C22.5177 16.5865 20.9142 21.2972 15.9973 21.2972ZM12.0052 15.7282C12.2779 16.3802 13.4024 18.6358 15.9973 18.6358C18.5921 18.6358 19.7099 16.3935 19.9894 15.7282V6.65945H12.0052V15.7282Z"
        fill={fill}
      />
      <path
        d="M22.1693 17.3038V14.6424C22.9278 14.6424 23.5 14.4095 23.9657 13.9039C24.9704 12.826 25.2698 10.7701 25.323 9.3196H22.1626V6.6582H27.2525C27.5985 6.6582 27.8913 6.92434 27.9179 7.27033L27.9645 7.88245C28.0044 8.41473 28.2971 13.1321 25.9218 15.7003C24.9571 16.7449 23.6596 17.2972 22.1693 17.2972V17.3038Z"
        fill={fill}
      />
      <path
        d="M10.0099 17.3046C8.46626 17.3046 7.12226 16.7391 6.12423 15.6612C3.72232 13.0796 3.98846 8.41552 4.02838 7.88989L4.07495 7.27112C4.10157 6.92514 4.38767 6.65234 4.7403 6.65234H10.0099V9.31374H6.67647C6.74301 10.7442 7.06238 12.7603 8.07371 13.8448C8.57272 14.3837 9.1915 14.6365 10.0032 14.6365V17.2979L10.0099 17.3046Z"
        fill={fill}
      />
    </svg>
  );
};

export default MenuEmulationAndReward1;
