import { IconProperty } from "@custom-types";
import React from "react";

const MenuContractSolve3 = ({ width = 32, height = 32, fill = "#F3BB90" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M16.506 27.5733C16.2327 27.92 15.706 27.9133 15.446 27.5599L14.9194 26.8466C14.8594 26.7733 13.2527 24.7266 9.33268 24.7266C6.95268 24.7266 5.73272 24.8999 5.12606 25.0399C4.33939 25.2266 3.5127 24.9333 3.01937 24.2333C2.7727 23.8933 2.66602 23.4733 2.66602 23.0533V6.2266L2.986 5.85331C3.23934 5.55998 4.01268 4.66663 9.33268 4.66663C12.686 4.66663 14.846 5.91324 16.0127 6.86658C17.1994 5.91991 19.386 4.66663 22.666 4.66663C26.006 4.66663 28.1727 5.04664 28.9194 5.75997L29.3327 6.15328V23.0599C29.3327 23.4799 29.2193 23.9 28.9793 24.24C28.4793 24.9333 27.6594 25.2266 26.8727 25.0399C26.2661 24.8933 25.0393 24.7199 22.666 24.7199C18.7727 24.7199 17.1327 26.7733 17.0593 26.86L16.506 27.5733ZM9.32601 22.0666C12.6727 22.0666 14.8327 23.2799 15.9993 24.2133C17.1727 23.2799 19.3327 22.0666 22.6593 22.0666C24.2927 22.0666 25.6327 22.1466 26.6593 22.2999V7.64661C26.0393 7.50661 24.8393 7.33997 22.6593 7.33997C18.8593 7.33997 17.1127 9.45993 17.0394 9.55326L15.9394 10.9266L14.906 9.50663C14.846 9.42663 13.226 7.33997 9.31934 7.33997C7.11267 7.33997 5.926 7.49993 5.31934 7.63326V22.2933C6.346 22.1399 7.68601 22.0666 9.32601 22.0666Z"
        fill={fill}
      />
      <path
        d="M17.6526 13.1799L17.046 11.9799C16.866 11.6199 17.0393 11.1866 17.4193 11.0466C18.5793 10.6332 21.146 9.97993 24.846 10.4199C25.2193 10.4666 25.4727 10.8133 25.4193 11.1799L25.2194 12.4999C25.166 12.8599 24.8327 13.0999 24.4727 13.0599C21.446 12.7066 19.366 13.2066 18.446 13.5133C18.1393 13.6133 17.7993 13.4733 17.6526 13.1799Z"
        fill={fill}
      />
      <path
        d="M14.4385 13.1799L15.0451 11.9799C15.2251 11.6199 15.0519 11.1866 14.6719 11.0466C13.5119 10.6332 10.9452 9.97993 7.24518 10.4199C6.87184 10.4666 6.61852 10.8133 6.67185 11.1799L6.8718 12.4999C6.92514 12.8599 7.25847 13.0999 7.61847 13.0599C10.6451 12.7066 12.7252 13.2066 13.6452 13.5133C13.9518 13.6133 14.2919 13.4733 14.4385 13.1799Z"
        fill={fill}
      />
      <path
        d="M17.6526 16.6933L17.046 15.4933C16.866 15.1333 17.0393 14.6999 17.4193 14.5666C18.5793 14.1532 21.146 13.5 24.846 13.94C25.2193 13.9866 25.4727 14.3332 25.4193 14.6999L25.2194 16.0199C25.166 16.3799 24.8327 16.6199 24.4727 16.5799C21.446 16.2266 19.366 16.7265 18.446 17.0332C18.1393 17.1265 17.7993 16.9866 17.6526 16.6933Z"
        fill={fill}
      />
      <path
        d="M14.4385 16.6933L15.0451 15.4933C15.2251 15.1333 15.0519 14.6999 14.6719 14.5666C13.5119 14.1532 10.9452 13.5 7.24518 13.94C6.87184 13.9866 6.61852 14.3332 6.67185 14.6999L6.8718 16.0199C6.92514 16.3799 7.25847 16.6199 7.61847 16.5799C10.6451 16.2266 12.7252 16.7265 13.6452 17.0332C13.9518 17.1265 14.2919 16.9866 14.4385 16.6933Z"
        fill={fill}
      />
      <path
        d="M17.6526 20.72L17.046 19.52C16.866 19.16 17.0393 18.7267 17.4193 18.5867C18.5793 18.1733 21.146 17.52 24.846 17.96C25.2193 18.0066 25.4727 18.3533 25.4193 18.72L25.2194 20.04C25.166 20.4 24.8327 20.64 24.4727 20.6C21.446 20.2467 19.366 20.7466 18.446 21.0533C18.1393 21.16 17.7993 21.0133 17.6526 20.72Z"
        fill={fill}
      />
      <path
        d="M14.4385 20.72L15.0451 19.52C15.2251 19.16 15.0519 18.7267 14.6719 18.5867C13.5119 18.1733 10.9452 17.52 7.24518 17.96C6.87184 18.0066 6.61852 18.3533 6.67185 18.72L6.8718 20.04C6.92514 20.4 7.25847 20.64 7.61847 20.6C10.6451 20.2467 12.7252 20.7466 13.6452 21.0533C13.9518 21.16 14.2919 21.0133 14.4385 20.72Z"
        fill={fill}
      />
    </svg>
  );
};

export default MenuContractSolve3;
