import { IconProperty } from "@custom-types";
import React from "react";

const MenuContractSolve4 = ({ width = 32, height = 32, fill = "#F3BB90" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M26.1972 8.5H5.80282C4.78874 8.5 4 9.28873 4 10.3028V23.4859C4 24.5 4.78874 25.2887 5.80282 25.2887H26.1972C27.2113 25.2887 28 24.5 28 23.4859V10.3028C28 9.28873 27.2113 8.5 26.1972 8.5ZM23.7183 10.8662L15.9437 16.838L8.169 10.8662H23.7183ZM25.6338 22.9225H6.47885V12.5563L14.9296 19.0916C15.6056 19.5423 16.5071 19.5423 17.0704 19.0916L25.5211 12.5563V22.9225H25.6338Z"
        fill={fill}
      />
    </svg>
  );
};

export default MenuContractSolve4;
