import { IconProperty } from "@custom-types";
import React from "react";

const MenuContractRelease5 = ({ width = 32, height = 32, fill = "#F3BB90" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M21.3326 20.3813L16.256 18.3506H9.9993C7.42663 18.3506 5.33263 16.2566 5.33263 13.6839C5.33263 11.1113 7.42663 9.01727 9.9993 9.01727H16.256L21.3326 6.98661V20.3813ZM21.2566 4.14527L15.7426 6.35061H9.9993C5.72196 6.35061 2.28929 10.0313 2.69929 14.3913C3.05663 18.1906 6.39796 21.0173 10.214 21.0173H15.7426L21.2566 23.2233C22.57 23.7486 23.9993 22.7813 23.9993 21.3666V6.00194C23.9993 4.58727 22.57 3.61927 21.2566 4.14527ZM26.1007 10.425C25.6993 10.347 25.3327 10.677 25.3327 11.085V12.4503C25.3327 12.727 25.4907 12.9976 25.7533 13.085C26.2827 13.2623 26.6653 13.763 26.6653 14.351C26.6653 14.939 26.2827 15.439 25.7533 15.6156C25.4907 15.7036 25.3327 15.9743 25.3327 16.251V17.6163C25.3327 18.0243 25.6993 18.355 26.1007 18.2763C27.94 17.9176 29.332 16.2936 29.332 14.351C29.332 12.4083 27.94 10.7836 26.1007 10.425ZM9.99963 22.3507H9.33296V27.0173C9.33296 27.386 9.63163 27.684 9.99963 27.684H11.333C11.701 27.684 11.9996 27.386 11.9996 27.0173V24.3507C11.9996 23.246 11.1043 22.3507 9.99963 22.3507Z"
        fill={fill}
      />
    </svg>
  );
};

export default MenuContractRelease5;
