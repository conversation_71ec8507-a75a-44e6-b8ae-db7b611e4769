import { IconProperty } from "@custom-types";
import React from "react";

const MenuClinicListIcon = ({ width = 32, height = 32, fill = "#F3BB90" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M23.0984 25.7H19.6628V22.2517V20.525C19.6628 20.2076 19.4058 19.95 19.0878 19.95H13.3223C13.0055 19.95 12.7484 20.207 12.7473 20.5238L12.7369 25.7H9.29844V10.3579L16.4342 7.3H23.0984V25.7ZM17.3628 25.7H15.0346V22.2517H17.3628V25.7ZM24.8234 5H15.9627L8.04379 8.39365C7.40957 8.66563 6.99844 9.2895 6.99844 9.9795V28H25.3984V5.575C25.3984 5.2576 25.1408 5 24.8234 5Z"
        fill={fill}
      />
      <path
        d="M15.0467 11.9H13.2935C12.9761 11.9 12.7185 12.1576 12.7185 12.475V13.625C12.7185 13.9429 12.9761 14.2 13.2935 14.2H15.0467V15.9537C15.0467 16.2717 15.3043 16.5287 15.6217 16.5287H16.7717C17.0891 16.5287 17.3467 16.2717 17.3467 15.9537V14.2H19.1004C19.4178 14.2 19.6754 13.9429 19.6754 13.625V12.475C19.6754 12.1576 19.4178 11.9 19.1004 11.9H17.3467V10.1468C17.3467 9.82938 17.0891 9.57178 16.7717 9.57178H15.6217C15.3043 9.57178 15.0467 9.82938 15.0467 10.1468V11.9Z"
        fill={fill}
      />
    </svg>
  );
};

export default MenuClinicListIcon;
