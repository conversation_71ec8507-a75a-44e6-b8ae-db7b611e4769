import { IconProperty } from "@custom-types";
import React from "react";

const MenuSaleReport = ({ width = 32, height = 33, fill = "#F3BB90" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 32 33" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M22 4.5H6C4.86667 4.5 4 5.36667 4 6.5V22.5C4 23.6333 4.86667 24.5 6 24.5H22C23.1333 24.5 24 23.6333 24 22.5V6.5C24 5.43333 23.1333 4.5 22 4.5ZM21.3333 21.8333H6.66667V7.16667H21.3333V21.8333ZM28 26.5V10.5C28 9.36667 27.1333 8.5 26 8.5H25.3333V25.8333H8V26.5C8 27.6333 8.86667 28.5 10 28.5H26C27.1333 28.5 28 27.6333 28 26.5ZM13.6 19.9667V18.6333C13.2 18.5667 12.8 18.4333 12.4667 18.3C12.1333 18.1 11.8667 17.9 11.6667 17.7C11.4667 17.4333 11.2667 17.2333 11.2 16.9667C11.0667 16.7 11 16.4333 11 16.1667L12.5333 15.7667C12.5333 15.9667 12.6 16.1667 12.6667 16.3C12.7333 16.5 12.8667 16.6333 13 16.7667C13.1333 16.9 13.3333 17.0333 13.5333 17.1C13.7333 17.1667 14 17.2333 14.2667 17.2333C14.6667 17.2333 15 17.1667 15.2 16.9667C15.4 16.7667 15.5333 16.5667 15.5333 16.3C15.5333 16.1 15.4667 15.9 15.3333 15.7667C15.2 15.6333 14.9333 15.5 14.6667 15.4333L13.4 15.2333C12.7333 15.1 12.2 14.8333 11.8 14.4333C11.4 14.0333 11.2 13.5667 11.2 12.9667C11.2 12.6333 11.2667 12.3667 11.4 12.0333C11.5333 11.7667 11.6667 11.5 11.8667 11.3C12.0667 11.1 12.3333 10.9 12.6 10.7667C12.8667 10.6333 13.2 10.5 13.5333 10.5V9.16667H14.8V10.5C15.1333 10.5667 15.4667 10.7 15.7333 10.8333C16 10.9667 16.2 11.1667 16.4 11.3667C16.6 11.5667 16.7333 11.7667 16.8 11.9667C16.9333 12.1667 17 12.3667 17 12.5667L15.4667 12.9667C15.4667 12.8333 15.4 12.7667 15.3333 12.6333C15.2667 12.5 15.2 12.3667 15.0667 12.2333C14.9333 12.1 14.8 12.0333 14.6667 11.9667C14.4667 11.9 14.2667 11.8333 14 11.8333C13.6 11.8333 13.3333 11.9 13.0667 12.1C12.8667 12.3 12.7333 12.5 12.7333 12.7667C12.7333 12.9667 12.8 13.1 12.9333 13.3C13.0667 13.4333 13.2667 13.5667 13.6 13.6333L14.7333 13.9C15.5333 14.1 16.0667 14.3667 16.4667 14.7667C16.8 15.1667 17 15.7 17 16.2333C17 16.5 16.9333 16.7667 16.8667 17.0333C16.8 17.3 16.6 17.5667 16.4 17.7667C16.2 17.9667 15.9333 18.1667 15.6667 18.3C15.4 18.4333 15.0667 18.5667 14.6667 18.6333V19.9667H13.6Z"
        fill={fill}
      />
    </svg>
  );
};

export default MenuSaleReport;
