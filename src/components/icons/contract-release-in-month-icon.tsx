import { IconProperty } from "@custom-types";

const ContractReleaseInMonthIcon = ({ width = 24, height = 24, fill = "#183028" }: IconProperty) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} viewBox="0 0 24 24" fill="none">
      <path
        d="M21 15L15.285 20.7L12.5778 18L12.9288 17.65C13.5303 17.05 14.4828 17.05 15.0343 17.65L15.285 17.9L18.5435 14.65C19.1451 14.05 20.0976 14.05 20.6491 14.65L21 15ZM11.4749 20H6.96306V7H15.9868V12.5C15.9868 12.8 16.1873 13 16.4881 13H17.4908C17.7916 13 17.9921 12.8 17.9921 12.5V6.5C17.9921 5.65 17.3404 5 16.4881 5H6.46174C5.6095 5 4.95778 5.65 4.95778 6.5V20.5C4.95778 21.35 5.6095 22 6.46174 22H11.4749C11.7757 22 11.9763 21.8 11.9763 21.5V20.5C11.9763 20.25 11.7757 20 11.4749 20ZM14.0317 3.5C14.0317 2.65 13.3799 2 12.5277 2H3.50396C2.65171 2 2 2.65 2 3.5V16.5C2 17.35 2.65171 18 3.50396 18H4.00528V4H14.0317V3.5ZM8.96834 9C8.66755 9 8.46702 9.2 8.46702 9.5V10.5C8.46702 10.8 8.66755 11 8.96834 11H13.9815C14.2823 11 14.4828 10.8 14.4828 10.5V9.5C14.4828 9.2 14.2823 9 13.9815 9H8.96834ZM12.4776 13H8.96834C8.66755 13 8.46702 13.2 8.46702 13.5V14.5C8.46702 14.8 8.66755 15 8.96834 15H10.9736C11.8259 15 12.4776 14.35 12.4776 13.5V13Z"
        fill={fill}
      />
    </svg>
  );
};

export default ContractReleaseInMonthIcon;
