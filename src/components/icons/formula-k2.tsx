import { IconProperty } from "@custom-types";
import React from "react";

const FormulaIcon = ({ width = 24, height = 24, stroke = "#E87722" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M9.0918 13.9102C8.25227 13.2805 7.63213 12.4027 7.31923 11.401C7.00633 10.3993 7.01653 9.32458 7.34838 8.32902C7.68023 7.33346 8.31692 6.46755 9.16825 5.85395C10.0196 5.24034 11.0424 4.91016 12.0918 4.91016C13.1412 4.91016 14.164 5.24034 15.0153 5.85395C15.8667 6.46755 16.5034 7.33346 16.8352 8.32902C17.1671 9.32458 17.1773 10.3993 16.8644 11.401C16.5515 12.4027 15.9313 13.2805 15.0918 13.9102C14.7014 14.2966 14.4074 14.7696 14.2337 15.2908C14.0599 15.812 14.0113 16.3667 14.0918 16.9102C14.0918 17.4406 13.8811 17.9493 13.506 18.3244C13.1309 18.6994 12.6222 18.9102 12.0918 18.9102C11.5614 18.9102 11.0527 18.6994 10.6776 18.3244C10.3025 17.9493 10.0918 17.4406 10.0918 16.9102C10.1723 16.3667 10.1237 15.812 9.94993 15.2908C9.7762 14.7696 9.48223 14.2966 9.0918 13.9102"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M9.79102 14.9102H14.391" stroke={stroke} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
};

export default FormulaIcon;
