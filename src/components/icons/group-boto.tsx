import { IconProperty } from "@custom-types";
import React from "react";

const GroupBancaBotoIcon = ({ width = 36, height = 36, fill = "#E87722" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 25 26" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M22.8208 25.6758H11.8442V1.35095C11.8442 0.74283 12.3915 0.317146 12.9997 0.43877L23.2769 2.68882C23.8546 2.81044 24.2499 3.32734 24.2499 3.90506V24.2163C24.2499 25.0068 23.6114 25.6758 22.8208 25.6758Z"
        fill={fill}
      />
      <path
        d="M15.9813 6.79352H15.3428C15.1603 6.79352 15.0083 6.64149 15.0083 6.45906V5.88134C15.0083 5.69891 15.1603 5.54688 15.3428 5.54688H15.9813C16.1637 5.54688 16.3158 5.69891 16.3158 5.88134V6.45906C16.3158 6.64149 16.1637 6.79352 15.9813 6.79352Z"
        fill="white"
      />
      <path
        d="M18.3807 6.79352H17.7422C17.5597 6.79352 17.4077 6.64149 17.4077 6.45906V5.88134C17.4077 5.69891 17.5597 5.54688 17.7422 5.54688H18.3807C18.5631 5.54688 18.7152 5.69891 18.7152 5.88134V6.45906C18.7152 6.64149 18.5631 6.79352 18.3807 6.79352Z"
        fill="white"
      />
      <path
        d="M20.7538 6.79352H20.1152C19.9328 6.79352 19.7808 6.64149 19.7808 6.45906V5.88134C19.7808 5.69891 19.9328 5.54688 20.1152 5.54688H20.7538C20.9362 5.54688 21.0882 5.69891 21.0882 5.88134V6.45906C21.1186 6.64149 20.9666 6.79352 20.7538 6.79352Z"
        fill="white"
      />
      <path
        d="M15.9813 8.80036H15.3428C15.1603 8.80036 15.0083 8.64833 15.0083 8.46589V7.88818C15.0083 7.70574 15.1603 7.55371 15.3428 7.55371H15.9813C16.1637 7.55371 16.3158 7.70574 16.3158 7.88818V8.46589C16.3158 8.64833 16.1637 8.80036 15.9813 8.80036Z"
        fill="white"
      />
      <path
        d="M18.3807 8.80036H17.7422C17.5597 8.80036 17.4077 8.64833 17.4077 8.46589V7.88818C17.4077 7.70574 17.5597 7.55371 17.7422 7.55371H18.3807C18.5631 7.55371 18.7152 7.70574 18.7152 7.88818V8.46589C18.7152 8.64833 18.5631 8.80036 18.3807 8.80036Z"
        fill="white"
      />
      <path
        d="M20.7538 8.80036H20.1152C19.9328 8.80036 19.7808 8.64833 19.7808 8.46589V7.88818C19.7808 7.70574 19.9328 7.55371 20.1152 7.55371H20.7538C20.9362 7.55371 21.0882 7.70574 21.0882 7.88818V8.46589C21.1186 8.64833 20.9666 8.80036 20.7538 8.80036Z"
        fill="white"
      />
      <path
        d="M15.9813 10.8072H15.3428C15.1603 10.8072 15.0083 10.6552 15.0083 10.4727V9.89501C15.0083 9.71258 15.1603 9.56055 15.3428 9.56055H15.9813C16.1637 9.56055 16.3158 9.71258 16.3158 9.89501V10.4727C16.3158 10.6552 16.1637 10.8072 15.9813 10.8072Z"
        fill="white"
      />
      <path
        d="M18.3807 10.8072H17.7422C17.5597 10.8072 17.4077 10.6552 17.4077 10.4727V9.89501C17.4077 9.71258 17.5597 9.56055 17.7422 9.56055H18.3807C18.5631 9.56055 18.7152 9.71258 18.7152 9.89501V10.4727C18.7152 10.6552 18.5631 10.8072 18.3807 10.8072Z"
        fill="white"
      />
      <path
        d="M20.7538 10.8072H20.1152C19.9328 10.8072 19.7808 10.6552 19.7808 10.4727V9.89501C19.7808 9.71258 19.9328 9.56055 20.1152 9.56055H20.7538C20.9362 9.56055 21.0882 9.71258 21.0882 9.89501V10.4727C21.1186 10.6552 20.9666 10.8072 20.7538 10.8072Z"
        fill="white"
      />
      <path
        d="M15.9813 12.814H15.3428C15.1603 12.814 15.0083 12.662 15.0083 12.4796V11.9018C15.0083 11.7194 15.1603 11.5674 15.3428 11.5674H15.9813C16.1637 11.5674 16.3158 11.7194 16.3158 11.9018V12.4796C16.3158 12.662 16.1637 12.814 15.9813 12.814Z"
        fill="white"
      />
      <path
        d="M18.3807 12.814H17.7422C17.5597 12.814 17.4077 12.662 17.4077 12.4796V11.9018C17.4077 11.7194 17.5597 11.5674 17.7422 11.5674H18.3807C18.5631 11.5674 18.7152 11.7194 18.7152 11.9018V12.4796C18.7152 12.662 18.5631 12.814 18.3807 12.814Z"
        fill="white"
      />
      <path
        d="M20.7538 12.814H20.1152C19.9328 12.814 19.7808 12.662 19.7808 12.4796V11.9018C19.7808 11.7194 19.9328 11.5674 20.1152 11.5674H20.7538C20.9362 11.5674 21.0882 11.7194 21.0882 11.9018V12.4796C21.1186 12.662 20.9666 12.814 20.7538 12.814Z"
        fill="white"
      />
      <path
        d="M19.2345 25.0673H16.8628V22.1483C16.8628 21.5402 17.3493 21.0537 17.9574 21.0537H18.1398C18.748 21.0537 19.2345 21.5402 19.2345 22.1483V25.0673Z"
        fill="white"
      />
      <path
        d="M11.8448 25.6755H1.75002C1.1419 25.6755 0.625 25.189 0.625 24.5505V9.25624C0.625 8.64812 1.1115 8.16162 1.71962 8.16162H10.6894C11.3279 8.16162 11.8448 8.67852 11.8448 9.31705V25.6755Z"
        fill="#F3BB91"
      />
      <path
        d="M9.38178 12.7531H3.08774C2.54043 12.7531 2.11475 12.3274 2.11475 11.7801C2.11475 11.2328 2.54043 10.8071 3.08774 10.8071H9.38178C9.92909 10.8071 10.3548 11.2328 10.3548 11.7801C10.3548 12.3274 9.92909 12.7531 9.38178 12.7531Z"
        fill="#183028"
      />
      <path
        d="M9.38178 17.6486H3.08774C2.54043 17.6486 2.11475 17.2229 2.11475 16.6756C2.11475 16.1283 2.54043 15.7026 3.08774 15.7026H9.38178C9.92909 15.7026 10.3548 16.1283 10.3548 16.6756C10.3548 17.1925 9.92909 17.6486 9.38178 17.6486Z"
        fill="#E87722"
      />
      <path
        d="M9.38178 22.9997H3.08774C2.54043 22.9997 2.11475 22.574 2.11475 22.0267C2.11475 21.4794 2.54043 21.0537 3.08774 21.0537H9.38178C9.92909 21.0537 10.3548 21.4794 10.3548 22.0267C10.3548 22.574 9.92909 22.9997 9.38178 22.9997Z"
        fill="#E87722"
      />
    </svg>
  );
};

export default GroupBancaBotoIcon;
