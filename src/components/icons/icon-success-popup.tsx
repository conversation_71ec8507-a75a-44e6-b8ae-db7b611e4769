import { IconProperty } from "@custom-types";
import * as React from "react";

function IconSuccessPopup({ width = 52, height = 52, fill = "#03824F", stroke = "#03824F" }: IconProperty) {
  return (
    <svg width={width} height={height} viewBox="0 0 52 52" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="25.9993" cy="26.0001" r="22.8333" fill={fill} stroke={stroke} strokeWidth="2" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M21.6257 34.6666L13.6016 26.4531L14.0854 25.9999C14.8874 25.2494 16.1869 25.2494 16.9882 25.9999L21.6257 31.0409L35.6604 17.8961C36.4617 17.1456 37.7619 17.1456 38.5632 17.8961L39.0471 18.3499L21.6257 34.6666Z"
        fill="#F8F9F9"
        stroke="#F8F9F9"
      />
    </svg>
  );
}

export default IconSuccessPopup;
