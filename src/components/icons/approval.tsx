import { IconProperty } from "@custom-types";
import React from "react";

const IconApproval = ({ width = 24, height = 24, fill = "white" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 4.5C10.346 4.5 9 5.8455 9 7.5C9 9.1545 10.346 10.5 12 10.5C13.6545 10.5 15 9.1545 15 7.5C15 5.8455 13.6545 4.5 12 4.5ZM12 12.5C9.243 12.5 7 10.257 7 7.5C7 4.743 9.243 2.5 12 2.5C14.757 2.5 17 4.743 17 7.5C17 10.257 14.757 12.5 12 12.5Z"
        fill={fill}
      />
      <path
        d="M8.876 15.5H15.124C15.8045 15.5 16.4441 15.6754 17 15.9835C17.3954 16.2027 18 15.947 18 15.4949V14.7153C18 14.4284 17.847 14.1598 17.5866 14.0395C17.3963 13.9515 17.2006 13.8734 17 13.8059C16.4109 13.6075 15.78 13.5 15.124 13.5H8.876C5.631 13.5 3 16.131 3 19.376V20C3 20.8285 3.6715 21.5 4.5 21.5H5V19.376C5 17.2355 6.7355 15.5 8.876 15.5Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M21 18V16.5C21 15.6715 20.3285 15 19.5 15H19V18H17.5C16.6715 18 16 18.6715 16 19.5V20H19V21.5C19 22.3285 19.6715 23 20.5 23H21V20H22.5C23.3285 20 24 19.3285 24 18.5V18H21Z"
        fill={fill}
      />
    </svg>
  );
};

export default IconApproval;
