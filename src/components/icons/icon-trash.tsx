import { IconProperty } from "@custom-types";

function IconTrash({ width = 24, height = 24, stroke = "#E87722", fill = "#FAE4D3" }: IconProperty) {
  return (
    <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="24" height="24" rx="12" fill={fill} />
      <path
        d="M9.99996 6V6.66667H6.66663V8H7.33329V16.6667C7.33329 17.0203 7.47377 17.3594 7.72382 17.6095C7.97387 17.8595 8.313 18 8.66663 18H15.3333C15.6869 18 16.0261 17.8595 16.2761 17.6095C16.5261 17.3594 16.6666 17.0203 16.6666 16.6667V8H17.3333V6.66667H14V6H9.99996ZM8.66663 8H15.3333V16.6667H8.66663V8ZM9.99996 9.33333V15.3333H11.3333V9.33333H9.99996ZM12.6666 9.33333V15.3333H14V9.33333H12.6666Z"
        fill={stroke}
      />
    </svg>
  );
}

export default IconTrash;
