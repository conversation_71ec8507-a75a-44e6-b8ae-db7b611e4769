import { IconProperty } from "@custom-types";
import React from "react";

const IconTickLeads = ({ width = 14, height = 14, fill = "#03824F" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.30447 9.91667L2.33398 6.87608L2.51309 6.70833C2.80999 6.4305 3.29106 6.4305 3.58771 6.70833L5.30447 8.57446L10.5 3.70838C10.7966 3.43054 11.278 3.43054 11.5746 3.70838L11.7537 3.87636L5.30447 9.91667Z"
        fill={fill}
        stroke={fill}
        strokeWidth="1.375"
      />
    </svg>
  );
};

export default IconTickLeads;
