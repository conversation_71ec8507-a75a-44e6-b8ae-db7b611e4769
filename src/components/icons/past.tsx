import { IconProperty } from "@custom-types";
import React from "react";

const IconPast = ({ width = 14, height = 14, fill = "#8B8E8F" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M7.489 1.75002C4.58043 1.75002 2.99968 3.58368 2.43061 5.00635V3.33076H2.11446C1.57701 3.33076 1.16602 3.74175 1.16602 4.27921V7.12455H4.01136C4.54881 7.12455 4.95981 6.71356 4.95981 6.1761V5.85995H3.4739C3.69521 5.06958 4.54881 3.55206 6.41409 3.14107C8.88005 2.572 11.3776 4.27921 11.5673 6.77679C11.757 9.21113 9.86012 11.2345 7.45738 11.2345C5.71856 11.2345 4.16943 10.128 3.56875 8.48399C3.50552 8.32592 3.34744 8.26269 3.18937 8.2943L2.58869 8.45238C2.399 8.51561 2.30415 8.7053 2.36738 8.86337C3.2526 11.3926 5.93987 13.0049 8.72198 12.3094C10.8402 11.8036 12.4842 10.0015 12.7687 7.82008C13.2745 4.59536 10.7137 1.75002 7.489 1.75002Z"
        fill={fill}
      />
      <path
        d="M6.54102 4.59534V7.75683H8.75406C9.29151 7.75683 9.70251 7.34583 9.70251 6.80838V6.49223H7.80561V5.54378C7.80561 5.00633 7.39462 4.59534 6.85716 4.59534H6.54102Z"
        fill={fill}
      />
    </svg>
  );
};

export default IconPast;
