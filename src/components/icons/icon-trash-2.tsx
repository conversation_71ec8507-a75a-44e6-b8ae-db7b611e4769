import { IconProperty } from "@custom-types";
import React from "react";

const IconTrash2 = ({ width = 25, height = 24, fill = "#B30909" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M19.994 4.54139H4.00595C3.70238 4.54139 3.5 4.33901 3.5 4.03544V3.02353C3.5 2.71996 3.70238 2.51758 4.00595 2.51758H19.994C20.2976 2.51758 20.5 2.71996 20.5 3.02353V4.03544C20.5 4.33901 20.2976 4.54139 19.994 4.54139Z"
        fill={fill}
      />
      <path
        d="M8.86551 4.2381L7.34766 2.82143L8.86551 1.20238C8.9667 1.10119 9.16908 1 9.27028 1H14.9369C15.1393 1 15.2405 1.10119 15.3417 1.20238L16.7584 2.82143L15.1393 4.2381L14.2286 3.125H9.97861L8.86551 4.2381Z"
        fill={fill}
      />
      <path
        d="M16.756 22.6543H7.24405C6.33333 22.6543 5.625 21.946 5.625 21.0352V7.27334C5.625 6.36263 6.33333 5.6543 7.24405 5.6543H16.8571C17.7679 5.6543 18.4762 6.36263 18.4762 7.27334V21.1364C18.375 21.946 17.6667 22.6543 16.756 22.6543ZM7.75 20.5293H16.25V7.7793H7.75V20.5293Z"
        fill={fill}
      />
      <path
        d="M13.0099 16.6843L9.46819 13.1427C9.26581 12.9403 9.26581 12.6367 9.46819 12.4343L10.1765 11.726C10.3789 11.5236 10.6825 11.5236 10.8849 11.726L14.4265 15.2677C14.6289 15.4701 14.6289 15.7736 14.4265 15.976L13.7182 16.6843C13.5158 16.8867 13.2122 16.8867 13.0099 16.6843Z"
        fill={fill}
      />
      <path
        d="M10.282 16.6843L9.57366 15.976C9.37128 15.7736 9.37128 15.4701 9.57366 15.2677L13.1153 11.726C13.3177 11.5236 13.6213 11.5236 13.8237 11.726L14.532 12.4343C14.7344 12.6367 14.7344 12.9403 14.532 13.1427L10.9903 16.6843C10.7879 16.8867 10.4844 16.8867 10.282 16.6843Z"
        fill={fill}
      />
    </svg>
  );
};

export default IconTrash2;
