import { IconProperty } from "@custom-types";
import React from "react";

const IconUserSmall = ({ width = 16, height = 16, fill = "#183028" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M7.98895 1.33398C6.00476 1.33398 4.39062 2.96289 4.39062 4.96525C4.39062 6.96721 6.00476 8.59612 7.98895 8.59612C9.97315 8.59612 11.5873 6.96721 11.5873 4.96525C11.5873 2.96289 9.97315 1.33398 7.98895 1.33398ZM7.98895 7.0636C6.8424 7.0636 5.90924 6.12231 5.90924 4.96525C5.90924 3.8078 6.8424 2.86651 7.98895 2.86651C9.13551 2.86651 10.0687 3.8078 10.0687 4.96525C10.0687 6.12231 9.13551 7.0636 7.98895 7.0636Z"
        fill={fill}
      />
      <path
        d="M13.4304 12.4582L13.4096 12.5663L13.3592 12.6639C13.3081 12.7635 13.0096 13.2866 12.1727 13.771C11.1457 14.3662 9.74533 14.6679 8.0105 14.6679H7.98892C6.2541 14.6679 4.85377 14.3662 3.82631 13.771C2.98987 13.2866 2.69134 12.7635 2.64019 12.6635L2.58983 12.5663L2.56905 12.4582C2.11267 10.0953 4.06489 8.00304 4.14801 7.91512C4.43815 7.61023 4.92011 7.60015 5.22224 7.89294C5.52316 8.18492 5.53356 8.67049 5.24542 8.97538C5.23263 8.98869 4.01813 10.2978 4.01813 11.7242C4.01813 11.8214 4.02373 11.9174 4.03532 12.011C4.2787 12.2893 5.24462 13.1354 7.98892 13.1354H8.0105C10.7564 13.1354 11.7211 12.2893 11.9637 12.011C11.9753 11.9174 11.9809 11.821 11.9809 11.7234C11.9809 10.3038 10.7656 8.98788 10.7536 8.97457C10.4655 8.67089 10.4759 8.18492 10.7772 7.89294C11.0793 7.60015 11.5609 7.61023 11.8506 7.91472C11.9337 8.00223 13.8888 10.0869 13.4304 12.4582Z"
        fill={fill}
      />
    </svg>
  );
};

export default IconUserSmall;
