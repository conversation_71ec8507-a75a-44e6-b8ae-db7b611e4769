import { IconProperty } from "@custom-types";
import React from "react";

const IconHistory = ({ width = 24, height = 24, fill = "#E87722" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12.8394 3C7.85328 3 5.14343 6.14343 4.16788 8.58229V5.70985H3.62591C2.70456 5.70985 2 6.41441 2 7.33576V12.2135H6.87773C7.79908 12.2135 8.50364 11.5089 8.50364 10.5876V10.0456H5.95638C6.33576 8.69068 7.79908 6.08923 10.9967 5.38467C15.2241 4.40912 19.5056 7.33576 19.8308 11.6173C20.156 15.7905 16.9042 19.2591 12.7852 19.2591C9.80437 19.2591 7.14871 17.3622 6.11897 14.544C6.01058 14.273 5.73959 14.1646 5.46861 14.2188L4.43886 14.4898C4.11368 14.5982 3.95109 14.9233 4.05949 15.1943C5.577 19.5301 10.1837 22.2941 14.9531 21.1018C18.5843 20.2346 21.4025 17.1454 21.8903 13.4058C22.7574 7.87773 18.3675 3 12.8394 3Z"
        fill={fill}
      />
      <path
        d="M11.2148 7.87695V13.2967H15.0086C15.93 13.2967 16.6345 12.5921 16.6345 11.6707V11.1288H13.3827V9.50286C13.3827 8.58151 12.6782 7.87695 11.7568 7.87695H11.2148Z"
        fill={fill}
      />
    </svg>
  );
};

export default IconHistory;
