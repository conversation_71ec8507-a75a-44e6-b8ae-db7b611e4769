import { IconProperty } from "@custom-types";

const RadioSelected = ({ width = 20, height = 20, stroke = "#F5F5F5", fill = "#E87722" }: IconProperty) => {
  return (
    <svg width={width} height={height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="0.5" y="0.5" width="19" height="19" rx="9.5" fill={fill} stroke={fill} />
      <circle cx="10" cy="9.99988" r="4" stroke={stroke} strokeWidth="4" />
    </svg>
  );
};

export default RadioSelected;
