import IconSearch from "../icons/icon-search";
import { WrapperInputSearch } from "./styled";

export interface IInputSearchProps {
  onChangeSearch?(e: { target: { value: string } }): void;
}

function InputSearch({ onChangeSearch }: IInputSearchProps) {
  return (
    <WrapperInputSearch>
      <IconSearch />
      <input onChange={onChangeSearch} type="text" placeholder="Tìm kiếm" />
    </WrapperInputSearch>
  );
}

export default InputSearch;
