import { TableType } from "@custom-types/config-table";
import ItemTableCollapse from "./item-table-collapse";
import { HeaderItem, TableCollapseMobileWrapper, ItemList } from "./styled";

export interface ChildrenMobileData {
  rowKey?: string;
  label: string;
  value?: number | string;
  detail?: string;
  children?: ChildrenMobileData[];
  type?: TableType;
}

export interface TableCollapseMobileData {
  label?: string;
  value?: number | string;
  children?: ChildrenMobileData[];
  isHide?: boolean;
  type?: TableType;
}

export interface HeadTableCollapseMobile {
  label: string;
}

interface TableCollapseMobileProp {
  head: HeadTableCollapseMobile[][];
  body: TableCollapseMobileData[];
  defaultOpen?: boolean;
  onViewDetail?: (item: any) => void;
}

const TableCollapseMobile = ({ head, body, onViewDetail }: TableCollapseMobileProp) => {
  return (
    <TableCollapseMobileWrapper className="hide-desktop">
      <HeaderItem>
        <h6 className="h8">{head.flat().find((item) => item.label)?.label || ""}</h6>
      </HeaderItem>
      <ItemList>
        {body.map((data, index) => (
          <ItemTableCollapse key={index} data={data} defaultOpen={true} onViewDetail={onViewDetail} />
        ))}
      </ItemList>
    </TableCollapseMobileWrapper>
  );
};

export default TableCollapseMobile;
