import styled from "styled-components";
import { css } from "styled-components";
import { device } from "styles/media";

const DragEnterStyle = css`
  background: ${({ theme }) => theme.color.status.primary_5};
  border-color: ${({ theme }) => theme.color.status.primary};
`;

export const UploadInputWrapper = styled.div<{ dragEnter: boolean }>`
  display: flex;

  position: relative;

  input {
    width: 0px;
    height: 0px;
  }

  > div:first-of-type {
    ${({ dragEnter }) => (dragEnter ? DragEnterStyle : null)}
  }

  @media ${device.mobile} {
    width: 100%;
  }
`;

export const DragWrapper = styled.div`
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;

  user-select: none;
  cursor: pointer;
`;
