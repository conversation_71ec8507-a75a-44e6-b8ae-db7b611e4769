import Icons from "components/icons";
import useClickAway from "hooks/use-click-away";
import Link from "next/link";
import { useRouter } from "next/router";
import { useRef, useState } from "react";
import { LinkProps } from "services/menu-link";
import {
  ButtonSetting,
  LinkSticky,
  SettingItem,
  SettingList,
  SideBarItemWrapper,
  SideBarListWrapper,
  SideBarSticky,
  Sticky,
} from "./styled";

interface SideBarDesktopProps {
  listLink: LinkProps[];
  onFuncSetting: (item: string) => void;
}

const settingList: LinkProps[] = [
  { value: "change-password", label: "<PERSON><PERSON><PERSON> mật khẩu", icon: "change-password" },
  { value: "log-out", label: "Đăng xuất", icon: "logout" },
];

const activeLink = (pathName: string, link: string) => {
  const splitPathName = pathName.split("/")[1];
  const splitPathLink = link.split("/")[1];

  return splitPathName === splitPathLink;
};

const SideBarDesktop = ({ listLink, onFuncSetting }: SideBarDesktopProps) => {
  const [showSetting, setShowSetting] = useState(false);

  const ref = useRef(null);
  const router = useRouter();
  const { pathname } = router;

  useClickAway(ref, () => {
    setShowSetting(false);
  });

  const handleSelect = (value: string) => {
    setShowSetting(false);
    onFuncSetting(value);
  };

  return (
    <SideBarSticky className="hide-mobile">
      <Sticky>
        <img src={`${process.env.basePath}/img/logo-fwd-cube.svg`} width={50} height={50} alt="" />
      </Sticky>
      <SideBarListWrapper>
        {listLink.map((item, index) =>
          item.type === "link" ? (
            <Link href={item.href} key={index} passHref legacyBehavior>
              <SideBarItemWrapper active={activeLink(pathname, item.href)}>
                <Icons icon={item.icon} />
                <span dangerouslySetInnerHTML={{ __html: item.label }} />
              </SideBarItemWrapper>
            </Link>
          ) : null
        )}
      </SideBarListWrapper>
      <LinkSticky>
        <SideBarItemWrapper>
          <ButtonSetting onClick={() => setShowSetting(true)}>
            <Icons icon="setting" />
            Cài đặt
          </ButtonSetting>

          <SettingList show={showSetting} ref={ref}>
            {settingList.map((item, key) => (
              <SettingItem className="body-4" key={key} onClick={() => handleSelect(item.value)}>
                <Icons icon={item.icon} />
                {item.label}
              </SettingItem>
            ))}
          </SettingList>
        </SideBarItemWrapper>
      </LinkSticky>
    </SideBarSticky>
  );
};

export default SideBarDesktop;
