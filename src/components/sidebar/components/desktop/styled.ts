import styled, { css } from "styled-components";
import { ScrollNoBar } from "styles";
import { FadeReverseStyled, FadeStyled } from "styles/fade-animation";
import { device } from "styles/media";

export const Sticky = styled.div`
  width: 100%;

  display: flex;
  justify-content: center;

  position: sticky;
  top: 0;
  z-index: 1;

  padding-top: 16px;
  padding-bottom: 8px;
  background-color: ${({ theme }) => theme.color.status.primary};
`;

export const SideBarSticky = styled.div`
  width: 96px;
  min-width: 96px;
  max-width: 96px;
  height: 100%;

  position: fixed;
  left: 0;
  top: 0;
  z-index: 1002;

  display: flex;
  flex-direction: column;
  align-items: center;

  background-color: ${({ theme }) => theme.color.status.primary};
`;

export const HeaderWrapper = styled.div`
  /* width: 120px; */
  padding: 16px;
  padding-top: 64px;

  background-color: #ffffff;
`;

export const HeaderItemWrapper = styled.a`
  width: 88px;
  height: auto;
  padding: 13px 10px;

  display: flex;
  flex-direction: column;
  align-items: center;

  font-weight: 450;
  font-size: 12px;
  line-height: 125%;
  text-align: center;

  color: ${({ theme }) => theme.color.status.grey_darkest};

  svg {
    margin-bottom: 4px;
  }

  :not(:last-child) {
    margin-bottom: 12px;
  }
`;

export const SideBarListWrapper = styled.div`
  width: 100%;
  padding-bottom: 93px;

  ${ScrollNoBar}
`;

//Item
export const LinkSticky = styled.div`
  width: 100%;
  padding-bottom: 26px;

  position: absolute;
  bottom: 0px;

  background: ${({ theme }) => theme.color.status.primary};
`;

const ActiveStyled = css`
  ::before {
    content: "";
    position: absolute;
    top: 0px;
    bottom: 0px;
    right: 0px;
    left: 8px;
    z-index: -1;

    background: #ffffff;
    box-shadow: 0px 4px 0px rgba(0, 0, 0, 0.1);
    border-radius: 8px 0px 0px 8px;
  }
`;

export const SideBarItemWrapper = styled.a<{ active?: boolean }>`
  width: 100%;
  padding: 12px 0px;

  display: flex;
  flex-direction: column;
  align-items: center;

  position: relative;

  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  text-align: center;

  color: ${({ active, theme }) => (active ? theme.color.status.primary : "#ffffff")};

  svg {
    margin-bottom: 3px;

    path {
      fill: ${({ active, theme }) => (active ? theme.color.status.primary : "#ffffff")};
    }
  }

  ${({ active }) => (active ? ActiveStyled : null)}
`;

export const ButtonSetting = styled.button`
  display: flex;
  flex-direction: column;
  align-items: center;

  font-weight: 450;
  font-size: 12px;
  line-height: 150%;
  color: #ffffff;
`;

export const SettingList = styled.div<{ show: boolean }>`
  width: 192px;
  padding: 12px;

  position: absolute;
  bottom: 100%;
  left: calc(50% - 24px);

  display: flex;
  flex-direction: column;

  background: #ffffff;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.25);
  border-radius: 8px;

  ${({ show }) => (show ? FadeStyled : FadeReverseStyled)}
`;

export const SettingItem = styled.div`
  padding: 10px 0px;
  padding-left: 32px;

  position: relative;

  text-align: left;
  color: ${({ theme }) => theme.color.text.body};

  cursor: pointer;

  svg {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);

    margin: 0px !important;
    path {
      fill: ${({ theme }) => theme.color.status.primary} !important;
    }
  }
`;

export const LogoutContent = styled.div`
  p {
    margin-bottom: 24px;
  }

  @media ${device.mobile} {
    padding: 0px 16px;
  }
`;
