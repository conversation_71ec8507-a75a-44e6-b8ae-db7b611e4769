import Icons from "components/icons";
import ScrollBar from "components/scroll-bar";
import useIsGroup from "hooks/use-is-group";
import Link from "next/link";
import { useRouter } from "next/router";
import { LinkProps } from "services/menu-link";
import { SubSideBarItem } from "../../styled";
import { ListWrapper, SubSideBarDesktopWrapper, SubSideBarSticky } from "./styled";

const SubSideBarDesktop = ({ list, paddingTabMenu = true }: { list: LinkProps[]; paddingTabMenu?: boolean }) => {
  const { pathname } = useRouter();
  const isGroup = useIsGroup();

  return (
    <SubSideBarSticky className="hide-mobile">
      <SubSideBarDesktopWrapper isGroup={isGroup} paddingTabMenu={paddingTabMenu}>
        <ScrollBar>
          <ListWrapper>
            {list.map((item, index) => (
              <Link href={item.href} key={index} passHref legacyBehavior>
                <SubSideBarItem active={pathname.includes(item.href)}>
                  <Icons icon={item.icon} width={32} height={32} />
                  {item.label}
                </SubSideBarItem>
              </Link>
            ))}
          </ListWrapper>
        </ScrollBar>
      </SubSideBarDesktopWrapper>
    </SubSideBarSticky>
  );
};

export default SubSideBarDesktop;
