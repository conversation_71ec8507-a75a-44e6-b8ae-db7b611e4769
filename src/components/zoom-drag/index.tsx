import React, { useState, useRef, useEffect, ReactNode } from "react";
import { ZoomDragWrapper } from "./styled";

interface ZoomPanPinchProps {
  children: ReactNode;
  minScale?: number;
  maxScale?: number;
  initialScale?: number;
}

const ZoomPanPinch: React.FC<ZoomPanPinchProps> = ({ children, minScale = 0.5, maxScale = 2, initialScale = 0.5 }) => {
  const [scale, setScale] = useState(initialScale);
  const [positionX, setPositionX] = useState(0);
  const [positionY, setPositionY] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [startY, setStartY] = useState(0);
  const [startDistance, setStartDistance] = useState<number | null>(null);
  const [cursor, setCursor] = useState("grab");

  const containerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  const setCurrentPosition = (x: number, y: number) => {
    const container = containerRef.current;
    const content = contentRef.current;
    if (!container || !content) return;

    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;
    const contentWidth = content.offsetWidth * scale;
    const contentHeight = content.offsetHeight * scale;

    const limitedX = Math.min(Math.max(x, containerWidth - contentWidth), 0);
    const limitedY = Math.min(Math.max(y, containerHeight - contentHeight), 0);

    setPositionX(limitedX);
    setPositionY(limitedY);
  };

  useEffect(() => {
    const container = containerRef.current;
    const content = contentRef.current;
    if (!container || !content) return;

    // Touch mobile
    const handleTouchStart = (e: TouchEvent) => {
      setIsDragging(true);
      e.preventDefault();

      if (e.touches.length === 1) {
        setStartX(e.touches[0].clientX - positionX);
        setStartY(e.touches[0].clientY - positionY);
      } else if (e.touches.length === 2) {
        const touch1 = e.touches[0];
        const touch2 = e.touches[1];
        const initialDistance = Math.sqrt(
          Math.pow(touch2.clientX - touch1.clientX, 2) + Math.pow(touch2.clientY - touch1.clientY, 2)
        );
        setStartDistance(initialDistance);
      }
    };

    const handleTouchMove = (e: TouchEvent) => {
      e.preventDefault();

      if (e.touches.length === 1 && isDragging) {
        const newX = e.touches[0].clientX - startX;
        const newY = e.touches[0].clientY - startY;
        setCurrentPosition(newX, newY);
      } else if (e.touches.length === 2 && startDistance) {
        const touch1 = e.touches[0];
        const touch2 = e.touches[1];

        const currentDistance = Math.sqrt(
          Math.pow(touch2.clientX - touch1.clientX, 2) + Math.pow(touch2.clientY - touch1.clientY, 2)
        );

        const deltaDistance = currentDistance - startDistance;
        const zoomFactor = 1 + (deltaDistance / startDistance) * 0.5; // sensitivity

        const newScale = Math.min(Math.max(scale * zoomFactor, minScale), maxScale);

        const midX = (touch1.clientX + touch2.clientX) / 2;
        const midY = (touch1.clientY + touch2.clientY) / 2;

        const offsetX = midX - positionX;
        const offsetY = midY - positionY;

        setPositionX(midX - offsetX * (newScale / scale));
        setPositionY(midY - offsetY * (newScale / scale));

        setScale(newScale);
        setStartDistance(currentDistance);
      }
    };

    const handleTouchEnd = () => {
      setIsDragging(false);
      setStartDistance(null);
    };

    // Mouse PC
    const handleWheel = (e: WheelEvent) => {
      e.preventDefault();

      const rect = container.getBoundingClientRect();
      const mouseX = e.clientX - rect.left;
      const mouseY = e.clientY - rect.top;

      const delta = e.deltaY * -0.0004; // sensitivity
      const newScale = Math.min(Math.max(scale + delta, minScale), maxScale);

      const scaleChange = newScale - scale;

      const newPositionX = positionX - (mouseX - positionX) * (scaleChange / scale);
      const newPositionY = positionY - (mouseY - positionY) * (scaleChange / scale);

      setScale(newScale);
      setPositionX(newPositionX);
      setPositionY(newPositionY);

      //zoom out
      if (newScale < scale) {
        setCurrentPosition(newPositionX, newPositionY);
      }
    };

    const handleMouseDown = (e: MouseEvent) => {
      e.preventDefault();
      setIsDragging(true);

      setStartX(e.clientX - positionX);
      setStartY(e.clientY - positionY);
    };

    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return;

      setCursor("grabbing");

      const newX = e.clientX - startX;
      const newY = e.clientY - startY;

      // setPositionX(newX);
      // setPositionY(newY);
      setCurrentPosition(newX, newY); // check is in limit
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      setCursor("grab");
    };

    container.addEventListener("wheel", handleWheel);
    container.addEventListener("mousedown", handleMouseDown);
    window.addEventListener("mousemove", handleMouseMove);
    window.addEventListener("mouseup", handleMouseUp);

    container.addEventListener("touchstart", handleTouchStart, { passive: false });
    container.addEventListener("touchmove", handleTouchMove, { passive: false });
    window.addEventListener("touchend", handleTouchEnd, { passive: false });

    return () => {
      container.removeEventListener("wheel", handleWheel);
      container.removeEventListener("mousedown", handleMouseDown);
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("mouseup", handleMouseUp);

      container.removeEventListener("touchstart", handleTouchStart);
      container.removeEventListener("touchmove", handleTouchMove);
      window.removeEventListener("touchend", handleTouchEnd);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [scale, positionX, positionY, isDragging, startX, startY, minScale, maxScale]);

  return (
    <ZoomDragWrapper ref={containerRef} cursor={cursor}>
      <div
        ref={contentRef}
        style={{
          transform: `translate(${positionX}px, ${positionY}px) scale(${scale})`,
          transformOrigin: "0 0",
          width: "fit-content",
          height: "fit-content",
        }}
      >
        {children}
      </div>
    </ZoomDragWrapper>
  );
};

export default ZoomPanPinch;
