import { TableNoHeadConfig } from "@custom-types/config-table";
import { TableSOAWrapper } from "./styled";
import TableDesktopSOA from "./table-desktop";
import TableMobileSOA from "./table-mobile";

export interface TableSOANoHeaderProps {
  data?: TableNoHeadConfig[];
  renderNote?: any;
}

//Table only for SOA
const TableSOANoHeader = ({ data, renderNote }: TableSOANoHeaderProps) => {
  return (
    <TableSOAWrapper>
      <TableDesktopSOA data={data} renderNote={renderNote} />
      <TableMobileSOA data={data} renderNote={renderNote} />
    </TableSOAWrapper>
  );
};

export default TableSOANoHeader;
