import styled from "styled-components";

export const TableMobileSOANoHeaderWrapper = styled.div`
  padding: 8px 16px;

  background: #ffffff;
  border-radius: 16px;
`;

export const ItemTable = styled.div`
  padding: 6px 0px;

  display: flex;
  flex-direction: column;

  :not(:last-child) {
    border-bottom: 1px solid ${({ theme }) => theme.color.status.grey};
  }

  label {
    width: 100%;
    margin-bottom: 4px;

    font-weight: 400;
    font-size: 14px;
    line-height: 125%;

    color: ${({ theme }) => theme.color.status.grey_darkest};
  }

  p {
    width: 100%;

    font-weight: 700;
    font-size: 16px;
    line-height: 125%;
    word-break: break-all;
  }
`;
