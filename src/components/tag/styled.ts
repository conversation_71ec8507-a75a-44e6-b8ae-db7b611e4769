import styled, { css } from "styled-components";

const DefaultTag = css`
  height: auto;
  display: inline;

  font-weight: 400;
  font-size: 12px;
  line-height: 150%;

  text-align: left;
  padding: 2px 8px;
  border-radius: 4px;
  color: #ffffff;
`;

export const Overdue = styled.div`
  ${DefaultTag}
  background: ${({ theme }) => theme.color.status.red_light};
  color: ${({ theme }) => theme.color.status.red};
`;

export const Completed = styled.div`
  ${DefaultTag}
  background: ${({ theme }) => theme.color.status.light_green};
`;

export const Expired = styled.div`
  ${DefaultTag};
  background: ${({ theme }) => theme.color.status.red};
`;
