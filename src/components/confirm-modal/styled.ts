import styled from "styled-components";
import { ButtonPrimary, ButtonSecondary } from "styles/buttons";
import { device } from "styles/media";

export const ConfirmContentWrapper = styled.div`
  p {
    margin-bottom: 24px;
  }

  @media ${device.mobile} {
    padding: 0 16px;
  }
`;
export const ModalFooter = styled.div`
  display: flex;

  width: 100%;
  justify-content: space-between;

  .${ButtonSecondary.styledComponentId} {
    width: 50%;
    margin-right: 8px;
  }

  .${ButtonPrimary.styledComponentId} {
    width: 50%;
    margin-left: 8px;
  }
`;
