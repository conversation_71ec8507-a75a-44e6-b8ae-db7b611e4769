import Modal from "components/modal";
import { ButtonPrimary, ButtonSecondary } from "styles/buttons";
import { ConfirmContentWrapper, ModalFooter } from "./styled";

export interface IConfirmModalDetailProps {
  show: boolean;
  title: string;
  content: string;
  confirmTitle?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
}

const ConfirmModal = ({
  show,
  title,
  content,
  confirmTitle = "Lưu",
  onCancel,
  onConfirm,
}: IConfirmModalDetailProps) => {
  if (!show) {
    return;
  }

  return (
    <Modal show={show} size="sm" title={title} titleAlign="left" onClose={onCancel}>
      <ConfirmContentWrapper>
        <p>{content}</p>

        <ModalFooter>
          <ButtonSecondary onClick={onCancel}>Hủy bỏ</ButtonSecondary>
          <ButtonPrimary onClick={onConfirm}>{confirmTitle}</ButtonPrimary>
        </ModalFooter>
      </ConfirmContentWrapper>
    </Modal>
  );
};

export default ConfirmModal;
