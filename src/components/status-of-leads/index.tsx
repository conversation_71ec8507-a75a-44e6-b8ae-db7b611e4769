export type StatusLeads = "contacted" | "consider" | "appointed" | "solution" | "list-customer" | "submitted";
export interface StatusProps {
  className?: string;
  status: StatusLeads;
}

const Status = ({ status, className }: StatusProps) => {
  // switch (status) {
  //   case "contacted":
  //     return (
  //       <Contacted className={className}>
  //         <Icons icon="phone-line" />
  //         <label className="label-5">Đ<PERSON> kết nối</label>
  //       </Contacted>
  //     );
  //   case "appointed":
  //     return (
  //       <Appointed className={className}>
  //         <Icons icon="group" />
  //         <label className="label-5">C<PERSON>ộ<PERSON> hẹn</label>
  //       </Appointed>
  //     );
  //   case "solution":
  //     return (
  //       <Solution className={className}>
  //         <Icons icon="add-form" />
  //         <label className="label-5">Tr<PERSON><PERSON> bày giải pháp</label>
  //       </Solution>
  //     );
  //   case "consider":
  //     return (
  //       <Consider className={className}>
  //         <Icons icon="consider" />
  //         <label className="label-5"><PERSON><PERSON> nh<PERSON></label>
  //       </Consider>
  //     );
  //   case "list-customer":
  //     return (
  //       <ListCustomer className={className}>
  //         <Icons icon="user-manager" />
  //         <label className="label-5">Danh sách KH</label>
  //       </ListCustomer>
  //     );
  //   case "submitted":
  //     return (
  //       <Submitted className={className}>
  //         <Icons icon="tick-leads" />
  //         <label className="label-5">Đã nộp</label>
  //       </Submitted>
  //     );
  //   default:
  //     return null;
  // }
};

export default Status;
