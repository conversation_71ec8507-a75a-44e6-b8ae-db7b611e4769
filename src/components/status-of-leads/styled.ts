import styled, { css } from "styled-components";
import { device } from "styles/media";

const DefaultTag = css`
  height: auto;
  align-items: center;
  display: flex;
  font-weight: 400;
  font-size: 12px;
  line-height: 150%;

  text-align: left;
  padding: 2px 8px;
  border-radius: 4px;
  color: #ffffff;

  Label {
    margin-left: 4px;
  }

  svg {
    width: 16px;
  }

  @media ${device.mobile} {
    display: inline-flex;

    label {
      margin-bottom: 0px;
    }
  }
`;

export const Contacted = styled.div`
  ${DefaultTag}
  background: ${({ theme }) => theme.color.status.dark_green_20};
  color: ${({ theme }) => theme.color.status.dark_green};

  svg {
    path {
      fill: ${({ theme }) => theme.color.status.dark_green};
    }
  }
`;

export const Solution = styled.div`
  ${DefaultTag}
  background: ${({ theme }) => theme.color.status.blue_20};
  color: ${({ theme }) => theme.color.status.blue};
`;

export const Appointed = styled.div`
  ${DefaultTag};
  background: ${({ theme }) => theme.color.status.light_green_20};
  color: ${({ theme }) => theme.color.status.light_green};
`;

export const ListCustomer = styled.div`
  ${DefaultTag};
  background: ${({ theme }) => theme.color.status.primary_20};
  color: ${({ theme }) => theme.color.status.primary};

  svg {
    path {
      fill: ${({ theme }) => theme.color.status.primary};
    }
  }
`;

export const Submitted = styled.div`
  ${DefaultTag};
  background: ${({ theme }) => theme.color.status.green_20};
  color: ${({ theme }) => theme.color.status.green};
`;

export const Consider = styled.div`
  ${DefaultTag};
  background: ${({ theme }) => theme.color.status.yellow_20};
  color: ${({ theme }) => theme.color.status.yellow};
`;
