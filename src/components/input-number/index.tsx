import { HelpTextProps } from "@custom-types";
import Icons from "components/icons";
import { useEffect, useRef, useState } from "react";
import { NumericFormat } from "react-number-format";
import { CountNow, FieldSetWrapper, HelpText, InputWrapper, MaxCount, TextCount, WrapperHelpText } from "./styled";
import { Error, Label, Warning, WrapperError } from "styles/input-styled";

interface InputNumberProps {
  valueType?: "text" | "number" | "pattern";
  value: string | number;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string | boolean;
  warning?: string;
  maxLength?: number;
  helpText?: HelpTextProps;
  allowNegative?: boolean;
  allowLeadingZeros?: boolean;
  isSeparator?: boolean;
  onBlur?(): void;
  onChange?(value: string | number): void;
}

function InputNumber({
  valueType,
  value,
  placeholder,
  required = false,
  disabled = false,
  error,
  warning,
  helpText,
  maxLength,
  allowNegative, // chặn số âm = false
  allowLeadingZeros = false, //cho phép số 0 ở đầu
  isSeparator = true,
  onChange,
  onBlur,
}: InputNumberProps) {
  const [focus, setFocus] = useState(false);

  const refInput = useRef(null);

  useEffect(() => {
    const ref = refInput.current;

    const handel = () => {
      setFocus(true);
    };

    ref.current?.addEventListener?.("focus", handel);

    return () => {
      ref.current?.removeEventListener?.("focus", handel);
    };
  }, []);

  useEffect(() => {
    if (focus) {
      onChange(value || "");
    }
  }, [focus, onChange, value]);

  const handleBlur = () => {
    onChange?.(value || "");
    setFocus(false);
    onBlur?.();
  };

  return (
    <FieldSetWrapper>
      <InputWrapper disabled={disabled} active={focus} error={!!error}>
        <NumericFormat
          disabled={disabled}
          value={value}
          decimalSeparator=","
          thousandSeparator={isSeparator ? "." : false}
          getInputRef={refInput}
          allowLeadingZeros={allowLeadingZeros}
          allowNegative={allowNegative}
          onValueChange={(value) =>
            onChange(
              valueType === "text" ? value.value : valueType === "pattern" ? value.formattedValue : value.floatValue
            )
          }
          onBlur={handleBlur}
          maxLength={maxLength} //nếu thêm maxLength phải cộng thêm số lượng dấu chấm
        />
        {placeholder && (
          <Label error={error} focus={focus} required={required} disabled={disabled} haveValue={value !== ""}>
            {placeholder}
            {required && <span>*</span>}
          </Label>
        )}
      </InputWrapper>
      {helpText && (
        <WrapperHelpText disabled={disabled}>
          <HelpText>{helpText.content}</HelpText>
          <TextCount>
            <CountNow>{helpText.countNow}</CountNow>/<MaxCount>{helpText.maxCount}</MaxCount>
          </TextCount>
        </WrapperHelpText>
      )}
      {error && (
        <WrapperError>
          <Icons icon="error-icon" />
          <Error>{error}</Error>
        </WrapperError>
      )}
      {warning && (
        <WrapperError>
          <Icons icon="icon-warning" />
          <Warning>{warning}</Warning>
        </WrapperError>
      )}
    </FieldSetWrapper>
  );
}

export default InputNumber;
