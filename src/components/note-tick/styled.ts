import styled from "styled-components";
import { device } from "styles/media";

export const NoteTickWrapper = styled.div`
  // @media ${device.mobile} {
  //   padding: 16px 0;
  // }
`;

export const NoteContent = styled.div`
  background-color: ${({ theme }) => theme.color.status.grey_50};
  border-radius: 8px;
  padding: 16px;

  & > div:first-child {
    margin-top: 0;
  }

  & > div:not(:first-child) {
    margin-top: 12px;
  }

  ul {
    margin-top: 8px;
  }

  h6 {
    color: unset;
  }
`;

export const NoteItem = styled.div`
  display: flex;
  align-items: center;
  margin-top: 10px !important;
  svg {
    width: 12px;
    height: 12px;
    flex-shrink: 0;
  }

  p {
    font-weight: 500;
    margin-left: 10px;
  }

  @media ${device.mobile} {
    align-items: baseline;
  }
`;
