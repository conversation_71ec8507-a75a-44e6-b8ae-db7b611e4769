import styled from "styled-components";

export const InputSearchWrapper = styled.div<{ active: boolean }>`
  display: flex;
  border: 1px solid ${({ active }) => (active ? "#e87722" : "#DBDFE1")};
  width: 100%;
  margin: 0;

  position: relative;

  border: 1px solid ${({ active }) => (active ? "#e87722" : "#DBDFE1")};
  border-radius: 4px;
  background-color: #ffffff;

  caret-color: ${({ theme }) => theme.color.status.primary};

  :hover {
    border: 1px solid ${({ theme, active }) => (active ? theme.color.text.primary : theme.color.text.placeholder)};
  }
  transition: all 0.3s ease;

  align-items: center;
  position: relative;

  input {
    padding: 0px;
    width: 99%;
    height: 40px !important;

    overflow: hidden;
    text-overflow: ellipsis;

    :focus::placeholder {
      color: transparent;
    }
  }
  svg {
    margin-left: 16px;
    min-width: 24px;
  }
`;

export const CustomInputNumber = styled.div`
  width: 100%;
  input {
    font-weight: 400;
  }
`;
