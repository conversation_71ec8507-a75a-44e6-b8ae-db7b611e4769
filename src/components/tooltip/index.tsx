import Icons, { IconType } from "components/icons";
import React, { ReactChild } from "react";
import BasicTooltipTrigger from "./basic-tooltip-trigger";

interface PopperTooltipProps {
  children: ReactChild | ReactChild[];
  icon: IconType;
  iconStyle?: any;
  placement?: "top" | "bottom";
}

function PopperTooltip({ children, icon, iconStyle, placement = "top" }: PopperTooltipProps) {
  return (
    <BasicTooltipTrigger trigger={["hover"]} placement={placement} tooltip={children}>
      <Icons icon={icon} {...iconStyle} />
    </BasicTooltipTrigger>
  );
}

export default PopperTooltip;
