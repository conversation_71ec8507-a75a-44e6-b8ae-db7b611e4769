import { TableNodeConfig } from "@custom-types/config-table";
import ScrollBar from "components/scroll-bar";
import { get, range } from "lodash";
import { useEffect, useMemo, useRef, useState } from "react";
import { formatValue } from "services/format-value";
import { TBODY } from "styles/table";
import ItemDesktop from "./item-desktop";
import { Node, TDCustom, THCustomStick, THEADCustom, TRCustom, TableDesktopWrapper, TableTreeCustom } from "./styled";

interface TableDesktop {
  config: TableNodeConfig;
  data: any;
  position?: number;
  stickyIndex: number;
}

const TableDesktop = ({ config, data, stickyIndex }: TableDesktop) => {
  const [offsetLeftLists, setOffsetLeftLists] = useState<number[]>([]);
  const thRef = useRef(null);

  const { header, body } = config;

  const defaultCols = useMemo(() => {
    return body?.filter((item) => !item.isTitle);
  }, [body]); //defaultCol fix

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    if (thRef.current) {
      timeoutId = setTimeout(() => {
        const list = range(0, stickyIndex).map((item) => {
          return get(thRef.current, ["firstChild", "children", item, "offsetWidth"]);
        });
        setOffsetLeftLists(list);
      }, 0);
    }

    return () => clearTimeout(timeoutId);
  }, [thRef, stickyIndex, data]);

  return (
    <TableDesktopWrapper className="hide-mobile">
      <ScrollBar>
        <TableTreeCustom offsetLeftLists={data && offsetLeftLists}>
          <THEADCustom ref={thRef}>
            {header.map((row, rowIndex) => {
              return (
                <TRCustom key={rowIndex}>
                  {row.map((col, colIndex) => (
                    <THCustomStick
                      offsetLeftLists={data && offsetLeftLists}
                      key={colIndex}
                      rowSpan={col.rowSpan}
                      colSpan={col.colSpan}
                      textAlign={col.textAlign}
                      isSticky={col.isSticky}
                      rowIndex={rowIndex}
                    >
                      {formatValue(col.label, col.type)}
                    </THCustomStick>
                  ))}
                </TRCustom>
              );
            })}
          </THEADCustom>
          <TBODY>
            {data?.length ? (
              data?.map((item: any, index: number) => (
                <ItemDesktop offsetLeftLists={offsetLeftLists} key={index} item={item} body={body} />
              ))
            ) : (
              <TRCustom>
                {defaultCols.map((item, index) => (
                  <TDCustom key={index} offsetLeftLists={offsetLeftLists} isSticky={item.isSticky}>
                    <Node>
                      <label className="label-4">-</label>
                    </Node>
                  </TDCustom>
                ))}
              </TRCustom>
            )}
          </TBODY>
        </TableTreeCustom>
      </ScrollBar>
    </TableDesktopWrapper>
  );
};

export default TableDesktop;
