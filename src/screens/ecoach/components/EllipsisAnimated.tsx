import styled, { keyframes } from "styled-components";

const bounceAnimation = keyframes`
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8,0,1,1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0,0,0.2,1);
  }
`;

export const EllipsisAnimated = styled.div`
  height: 2.5rem;
  margin-top: 0.3rem;
  margin-right: 0.3rem;

  animation: ${bounceAnimation};
  animation-duration: 1s;
  animation-iteration-count: infinite;
`;
