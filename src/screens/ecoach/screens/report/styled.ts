import styled from "styled-components";

export const Container = styled.main`
  min-height: calc(100vh - 56px);
  background-color: #fff;
`;

export const MainLoading = styled.main`
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 1536px;
  margin-left: auto;
  margin-right: auto;
`;

export const MainContentLoading = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  background: white;
`;

export const Main = styled.main`
  min-height: calc(100vh - 56px);
  background-color: #fffbf6;
`;

export const MainContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 1536px;
  margin-left: auto;
  margin-right: auto;
`;

export const MainContentInner = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  position: relative;
`;

export const BtnGroupContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
`;
