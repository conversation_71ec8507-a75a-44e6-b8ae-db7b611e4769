import { ValueLabel } from "@custom-types";
import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { getDateFrom } from "services/untils";

export interface IncomeGAForm {
  month: string;
  period: ValueLabel;
}

interface PerformanceSlice {
  incomeGAForm: IncomeGAForm;
}
const initialState: PerformanceSlice = {
  incomeGAForm: {
    month: getDateFrom("MM/YYYY"),
    period: { value: "", label: "" },
  },
};

const gaSlice = createSlice({
  name: "GA",
  initialState: initialState,
  reducers: {
    clearGASlice: () => {
      return initialState;
    },
    setIncomeGAForm: (state, action: PayloadAction<IncomeGAForm>) => {
      state.incomeGAForm = action.payload;
    },
  },
});

export const { clearGASlice, setIncomeGAForm } = gaSlice.actions;

export default gaSlice.reducer;
