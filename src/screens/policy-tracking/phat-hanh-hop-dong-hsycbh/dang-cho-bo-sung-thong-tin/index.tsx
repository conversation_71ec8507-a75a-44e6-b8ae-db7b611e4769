import { EMPTY_DATA, ERROR, ERROR_API_MESSAGE, WARNING } from "@constants/message";
import { DataTable, TableConfig } from "@custom-types/config-table";
import { DownloadAwlpInput, ErrorPolicy, PendingItem, PolicyInput, PolicyOutput } from "@custom-types/policy-tracking";
import { downloadAwpl, getPendingItem } from "api/policy-tracking";
import { Alert } from "components/alert";
import Icons from "components/icons";
import PolicyContent from "components/policy-content";
import useActionApi from "hooks/use-action-api";
import useIsGroup from "hooks/use-is-group";
import { useAppSelector } from "hooks/use-redux";
import { cloneDeep } from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import { setTriggerCallAPIPolicy } from "screens/home/<USER>";
import { dateTypePendingList } from "screens/policy-tracking/constants";
import { setEPending, setPolicyDetail } from "screens/policy-tracking/slice";
import EPendingModal from "sections/policy-tracking/e-pending-modal";
import { ButtonUploadMobile } from "sections/policy-tracking/e-pending-modal/styled";
import DownloadAwplModal from "sections/policy-tracking/phat-hanh-hop-dong-hsycbh/dang-cho-bo-sung-thong-tin/download-awpl";
import FormSearchPending from "sections/policy-tracking/phat-hanh-hop-dong-hsycbh/dang-cho-bo-sung-thong-tin/form-search";
import PolicyTable from "sections/policy-tracking/policy-table";
import { handleValidatePolicyForm } from "sections/policy-tracking/validate-search-policy";
import { formatValueTable } from "services/format-value";
import { formatDateRequest, getDateFrom, getDateTo } from "services/untils";
import { FlexBox, PolicyNo } from "styles";
import { ButtonIcon } from "styles/buttons";

const InformationPending = () => {
  const [formSearch, setFormSearch] = useState<PolicyInput>({
    agentId: "",
    advancePolicyNo: "",
    fromDate: getDateFrom(),
    toDate: getDateTo(),
    group: "",
    dateType: dateTypePendingList.find((item) => item.value === "PROPOSALENTRYDATE"),
  });
  const [error, setError] = useState<ErrorPolicy>({
    fromDate: null,
    toDate: null,
  });
  const [showModal, setShowModal] = useState({
    show: false,
    data: null,
  });
  const [openQuickSearch, setOpenQuickSearch] = useState(false);
  const [dataPendingItem, setDataPendingItem] = useState<PendingItem[]>([]);

  const dispatch = useDispatch();
  const isGroup = useIsGroup();

  const { username } = useAppSelector((state) => state.rootReducer.user);
  const {
    currentAgentOfTeam: { agentCode },
  } = useAppSelector((state) => state.teamPerformanceReducer);
  const { triggerCallAPIPolicy } = useAppSelector((state) => state.homeReducer);

  const actionGetPendingItem = useActionApi<PolicyInput, PolicyOutput<PendingItem>>(getPendingItem);
  const actionDownloadAwpl = useActionApi<DownloadAwlpInput, Blob>(downloadAwpl);

  const configTable: TableConfig[] = useMemo(() => {
    return isGroup
      ? [
          { key: "agentCode", show: true, type: "string", label: "Mã TVTC" },
          { key: "agentName", show: true, type: "string", label: "Tên TVTC" },
          { key: "policyNo", show: true, type: "string", label: "Số hợp đồng" },
          { key: "installPremium", show: true, type: "number", label: "Phí BH thực nộp" },
          { key: "apeAmount", show: true, type: "number", label: "Phí BH quy năm (APE)" },
          { key: "submitDate", show: true, type: "date", label: "Ngày nộp HSYCBH", sort: true },
          { key: "effDate", show: true, type: "date", label: "Ngày gửi YC bổ sung", sort: true },
          // { key: "deadLine", show: true, type: "date", label: "Ngày hết hạn bổ sung thông tin thẩm định", sort: true },
          { key: "lstawplindex", show: true, type: "string", label: "Thư thông báo thẩm định" },
          { key: "fullnameOW", show: true, type: "string", label: "Bên mua BH" },
          { key: "pendCode", show: true, type: "string", label: "Thông tin yêu cầu bổ sung" },
        ]
      : [
          { key: "policyNo", show: true, type: "string", label: "Số hợp đồng" },
          { key: "installPremium", show: true, type: "number", label: "Phí BH thực nộp" },
          { key: "apeAmount", show: true, type: "number", label: "Phí BH quy năm (APE)" },
          { key: "submitDate", show: true, type: "date", label: "Ngày nộp HSYCBH", sort: true },
          { key: "effDate", show: true, type: "date", label: "Ngày gửi YC bổ sung", sort: true },
          // { key: "deadLine", show: true, type: "date", label: "Ngày hết hạn bổ sung thông tin thẩm định", sort: true },
          { key: "lstawplindex", show: true, type: "string", label: "Thư thông báo thẩm định" },
          { key: "fullnameOW", show: true, type: "string", label: "Bên mua BH" },
          { key: "pendCode", show: true, type: "string", label: "Thông tin yêu cầu bổ sung" },
          { key: "upload", show: true, type: "function", label: "Bổ sung", sticky: "right" },
        ];
  }, [isGroup]);

  const handleShowDetailPolicy = (data: PendingItem) => {
    if (isGroup) {
      return;
    }

    dispatch(
      setPolicyDetail({
        data: {
          agentId: data.agentCode,
          policyNo: data.policyNo,
        },
      })
    );
  };

  const handleShowDetailLetter = (rowData: PendingItem) => {
    setShowModal({
      show: true,
      data: rowData.lstawplindex,
    });
  };

  const formatValueTablePolicy = (data: PendingItem, config: TableConfig) => {
    switch (config.key) {
      case "lstawplindex":
        return (
          <PolicyNo primary onClick={() => handleShowDetailLetter(data)}>
            Thư báo
          </PolicyNo>
        );
      case "policyNo":
        return (
          <PolicyNo primary={!isGroup} onClick={() => handleShowDetailPolicy(data)}>
            {data[config.key]}
          </PolicyNo>
        );
      case "upload": {
        return (
          <FlexBox justifyContent="center">
            <ButtonIcon onClick={() => handleShowEPending(data)}>
              <Icons icon="icon-upload" />
            </ButtonIcon>
          </FlexBox>
        );
      }
      default:
        return formatValueTable(data, config);
    }
  };

  const renderFuncHeaderMobile = (data: PendingItem) => {
    return (
      <ButtonUploadMobile size="tiny" withIcon onClick={() => handleShowEPending(data)}>
        <Icons icon="icon-upload" />
        Bổ sung
      </ButtonUploadMobile>
    );
  };

  const dataTable: DataTable[][] = useMemo(
    () =>
      dataPendingItem.map((d: any) =>
        configTable.map((config) => ({
          config: config,
          node: formatValueTablePolicy(d, config),
          originData: d[config.key],
          renderFuncHeaderMobile: config.type === "function" ? renderFuncHeaderMobile(d) : null,
        }))
      ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dataPendingItem, configTable]
  );

  //onChange & validate
  const disableSubmit = useMemo(() => {
    return Object.values(error).findIndex((value) => value) > -1;
  }, [error]);

  const handleChangeData = (name: string, value: string) => {
    setFormSearch((pre) => ({
      ...pre,
      [name]: value,
    }));

    if (name === "fromDate" || name === "toDate") {
      const newError = handleValidatePolicyForm(name, value, formSearch.fromDate);
      setError((pre: any) => ({ ...pre, ...newError }));
    }
  };

  const handleBlurData = (name: string, valueDateFrom: string) => {
    const newError = handleValidatePolicyForm(name, formSearch.toDate, valueDateFrom);
    setError((pre: any) => ({ ...pre, ...newError }));
  };

  //Đối với QL Nhóm, Bất đồng bộ vì phải lấy agentCode from Hierarchy(call API) nên cần truyền agentId vào handleSubmit
  const handleSubmit = useCallback(
    (agentId: string) => {
      if (!agentId) {
        return;
      }

      const payload = cloneDeep(formSearch);

      payload.agentId = agentId;
      payload.dateType = formSearch.dateType.value;
      payload.fromDate = formatDateRequest(formSearch.fromDate);
      payload.toDate = formatDateRequest(formSearch.toDate);
      payload.group = isGroup ? "1" : "";

      actionGetPendingItem({
        body: payload,
        loading: {
          type: "local",
          name: "getPolicyTrackingLoading",
        },
      })
        .then(({ data }) => {
          setDataPendingItem(data.listData);

          if (!data.listData.length) {
            Alert(WARNING, EMPTY_DATA);
          }
        })
        .catch((error) => {
          console.log("error", error);
          Alert(ERROR, ERROR_API_MESSAGE);
        });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [formSearch, isGroup]
  );

  //auto call API when redirect from home-page
  useEffect(() => {
    if (triggerCallAPIPolicy && username) {
      handleSubmit(username);
    }

    const cleanup = setTimeout(() => {
      dispatch(setTriggerCallAPIPolicy(false));
    }, 500);

    return () => clearTimeout(cleanup);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [triggerCallAPIPolicy, username, handleSubmit]);

  const handleOpenQuickSearch = (value: boolean) => {
    setOpenQuickSearch(value);
  };

  const handleDownloadAwpl = useCallback(
    (value: string) => {
      actionDownloadAwpl({
        body: { agentId: username, awplIndex: value },
        loading: {
          type: "global",
          name: "downloadAwplLoading",
        },
      })
        .then(({ data }) => {
          const url = window.URL.createObjectURL(data);
          var link = document.createElement("a");
          link.href = url;
          link.download = `thu-yeu-cau-bo-sung-${value}.pdf`;
          link.click();
        })
        .catch((error) => {
          console.log("error", error);
          Alert(ERROR, ERROR_API_MESSAGE);
        });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [username]
  );

  const handleCloseModal = () => {
    setShowModal({
      show: false,
      data: [],
    });
  };

  const handleShowEPending = (data: PendingItem) => {
    dispatch(
      setEPending({
        show: false,
        data: data,
      })
    );
  };

  return (
    <PolicyContent
      title="HSYCBH đang chờ bổ sung thông tin"
      openQuickSearch={openQuickSearch}
      onOpenQuickSearch={handleOpenQuickSearch}
    >
      <FormSearchPending
        showHierarchy={isGroup}
        disableSubmit={disableSubmit}
        error={error}
        onBlurData={handleBlurData}
        formSearch={formSearch}
        onChangeData={handleChangeData}
        onSubmit={() => handleSubmit(isGroup ? agentCode : username)} // agentCode get from  currentAgentOfTeam(Hierarchy tree)
      />
      <PolicyTable data={dataTable} config={configTable} />

      <DownloadAwplModal
        show={showModal.show}
        data={showModal.data}
        onClose={handleCloseModal}
        onDownload={handleDownloadAwpl}
      />
      <EPendingModal />
    </PolicyContent>
  );
};

export default InformationPending;
