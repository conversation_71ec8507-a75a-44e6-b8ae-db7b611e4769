import { EMPTY_DATA, ERROR_API_MESSAGE } from "@constants/message";
import { DataTable, TableConfig } from "@custom-types/config-table";
import {
  CustomerConfirmData,
  CustomerConfirmInput,
  CustomerConfirmOutput,
  CustomerResentSmsInput,
  ErrorPolicy,
  PolicyInput,
} from "@custom-types/policy-tracking";
import { customerResentSms, getListCustomerConfirm } from "api/policy-tracking";
import { Alert } from "components/alert";
import PolicyContent from "components/policy-content";
import Status from "components/status";
import useActionApi from "hooks/use-action-api";
import useIsGroup from "hooks/use-is-group";
import { useAppSelector } from "hooks/use-redux";
import useWindowResize from "hooks/use-window-resize";
import { useCallback, useMemo, useState } from "react";
import { customerConfirmTypeList } from "screens/policy-tracking/constants";
import FormSearchCustomerConfirm from "sections/policy-tracking/phat-hanh-hop-dong-hsycbh/khach-hang-xac-nhan-tt/form-search";
import PolicyTable from "sections/policy-tracking/policy-table";
import { handleValidatePolicyForm } from "sections/policy-tracking/validate-search-policy";
import { formatValueTable } from "services/format-value";
import { formatDateRequest, getDateFrom, getDateTo } from "services/untils";
import { FlexBox } from "styles";
import { ButtonSecondary } from "styles/buttons";

type TableStatusKey = "CANCELNCF" | "CANCELCFR" | "WAITINGCF";

const tableStatus = {
  CANCELNCF: {
    color: {
      text: "#E87722",
      background: "#FAE4D3",
      border: "#E87722",
    },
    label: "Hủy HS do KH không xác nhận",
  },
  CANCELCFR: {
    color: {
      text: "#FFFFFF",
      background: "#B30909",
    },
    label: "Hủy HS do KQ xác nhận của KH",
  },
  WAITINGCF: {
    color: {
      text: "#FFFFFF",
      background: "#FED141",
    },
    label: "Chờ KH xác nhận",
  },
};

const CustomerConfirmScreen = () => {
  const size = useWindowResize();
  const [dataList, setDataList] = useState<CustomerConfirmData[]>([]);
  const [formSearch, setFormSearch] = useState<Partial<PolicyInput>>({
    fromDate: getDateFrom(),
    toDate: getDateTo(),
    dateType: customerConfirmTypeList.find((item) => item.value === "PROPOSALENTRYDATE"),
  });
  const [error, setError] = useState<ErrorPolicy>({
    fromDate: null,
    toDate: null,
  });

  const isGroup = useIsGroup();
  const { username } = useAppSelector((state) => state.rootReducer.user);
  const {
    currentAgentOfTeam: { agentCode },
  } = useAppSelector((state) => state.teamPerformanceReducer);
  const actionGetListCustomerConfirm = useActionApi<CustomerConfirmInput, CustomerConfirmOutput>(
    getListCustomerConfirm
  );
  const actionCustomerResentSms = useActionApi<CustomerResentSmsInput, CustomerConfirmOutput>(customerResentSms);

  const configTable: TableConfig[] = useMemo(() => {
    return isGroup
      ? [
          { key: "agentCode", show: true, type: "string", label: "Mã quản lý" },
          { key: "lineManerName", show: true, type: "string", label: "Tên quản lý" },
          { key: "proposalNum", show: true, type: "string", label: "Số HSYCBH" },
          { key: "productName", show: true, type: "string", label: "Tên sản phẩm" },
          { key: "poName", show: true, type: "string", label: "Bên mua BH" },
          { key: "mainLAName", show: true, type: "string", label: "Người được BH" },
          { key: "proposalReceiveddt", show: true, type: "date", label: "Ngày nộp HSYCBH", sort: true },
          { key: "totalPremium", show: true, type: "number", label: "Phí BH" },
          { key: "status", show: true, type: "string", label: "Trạng thái" },
        ]
      : [
          { key: "lineManerCode", show: true, type: "string", label: "Mã quản lý" },
          { key: "lineManerName", show: true, type: "string", label: "Tên quản lý" },
          { key: "proposalNum", show: true, type: "string", label: "Số HSYCBH" },
          { key: "productName", show: true, type: "string", label: "Tên sản phẩm" },
          { key: "poName", show: true, type: "string", label: "Bên mua BH" },
          { key: "mainLAName", show: true, type: "string", label: "Người được BH" },
          { key: "proposalReceiveddt", show: true, type: "date", label: "Ngày nộp HSYCBH", sort: true },
          { key: "totalPremium", show: true, type: "number", label: "Phí BH" },
          { key: "status", show: true, type: "string", label: "Trạng thái" },
          { key: "action", show: true, type: "string", label: "Chức năng" },
        ];
  }, [isGroup]);

  const handleResendSms = (data: CustomerConfirmData) => {
    actionCustomerResentSms({
      body: {
        seq: data.quotationSeq,
      },
      loading: {
        name: "resendSmsLoading",
        type: "global",
      },
    })
      .then(({ data }) => {
        if (data.status == "SUCCESS") {
          Alert("SUCCESSFUL", "Gửi lại SMS thành công");
        } else if (data.status == "SMSOVER") {
          Alert("WARNING", "Không thể gửi lại SMS do đã vượt quá số lần cho phép");
        } else {
          Alert("ERROR", ERROR_API_MESSAGE);
        }
      })
      .catch((err) => {
        Alert("ERROR", ERROR_API_MESSAGE);
      });
  };

  const formatValueTablePolicy = (data: any, config: TableConfig) => {
    switch (config.key) {
      case "status":
        const statusKey = data[config.key] as TableStatusKey;
        return <Status color={tableStatus[statusKey]?.color} label={tableStatus[statusKey]?.label} />;
      case "action":
        if (data["status"] == "WAITINGCF") {
          return (
            <FlexBox className="sm-mb-16">
              <ButtonSecondary maxWidth size="tiny" onClick={() => handleResendSms(data)}>
                Gửi lại SMS
              </ButtonSecondary>
            </FlexBox>
          );
        } else if (size.width > 786) {
          return;
        }

      default:
        return formatValueTable(data, config);
    }
  };

  const dataTable: DataTable[][] = useMemo(() => {
    return (
      dataList?.map((d: any) =>
        configTable.map((config) => ({
          config: config,
          node: formatValueTablePolicy(d, config),
          originData: d[config.key],
        }))
      ) ?? []
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataList, configTable]);

  //onChange & validate
  const disableSubmit = useMemo(() => {
    return Object.values(error).findIndex((value) => value) > -1;
  }, [error]);

  const handleChangeData = (name: string, value: string) => {
    setFormSearch((pre) => ({
      ...pre,
      [name]: value,
    }));

    if (name === "fromDate" || name === "toDate") {
      const newError = handleValidatePolicyForm(name, value, formSearch.fromDate);
      setError((pre: any) => ({ ...pre, ...newError }));
    }
  };

  const handleBlurData = (name: string, valueDateFrom: string) => {
    const newError = handleValidatePolicyForm(name, formSearch.toDate, valueDateFrom);
    setError((pre: any) => ({ ...pre, ...newError }));
  };

  //Đối với QL Nhóm, Bất đồng bộ vì phải lấy agentCode from Hierarchy(call API) nên cần truyền agentId vào handleSubmit
  const handleSubmit = useCallback(
    (agentId: string) => {
      if (!agentId) {
        return;
      }

      actionGetListCustomerConfirm({
        body: {
          agentId: agentId,
          proposalDateFrom: formatDateRequest(formSearch.fromDate),
          proposalDateTo: formatDateRequest(formSearch.toDate),
          group: isGroup ? "1" : "",
        },
        loading: {
          name: "getPolicyTrackingLoading",
          type: "local",
        },
      })
        .then(({ data }) => {
          setDataList(data?.data[0]);
          if (!data?.data[0]?.length) {
            Alert("WARNING", EMPTY_DATA);
          }
        })
        .catch((err) => {
          console.log(err);
          Alert("ERROR", ERROR_API_MESSAGE);
        });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [formSearch, isGroup]
  );

  return (
    <PolicyContent title="Khách hàng xác nhận thông tin" showQuickSearch={false}>
      <FormSearchCustomerConfirm
        showHierarchy={isGroup}
        disableSubmit={disableSubmit}
        error={error}
        onBlurData={handleBlurData}
        formSearch={formSearch}
        onChangeData={handleChangeData}
        onSubmit={() => handleSubmit(isGroup ? agentCode : username)} // agentCode get from  currentAgentOfTeam(Hierarchy tree)
      />
      <PolicyTable data={dataTable} config={configTable} />
    </PolicyContent>
  );
};

export default CustomerConfirmScreen;
