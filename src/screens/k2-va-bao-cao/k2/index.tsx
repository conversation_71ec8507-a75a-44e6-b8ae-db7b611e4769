import { ChannelType } from "@custom-types/user";
import { useAppSelector } from "hooks/use-redux";
import { useMemo } from "react";
import AgencyK2 from "sections/k2-va-bao-cao/k2/agency";
import BancaK2 from "sections/k2-va-bao-cao/k2/banca";
import IfaK2 from "sections/k2-va-bao-cao/k2/ifa";
import { TitlePage } from "styles";
import { K2Wrapper } from "./styled";

interface K2Props {}

export function formatDataSummaryK2(data: any, channel?: ChannelType) {
  lastElement(data);
  function spreadObject(list: any) {
    list?.forEach((item: any) => {
      const data = item.data;

      item.agentId = data?.code || data?.agentId; // if IFA data.agentId, else: data.code
      item.agencyId = data?.agencyId;
      item.agentName = data?.name || data?.agentName; // if IFA: data.agentName, else: data.name
      item.ap2 = data?.ap2;
      item.designationCd = data?.designationCd;
      item.ep2 = data?.ep2;
      item.k2Ratio = data?.rate || data?.k2Ratio; // if IFA: data.k2Ratio, else: data.rate
      item.k2_55 = data?.k2_55;
      item.k2_60 = data?.k2_60;
      item.k2_65 = data?.k2_65;
      item.k2_70 = data?.k2_70;
      item.k2_75 = data?.k2_75;
      item.k2_80 = data?.k2_80;
      item.k2_85 = data?.k2_85;
      item.k2_90 = data?.k2_90;
      item.k2_95 = data?.k2_95;
      item.parentId = data?.parentId;
      item.type = data?.type;
      item.yyyymm = data?.yyyymm;

      if (channel === "BROKER") {
        item.agentInfo = `${item.agentId} - ${item.agentName}`;
      } else {
        item.agentInfo = ` ${item.agentId} - ${item.designationCd} - ${item.agentName}`;
      }

      delete item.data;
      delete item.expanded;
      delete item.status;

      if (list) spreadObject(item.children);
    });
  }
  spreadObject(data);

  return data;
}

export function lastElement(data: any): void {
  let lastItemArr: Array<any>;
  if (data?.length > 0) {
    data?.forEach((item: any) => {
      if (item.children.length > 0) {
        item.isHaveChildren = 1;
        lastElement(item.children);
        if (item.isHaveChildren === 1) {
          lastItemArr = item.children.slice(-1);
          lastItemArr.forEach((item: any) => {
            item.isLastChild = true;
          });
        }
      }
    });
  }
}

const K2 = ({}: K2Props) => {
  const {
    user: { channel },
  } = useAppSelector((state) => state.rootReducer);

  const Content = useMemo(() => {
    switch (channel) {
      case "AGENCY":
        return AgencyK2;
      case "BROKER":
        return IfaK2;
      case "BANCA":
        return BancaK2;
      default:
        return null;
    }
  }, [channel]);

  return (
    <K2Wrapper>
      <TitlePage>Tỷ lệ duy trì K2</TitlePage>
      {Content ? <Content /> : null}
    </K2Wrapper>
  );
};

export default K2;
