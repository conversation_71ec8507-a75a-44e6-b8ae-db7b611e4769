import { LoadingProps } from "@custom-types/loading";
import {
  PerformanceDetailInput,
  PerformanceDetailYearly,
  PerformanceType,
  SalesPersonalInput,
} from "@custom-types/performance";
import { SOAInput, SOAMonthlyAllowanceType, SOAPolicyRewardType, SOAType } from "@custom-types/soa";
import { createAction } from "@reduxjs/toolkit";
import {
  GET_CHART_SALES_PERSONAL_BY_MONTH,
  GET_PERFORMANCE_DETAIL_PERSONAL,
  GET_SOA,
  GET_SOA_MONTHLY_ALLOWANCE_DETAIL,
  GET_SOA_POLICY_REWARD_DETAIL,
} from "./saga";

export interface PerformanceDetailPersonalAction {
  payload: PerformanceDetailInput;
  type: PerformanceType;
  loading: LoadingProps;
  callback?: (data: any) => void;
}

export interface SalesChartByMonthAction {
  payload: SalesPersonalInput;
  loading: LoadingProps;
  dataDetail?: PerformanceDetailYearly;
  callback?: (data: any) => void;
}

export const getPerformanceDetailPersonalAction = createAction(
  GET_PERFORMANCE_DETAIL_PERSONAL,
  (payload: PerformanceDetailPersonalAction) => ({
    payload,
  })
);

export const getSalesChartByMonthAction = createAction(
  GET_CHART_SALES_PERSONAL_BY_MONTH,
  (payload: SalesChartByMonthAction) => ({
    payload,
  })
);

//SOA

export interface SOAInputAction {
  payload: SOAInput;
  type: SOAType;
  callback?: (data: any) => void;
}

export const getSOAAction = createAction(GET_SOA, (payload: SOAInputAction) => ({
  payload,
}));

// Thưởng theo chính sách Input
export interface SOAInputDetailAction<Type> {
  payload: SOAInput;
  type: Type;
  callback?: () => void;
}

export const getSOADetailAction = createAction(
  GET_SOA_POLICY_REWARD_DETAIL,
  (payload: SOAInputDetailAction<SOAPolicyRewardType>) => ({
    payload,
  })
);

// Trợ cấp cố định Input

export const getSOAMonthlyAction = createAction(
  GET_SOA_MONTHLY_ALLOWANCE_DETAIL,
  (payload: SOAInputDetailAction<SOAMonthlyAllowanceType>) => ({
    payload,
  })
);
