import { BlockWrapper } from "components/block/styled";
import { ProjectItem as ProjectItemDesktop } from "sections/home/<USER>/styled";
import styled from "styled-components";
import { device } from "styles/media";

export const HomeWrapper = styled.div`
  width: 100%;
  height: 100%;

  display: flex;
  flex-direction: row;
  position: relative;
  background-color: #ffffff;

  @media ${device.mobile} {
    min-height: calc(100 * var(--vh) - 50px);
    flex-direction: column;
  }
`;

export const ContentLeft = styled.div`
  width: 55%;
  height: calc(100 * var(--vh));

  display: flex;
  flex-direction: column;

  @media ${device.mobile} {
    width: 100%;
    height: 100%;

    h5 {
      margin-top: 24px;
      margin-bottom: 16px;
      padding: 0px 16px;

      color: ${({ theme }) => theme.color.status.grey_darkest};
    }
  }
`;

export const WrapperLeft = styled.div`
  padding: 24px 16px;

  .${ProjectItemDesktop.styledComponentId} {
    max-width: calc(100% / 3);
  }
`;

export const ContentRight = styled.div`
  width: 45%;
  height: calc(100 * var(--vh));

  background: ${({ theme }) => theme.color.status.primary_20};
  border-radius: 50px 0px 0px 0px;

  .${BlockWrapper.styledComponentId} {
    padding: 0px;
    margin-top: 0px;
  }

  @media ${device.mobile} {
    width: 100%;
    height: 100%;
    padding: 16px;

    flex: 1;

    border-radius: 0px;
  }
`;

export const WrapperRight = styled.div`
  padding: 35px 24px;
`;

export const NameTask = styled.h6`
  margin-top: 16px;
  margin-bottom: 24px;
`;

export const ProjectWrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: flex-start;

  padding: 16px 0px;

  @media ${device.mobile} {
    padding: 16px;
    justify-content: flex-start;
  }
`;

export const ProjectItem = styled.div`
  width: 100px;
  min-width: 100px;
  height: 100%;

  display: flex;
  flex-direction: column;
  align-items: center;

  position: relative;
  text-align: center;

  p {
    font-weight: 500;
    color: ${({ theme }) => theme.color.text.body};
  }
`;
