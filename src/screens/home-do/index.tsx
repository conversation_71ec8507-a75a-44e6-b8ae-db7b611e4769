/* eslint-disable no-unused-vars */
import { ERROR, ERROR_API_MESSAGE } from "@constants/message";
import {
  LinkVoiceRecordingInput,
  LinkVoiceRecordingOutput,
  RecordVoiceRecordingInput,
  RecordVoiceRecordingOutput,
  ResultRecordVoiceRecording,
  VoiceRecordCheckEnroll,
} from "@custom-types/home";
import { checkEnrollStatus, getLinkVoiceRecording, recordVoiceRecording } from "api/home";
import { Alert } from "components/alert";
import Icons, { IconType } from "components/icons";
import ScrollBar from "components/scroll-bar";
import TabForMobile, { ListTabsProps } from "components/tab-for-mobile";
import useActionApi from "hooks/use-action-api";
import { useAppSelector } from "hooks/use-redux";
import { useRouter } from "next/router";
import { useCallback, useEffect, useMemo, useState } from "react";
import CheckVoiceRecord from "sections/home/<USER>";
import ProfileDesktop from "sections/home/<USER>/desktop";
import ProfileMobile from "sections/home/<USER>/mobile";
import Projects from "sections/home/<USER>";
import { LinkProps } from "services/menu-link";
import {
  ContentLeft,
  ContentRight,
  HomeWrapper,
  ProjectItem,
  ProjectWrapper,
  WrapperLeft,
  WrapperRight,
} from "./styled";

const listTab: ListTabsProps[] = [
  { value: "overview", label: "Tổng quan" },
  { value: "tasks", label: "Nhiệm vụ hôm nay" },
];

const HomeDO = () => {
  const [active, setActive] = useState("overview");
  const [haveVoiceRecord, setHaveVoiceRecord] = useState(null);

  const { username, sa, isSmartPilotRun, isSmart1Show } = useAppSelector((state) => state.rootReducer.user);

  const router = useRouter();
  const { query } = router;

  const actionGetLinkVoiceRecording = useActionApi<LinkVoiceRecordingInput, LinkVoiceRecordingOutput>(
    getLinkVoiceRecording
  );
  const actionRecordVoiceRecording = useActionApi<RecordVoiceRecordingInput, RecordVoiceRecordingOutput>(
    recordVoiceRecording
  );
  const actionCheckRecordVoiceRecording = useActionApi<{ agentCode: string }, VoiceRecordCheckEnroll>(
    checkEnrollStatus
  );

  useEffect(() => {
    const { enrolled, quota, comparison } = query as unknown as ResultRecordVoiceRecording;
    if (enrolled && quota && comparison) {
      actionRecordVoiceRecording({
        loading: {
          type: "global",
          name: "recordVoiceRecordingLoading",
        },
        body: {
          agentCode: username,
          body: {
            enrolled,
            quota,
            comparison,
          },
        },
      }).then(({ data }) => {
        router.replace({ pathname: router.pathname, query: {} }, null, { shallow: false });
        setTimeout(() => {
          if (data.code === 200) {
            Alert("SUCCESSFUL", "Đăng ký giọng nói mẫu thành công");
          } else {
            Alert("ERROR", "Đăng ký giọng nói mẫu thất bại");
          }
        }, 500);
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [query, username]);

  useEffect(() => {
    //check voice recording status
    actionCheckRecordVoiceRecording({
      body: {
        agentCode: username,
      },
      loading: {
        type: "local",
        name: "checkRecordVoiceRecordingLoading",
      },
    })
      .then(({ data }) => {
        if (data?.code === 200) {
          setHaveVoiceRecord(data?.data?.enrolled);
        }
      })
      .catch((err) => {
        console.log(err);
        Alert(ERROR, ERROR_API_MESSAGE);
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [username]);

  const projectList: LinkProps[] = useMemo(() => {
    return [
      {
        href: `${process.env.basePath}/smart2`,
        value: "smart-2.0",
        icon: "project-smart",
        label: "Bán mới",
        show: sa !== 1 && isSmartPilotRun === "Y" ? true : false,
      },
      {
        href: `${process.env.basePath}/smart`,
        value: "smart-1.0",
        icon: "project-smart",
        label: "Bán mới",
        show: (sa !== 1 && isSmartPilotRun !== "Y") || (sa !== 1 && isSmart1Show === "Y") ? true : false,
      },
      {
        href: "",
        value: "voice-recording",
        icon: "project-voice-recording",
        label: "Đăng ký giọng nói mẫu",
        show: true,
      },
    ];
  }, [sa, isSmartPilotRun, isSmart1Show]);

  const handleRedirectToOtherSystem = (item: LinkProps) => {
    if (item.value === "voice-recording") {
      actionGetLinkVoiceRecording({
        loading: {
          type: "global",
          name: "getLinkVoiceRecordingLoading",
        },
        body: {
          agentId: username,
          callback_url: window.location.href,
        },
      }).then(({ data }) => {
        if (data.code === 200) {
          window.location.href = data.data.url;
        } else {
          Alert("ERROR", ERROR_API_MESSAGE);
        }
      });
    } else {
      window.location.href = item.href;
    }
  };

  const handleActive = (listTab: ListTabsProps) => {
    setActive(listTab.value);
  };

  const handleShowProfileModal = useCallback(() => {}, []);

  return (
    <HomeWrapper>
      <ProfileMobile achievementList={[]} onShowModal={handleShowProfileModal} />
      <TabForMobile list={listTab} active={active} onChange={handleActive} />
      <ContentLeft className="hide-mobile">
        <ScrollBar>
          <WrapperLeft>
            <Projects showSettingView={false} list={projectList} onRedirect={handleRedirectToOtherSystem} />
            {/* {width > 768
              ? layout.map((Component, index) => (
                  <Component
                    key={index}
                    performanceNewBusiness={performanceNewBusiness}
                    dataChart={dataChart}
                    currentTab={currentTab}
                    setCurrentTab={setCurrentTab}
                  />
                ))
              : null} */}
          </WrapperLeft>
        </ScrollBar>
      </ContentLeft>
      <ContentRight className="hide-mobile">
        <ScrollBar>
          <WrapperRight>
            <ProfileDesktop showNotification={false} achievementList={[]} onShowModal={handleShowProfileModal} />
            {/* <TaskToday>
              Nhiệm vụ hôm nay <span>({dataPolicyIndue?.length + Number(dataPolicyOverdue?.length)})</span>
            </TaskToday>
            <ContractIndue data={dataPolicyIndue} onShowDetail={handleShowDetail} />
            <ContractOverdue data={dataPolicyOverdue} onShowDetail={handleShowDetail} />
            <BirthdayReminder data={dataBirthdayReminder} /> */}
            <CheckVoiceRecord haveVoiceRecord={haveVoiceRecord} />
          </WrapperRight>
        </ScrollBar>
      </ContentRight>
      {active === "overview" ? (
        <ContentLeft className="hide-desktop">
          <h5 className="body-5">Công cụ hỗ trợ TVTC</h5>
          <ScrollBar height="auto">
            <ProjectWrapper>
              {projectList.map((item, index) =>
                item?.show ? (
                  <ProjectItem key={index} onClick={() => handleRedirectToOtherSystem(item)}>
                    <Icons icon={`${item.icon}-mobile` as IconType} />
                    <p className="body-5">{item.label}</p>
                  </ProjectItem>
                ) : null
              )}
            </ProjectWrapper>
          </ScrollBar>
          {/* {width <= 768
            ? layout.map((Component, index) => (
                <Component
                  key={index}
                  performanceNewBusiness={performanceNewBusiness}
                  dataChart={dataChart}
                  currentTab={currentTab}
                  setCurrentTab={setCurrentTab}
                />
              ))
            : null} */}
        </ContentLeft>
      ) : (
        <ContentRight className="hide-desktop">
          {/* <NameTask>Nhiệm vụ hôm nay ({dataPolicyIndue?.length + Number(dataPolicyOverdue?.length)})</NameTask>
          <ContractIndue data={dataPolicyIndue} onShowDetail={handleShowDetail} />
          <ContractOverdue data={dataPolicyOverdue} onShowDetail={handleShowDetail} />
          <BirthdayReminder data={dataBirthdayReminder} /> */}
          <CheckVoiceRecord haveVoiceRecord={haveVoiceRecord} />
        </ContentRight>
      )}
      {/* <ProfileDetail
        modal={showProfileModal}
        onCloseModal={() => setShowProfileModal({ show: false, type: "" })}
        onGetAvatar={handleGetAvatar}
        onGetPerformanceNewBusiness={handleGetPerformanceNewBusiness}
      /> */}
    </HomeWrapper>
  );
};

export default HomeDO;
