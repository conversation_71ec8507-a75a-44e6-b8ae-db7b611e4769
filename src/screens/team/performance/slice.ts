import { ValueLabel } from "@custom-types";
import { PerformanceType } from "@custom-types/performance";
import { HierarchyData } from "@custom-types/team";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export interface FormSearchPerformance {
  date: string;
  month: string;
  year: string;
}

export interface AgentDetailData {
  agentCode: string;
  description: string;
  icon: string;
  label: string;
}

export interface AgentDetailProps {
  type: "sale-personal" | "team-target" | "edit-personal" | "potential-customers" | "business-team" | "";
  title: string;
  data?: any;
}
export interface FypLessThan10Mil {
  show: boolean;
  value: string;
}

export interface TeamPerformanceForm {
  performanceTeamType?: PerformanceType;
  performanceTeamDate?: string;
  performanceTeamErrorDate?: string;
  performanceTeamQuarterly?: ValueLabel;
}

export interface TeamPerformanceSlice {
  agentTotal: number;
  flag: boolean;
  teamPerformanceDetail: TeamPerformanceForm;
  performanceTeamForm: TeamPerformanceForm;
  agentDetailModal: AgentDetailProps;
  fypLessThan10Mil: ValueLabel;
  hierarchyList: HierarchyData[];
  currentAgentOfTeam: HierarchyData;
  fypForFormSearch: {
    team: number;
    detail: number;
  };
  fypTeam: number;
}

const initialState: TeamPerformanceSlice = {
  agentTotal: null,
  flag: false,
  performanceTeamForm: {
    performanceTeamType: "",
    performanceTeamDate: "",
    performanceTeamErrorDate: "",
  },
  teamPerformanceDetail: {
    performanceTeamType: "",
    performanceTeamDate: "",
    performanceTeamErrorDate: "",
  },
  fypLessThan10Mil: {
    value: "",
    label: "",
  },
  agentDetailModal: {
    type: "",
    title: "",
    data: {
      agentCode: "",
      description: "",
      icon: "",
      label: "",
    },
  },
  hierarchyList: [],
  currentAgentOfTeam: {
    agentCode: "",
    agentName: "",
    designationCd: "",
    supervisorId: "",
    level: "",
    manager: false,
    branchLvl: 0,
    children: [],
  },
  fypForFormSearch: {
    team: 0,
    detail: 0,
  },
  fypTeam: 0,
};

const teamPerformanceSlice = createSlice({
  name: "teamPerformance",
  initialState: initialState,
  reducers: {
    clearTeamPerformanceSlice: () => {
      return initialState;
    },
    setAgentDetailModal: (state, action: PayloadAction<AgentDetailProps>) => {
      state.agentDetailModal = action.payload;
    },
    setFypLessThan10Mil: (state, action: PayloadAction<ValueLabel>) => {
      state.fypLessThan10Mil = action.payload;
    },
    setHierarchy: (state, action: PayloadAction<HierarchyData[]>) => {
      state.hierarchyList = action.payload;
    },
    setCurrentAgentOfTeam: (state, action: PayloadAction<HierarchyData>) => {
      state.currentAgentOfTeam = action.payload;
    },
    setAgentTotal: (state, action: PayloadAction<number>) => {
      state.agentTotal = action.payload;
    },
    setTeamPerformanceDetail: (state, action: PayloadAction<TeamPerformanceForm>) => {
      state.teamPerformanceDetail = action.payload;
    },
    setPerformanceTeamForm: (state, action: PayloadAction<TeamPerformanceForm>) => {
      state.performanceTeamForm = action.payload;
    },
    setFlag: (state, action: PayloadAction<boolean>) => {
      state.flag = action.payload;
    },
    setFYPForFormSearch: (state, action: PayloadAction<{ team: number; detail: number }>) => {
      state.fypForFormSearch = action.payload;
    },
    setFypTeam: (state, action: PayloadAction<number>) => {
      state.fypTeam = action.payload;
    },
  },
});

export const {
  setHierarchy,
  setAgentTotal,
  setFlag,
  setFypLessThan10Mil,
  setAgentDetailModal,
  setCurrentAgentOfTeam,
  setPerformanceTeamForm,
  setTeamPerformanceDetail,
  clearTeamPerformanceSlice,
  setFYPForFormSearch,
  setFypTeam,
} = teamPerformanceSlice.actions;

export default teamPerformanceSlice.reducer;
