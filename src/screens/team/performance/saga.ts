import { SagaAction } from "@custom-types";
import { call, put, select, takeLatest } from "redux-saga/effects";
import { RootState } from "redux/store";
import { setFYPForFormSearch, setFypTeam, TeamPerformanceSlice } from "./slice";
import { setLoading } from "redux/root-reducer";
import { AgentIdInput } from "@custom-types/home";
import { LoadingKey } from "@custom-types/loading";
import { getFYPTeam } from "api/team";
import { Alert } from "components/alert";
import { ERROR, ERROR_API_MESSAGE } from "@constants/message";
import { FypTeamOutput } from "@custom-types/team";

export const SET_FYP_FOR_FORM_SEARCH = "SET_FYP_FOR_FORM_SEARCH";
export const GET_FYP_TEAM = "GET_FYP_TEAM";

function* setFYPToFormSearchSaga({ payload: { key, value } }: SagaAction<{ key: "team" | "detail"; value: number }>) {
  const preState: TeamPerformanceSlice = yield select((state: RootState) => state.teamPerformanceReducer);

  yield put(
    setFYPForFormSearch({
      ...preState.fypForFormSearch,
      [key]: value,
    })
  );
}

function* getFypTeamSaga({ payload }: SagaAction<AgentIdInput>) {
  try {
    yield put(setLoading({ getFYPTeamLoading: "local" } as LoadingKey));

    const { data }: { data: FypTeamOutput } = yield call(getFYPTeam, payload);

    yield put(setFypTeam(data?.fypTeam ?? 0));
  } catch (error) {
    console.log("error", error);
    Alert(ERROR, ERROR_API_MESSAGE);
  } finally {
    yield put(setLoading({ getFYPTeamLoading: "" } as LoadingKey));
  }
}

function* teamSaga() {
  yield takeLatest(SET_FYP_FOR_FORM_SEARCH, setFYPToFormSearchSaga);
  yield takeLatest(GET_FYP_TEAM, getFypTeamSaga);
}

export default teamSaga;
