import { useDispatch } from "react-redux";
import TopAgentModal from "sections/team/agent-detail-modal";
import { setAgentDetailModal } from "./slice";
import { PerformanceTeamWrapper } from "./styled";
import TeamPerformanceContentLeft from "sections/team/content-left";
import TeamPerformanceContentRight from "sections/team/content-right";

const PerformanceTeam = () => {
  const dispatch = useDispatch();

  const handleShowModal = (item: any) => {
    dispatch(
      setAgentDetailModal({
        type: item.type,
        title: item.title,
        data: item.data,
      })
    );
  };

  return (
    <PerformanceTeamWrapper>
      <TeamPerformanceContentLeft onShowModal={handleShowModal} />
      <TeamPerformanceContentRight onShowModal={handleShowModal} />
      <TopAgentModal />
    </PerformanceTeamWrapper>
  );
};

export default PerformanceTeam;
