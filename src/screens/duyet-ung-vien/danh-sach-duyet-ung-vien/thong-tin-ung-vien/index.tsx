import { ERROR_API_MESSAGE } from "@constants/message";
import { File } from "@custom-types";
import { TableType } from "@custom-types/config-table";
import {
  ApproveCandidateInput,
  ApproveCandidateOutput,
  ConfigEreData,
  ConfigEreOutput,
  DataValueConfigEre,
  GetBoListInput,
  GetDetailCandidateInput,
  GetDetailCandidateOutput,
  GetToListInput,
  ToListOutput,
} from "@custom-types/ere-approval";
import { approveCandidateEre, getBoList, getConfigEre, getDetailCandidateEre, getToList } from "api/ere-approval";
import { Alert } from "components/alert";
import Icons from "components/icons";
import InputFieldSet from "components/input-fileldset";
import Modal from "components/modal";
import ModalFullPage from "components/modal-full-page";
import { ModalFooter } from "components/modal/styled";
import RadioButton from "components/radio";
import SeparateLine from "components/separate-line";
import useActionApi from "hooks/use-action-api";
import { useAppSelector } from "hooks/use-redux";
import { isNil, isNull } from "lodash";
import moment from "moment";
import { useRouter } from "next/router";
import { useCallback, useEffect, useMemo, useState } from "react";
import BenefitDeclarationSection from "sections/duyet-ung-vien/danh-sach-duyet-ung-vien/thong-tin-ung-vien/bang-ke-khai-loi-ich-section";
import { SubQuestion } from "sections/duyet-ung-vien/danh-sach-duyet-ung-vien/thong-tin-ung-vien/bang-ke-khai-loi-ich-section/styled";
import GallerySlide from "sections/duyet-ung-vien/danh-sach-duyet-ung-vien/thong-tin-ung-vien/gallery";
import HistoryAcceptModal from "sections/duyet-ung-vien/danh-sach-duyet-ung-vien/thong-tin-ung-vien/lich-su-duyet-modal";
import InputBlock from "sections/duyet-ung-vien/danh-sach-duyet-ung-vien/thong-tin-ung-vien/section-detail/block-input";
import BankInfoEre from "sections/duyet-ung-vien/danh-sach-duyet-ung-vien/thong-tin-ung-vien/section-detail/table/bank";
import DocumentInfoEre from "sections/duyet-ung-vien/danh-sach-duyet-ung-vien/thong-tin-ung-vien/section-detail/table/document";
import RelationshipInfoEre from "sections/duyet-ung-vien/danh-sach-duyet-ung-vien/thong-tin-ung-vien/section-detail/table/relationship";
import WorkExperienceInfoEre from "sections/duyet-ung-vien/danh-sach-duyet-ung-vien/thong-tin-ung-vien/section-detail/table/work-exp";
import {
  AvatarCandidate,
  CommentWrapper,
  DetailCandidateWrapper,
  InfoCandidateLeft,
  InfoCandidateRight,
  TitleComment,
} from "sections/duyet-ung-vien/danh-sach-duyet-ung-vien/thong-tin-ung-vien/styled";
import { getLabelOfList } from "sections/duyet-ung-vien/danh-sach-duyet-ung-vien/utils";
import { getAge } from "services/untils";
import { FlexBox } from "styles";
import { ButtonPrimary } from "styles/buttons";
import { defaultStatus } from "..";

interface DetailCandidateScreenProps {}

export interface GalleryProps {
  show?: boolean;
  data?: {
    action: string;
    filesData: File[];
  };
  indexItem?: number;
  title?: string;
}

export interface DataRenderInput {
  label: string;
  value: string;
  type?: TableType;
  md: number;
}

// const note =
//   "(<span style='color:#B30909;'>*</span>) Bắt buộc phải kiểm tra tất cả các Tài liệu hỗ trợ thì mới có thể gửi thông tin ứng viên đi.";

const DetailCandidateScreen = ({}: DetailCandidateScreenProps) => {
  const [actionSubmit, setActionSubmit] = useState("APPROVE");
  const [noteSubmit, setNoteSubmit] = useState("");
  const [showHistoryAccepted, setShowHistoryAccepted] = useState(false);
  const [dataDetail, setDataDetail] = useState<GetDetailCandidateOutput>(null);
  const [dataTableDocument, setDataTableDocument] = useState([]);
  const [showGallery, setShowGallery] = useState<GalleryProps>({
    show: false,
    data: {
      action: "",
      filesData: [],
    },
    indexItem: null,
  });
  const [ereConfig, setEreConfig] = useState<ConfigEreData>(null);
  const [isApprove, setIsApprove] = useState(false);
  const [boList, setBoList] = useState<DataValueConfigEre[]>([]);
  const [toList, setToList] = useState<ToListOutput[]>([]);
  const [showModalException, setShowModalException] = useState({
    show: false,
    content: [""],
  });

  //api
  const actionApproveCandidate = useActionApi<ApproveCandidateInput, ApproveCandidateOutput>(approveCandidateEre);
  const actionGetDetailCandidate = useActionApi<GetDetailCandidateInput, GetDetailCandidateOutput>(
    getDetailCandidateEre
  );
  const actionGetConfigEre = useActionApi<{ channelCd: string }, ConfigEreOutput>(getConfigEre);
  const actionGetBoList = useActionApi<GetBoListInput[], DataValueConfigEre[]>(getBoList);
  const actionGetToList = useActionApi<GetToListInput, ToListOutput[]>(getToList);

  const {
    user: { channel, username },
  } = useAppSelector((state) => state.rootReducer);

  const router = useRouter();
  const { registrationId } = router.query;

  useEffect(() => {
    if (isNil(ereConfig) && channel) {
      actionGetConfigEre({
        loading: {
          type: "global",
          name: "getConfigEreLoading",
        },
        isCache: true,
        body: { channelCd: channel },
      })
        .then(({ data }) => {
          setEreConfig(data?.config);
        })
        .catch((err) => {
          console.log(err);
          Alert("ERROR", ERROR_API_MESSAGE);
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ereConfig, channel]);

  useEffect(() => {
    if (username && registrationId) {
      actionGetDetailCandidate({
        body: {
          id: registrationId as string,
          params: {
            agentCode: username,
          },
        },
        loading: {
          name: "getDetailCandidateLoading",
          type: "global",
        },
      })
        .then(({ data }) => {
          const { type, candidate, positionCode } = data;

          let modal: {
            show: boolean;
            content: string[];
          } = {
            show: false,
            content: [],
          };

          if (type === "BANCA") {
            const age = getAge(candidate.birthday, "YYYY-MM-DD");

            if (["FSC", "SFSC"].includes(positionCode)) {
              if (age < 22 || age > 35) {
                modal.show = true;
                modal.content.push("Ứng viên không đủ điều kiện tuyển dụng về tuổi, cần xét duyệt ngoại lệ");
              }
            } else if (["BSM", "SBSM"].includes(positionCode)) {
              if (age < 22 || age > 50) {
                modal.show = true;
                modal.content.push("Ứng viên không đủ điều kiện tuyển dụng về tuổi, cần xét duyệt ngoại lệ");
              }
            }

            if (!["CAODANG", "DAIHOC", "SAUDAIHOC"].includes(candidate.educationCode)) {
              modal.show = true;
              modal.content.push("Ứng viên không đủ điều kiện tuyển dụng về bằng cấp, cần xét duyệt ngoại lệ");
            }

            setShowModalException(modal);
          }

          setDataDetail(data);
        })
        .catch((err) => {
          console.log(err);
          Alert("ERROR", ERROR_API_MESSAGE);
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [registrationId, username]);

  useEffect(() => {
    if (channel === "BANCA" && !ereConfig?.boList?.length && !isNull(dataDetail)) {
      let payload: GetBoListInput[];

      if (dataDetail?.positionCode === "SBSM" || dataDetail?.positionCode === "BSM") {
        // if position === "SBSM"||"BSM" => select all of sbmList to get boList
        payload = ereConfig?.sbmList;
      } else {
        payload = [ereConfig?.sbmList?.find((item) => item.itemCode === dataDetail?.bsmCode)];
      }

      actionGetBoList({
        body: payload,
        loading: {
          name: "getBoListLoading",
          type: "global",
        },
      })
        .then(({ data }) => {
          setBoList(data);
        })
        .catch((err) => {
          console.log(err);
          Alert("ERROR", ERROR_API_MESSAGE);
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [channel, dataDetail, ereConfig?.boList, ereConfig?.sbmList]);

  useEffect(() => {
    if (dataDetail?.bankBo && channel === "BANCA" && !ereConfig?.toList?.length) {
      actionGetToList({
        body: {
          boCode: dataDetail?.bankBo,
        },
        loading: {
          name: "getToListLoading",
          type: "global",
        },
      })
        .then(({ data }) => {
          setToList(data);
        })
        .catch((err) => {
          console.log(err);
          Alert("ERROR", ERROR_API_MESSAGE);
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [channel, ereConfig, dataDetail]);

  const onlyView = useMemo(() => {
    return ["PA", "A", "AA"].includes(dataDetail?.status);
  }, [dataDetail?.status]);

  // false: ko phải ng có quyền approve.
  const isApproveView = useMemo(() => {
    //CUBE-728 + CUBE-917 : Các status trong defaultStatus có quyền Approve Hồ sơ
    return username === dataDetail?.approverCode && defaultStatus.includes(dataDetail?.status);
  }, [dataDetail, username]);

  /** --------------------------- INFO --------------------------- */
  const infoList = useMemo(() => {
    return [
      {
        label: "Họ tên người báo cáo trực tiếp",
        value: dataDetail?.reporterName,
      },
      {
        label: "Mã số người báo cáo trực tiếp",
        value: dataDetail?.reporterCode,
      },
      {
        label: "Họ tên người giới thiệu",
        value: dataDetail?.introducerName,
      },
      {
        label: "Mã số người giới thiệu",
        value: dataDetail?.introducerCode,
      },
      {
        label: "Lớp học",
        value: dataDetail?.clazzName,
      },
      {
        label: "Nơi thi",
        value: dataDetail?.examPlace,
      },
      {
        label: "Ngày thi",
        value: dataDetail?.examDate ? moment(dataDetail?.examDate).format("DD/MM/YYYY") : null,
      },
    ];
  }, [dataDetail]);

  const positionInfo = useMemo(() => {
    const channelFromDataDetail = dataDetail?.type;
    const checkOutOfIndustry =
      channelFromDataDetail === "AGENCY" && (dataDetail?.positionCode === "FWO" || dataDetail?.positionCode === "FWM")
        ? true
        : false;
    const typeBanca = channelFromDataDetail === "BANCA";
    return [
      { label: "Vị trí", md: checkOutOfIndustry ? 6 : 12, value: dataDetail?.positionCode, show: true },
      {
        label: "Tuyển dụng ngoài ngành",
        md: 6,
        value: dataDetail?.outOfIndustry === "Y" ? "Có" : "Không",
        show: checkOutOfIndustry,
      },
      { label: "Dự án", md: 12, value: dataDetail?.projectName, show: typeBanca },
      { label: "Chi nhánh", md: 6, value: getLabelOfList(boList, dataDetail?.bankBo), show: typeBanca },
      { label: "Văn phòng", md: 6, value: getLabelOfList(toList, dataDetail?.bankTo), show: typeBanca },
      { label: "BSM/SBSM", md: 6, value: dataDetail?.bsmCode, show: typeBanca },
      { label: "DOB/RDOB", md: 6, value: dataDetail?.rdobName, show: typeBanca },
      {
        label: "Văn phòng làm việc",
        md: 12,
        value: channelFromDataDetail === "AGENCY" ? dataDetail?.officeName : dataDetail?.locationName,
        show: true,
      },
    ].filter((i) => i.show);
  }, [dataDetail, boList, toList]);

  const personalInfo: DataRenderInput[] = useMemo(() => {
    const candidateInfo = dataDetail?.candidate;
    return [
      { label: "Họ tên ", md: 6, value: candidateInfo?.fullName, type: "string" },
      { label: "Giới tính", md: 6, value: candidateInfo?.gender, type: "string" },
      {
        label: "Ngày tháng năm sinh",
        md: 6,
        value: moment(candidateInfo?.birthday ?? "").format("DD/MM/YYYY"),
        type: "string",
      },
      {
        label: "Nơi sinh",
        md: 6,
        value: getLabelOfList(ereConfig?.provinceList, candidateInfo?.birthPlace),
        type: "string",
      },

      { label: "Tình trạng hôn nhân", md: 6, value: candidateInfo?.maritalStatus, type: "string" },
      { label: "Học vấn", md: 6, value: candidateInfo?.education, type: "string" },
      { label: "Tôn giáo", md: 6, value: candidateInfo?.religion, type: "string" },
      { label: "Quốc tịch", md: 6, value: candidateInfo?.nationality, type: "string" },
    ];
  }, [dataDetail, ereConfig?.provinceList]);

  const identityInfo: DataRenderInput[] = useMemo(() => {
    const candidateInfo = dataDetail?.candidate;
    return [
      {
        label: `Số CCCD`,
        md: 12,
        value: candidateInfo?.idNumber,
        type: "string",
      },
      { label: "Ngày cấp", md: 6, value: moment(candidateInfo?.issueDate ?? "").format("DD/MM/YYYY"), type: "string" },
      { label: "Nơi cấp", md: 6, value: candidateInfo?.issuePlace, type: "string" },
    ];
  }, [dataDetail]);

  const addressInfo: DataRenderInput[] = useMemo(() => {
    const candidateInfo = dataDetail?.candidate;
    const businessAddress = candidateInfo?.businessAddress;
    const residentAddress = candidateInfo?.residentAddress;

    return [
      {
        label: "Địa chỉ liên lạc (nơi nhận tất cả các thông tin, văn bản liên quan đến công ty)",
        md: 12,
        value: businessAddress?.address,
        type: "string",
      },
      {
        label: "Địa chỉ thường trú (địa chỉ trên sổ HKTT)",
        md: 12,
        value: residentAddress?.address,
        type: "string",
      },
      { label: "Di động", md: 6, value: candidateInfo?.mobilePhone, type: "string" },
      { label: "Email", md: 6, value: candidateInfo?.email, type: "string" },
    ];
  }, [dataDetail]);
  /** --------------------------- INFO --------------------------- */

  //avatar
  const avatar = useMemo(() => {
    return dataDetail?.files?.find((i) => i.fieldType === "PICTURE")?.fileContent.replace("FWDVIETNAM", "");
  }, [dataDetail]);

  const disableCheck = useMemo(() => {
    if (
      (actionSubmit === "REJECT" && !noteSubmit) ||
      !dataTableDocument.every((i) => i.files?.action === "check") ||
      onlyView
    ) {
      return true;
    } else {
      return false;
    }
  }, [actionSubmit, dataTableDocument, noteSubmit, onlyView]);

  const handleUpdateTableDoc = useCallback(
    (idx: number) => {
      if (onlyView) {
        return;
      }

      const updatedData = dataTableDocument.map((item, i) => {
        if (i === idx) {
          return {
            ...item,
            files: {
              ...item.files,
              action: item.files.action === "check" ? "unCheck" : "check",
            },
          };
        }
        return item;
      });

      //update action when show
      setShowGallery((prev) => ({
        ...prev,
        data: updatedData[idx].files,
        show: true,
      }));
      setDataTableDocument(updatedData);
    },
    [dataTableDocument, onlyView]
  );

  const handleApproveCandidate = () => {
    actionApproveCandidate({
      body: {
        payload: {
          action: actionSubmit,
          comment: noteSubmit,
          fullName: dataDetail?.candidate?.fullName,
          username: username,
          entityId: 0,
          date: null,
          skipExam: null,
          recruitFlag: null,
        },
        params: {
          registrationId: registrationId as string,
        },
      },
      loading: {
        name: "changePasswordLoading",
        type: "global",
      },
    })
      .then(({ data }) => {
        if (data?.status === "ok") {
          Alert("SUCCESSFUL", "Bạn đã duyệt ứng viên thành công"); // chưa fix wording
          setIsApprove(true);
        } else {
          Alert("ERROR", "Duyệt ứng viên thất bại"); // chưa fix wording
        }
      })
      .catch((err) => {
        console.log(err);
        Alert("ERROR", ERROR_API_MESSAGE);
      });
  };

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (isApprove) {
      timer = setTimeout(() => {
        router.back();
      }, 750);
    }

    return () => {
      clearTimeout(timer);
    };
  }, [isApprove, router]);

  return (
    <ModalFullPage
      show={true}
      onClose={() => router.back()}
      title="Thông tin ứng viên"
      primaryBtn={{
        text: "Hoàn Tất",
        disable: disableCheck,
        onClick: !isApproveView ? null : () => handleApproveCandidate(),
      }}
    >
      <DetailCandidateWrapper>
        <InfoCandidateLeft>
          <AvatarCandidate>
            <img src={avatar ? avatar : `${process.env.basePath}/img/avatar-default.jpg`} alt="avatar" />
          </AvatarCandidate>
          <h6 className="color-primary mt-20 mb-20">Người quản lý trực tiếp/người giới thiệu</h6>
          <FlexBox direction="column" gap={16} className="full-width">
            {infoList.map((d, i) => (
              <div key={i}>
                <p className="body-4">{d.label}</p>
                <h6 className="h7 info">{d.value || "-"}</h6>
              </div>
            ))}
          </FlexBox>
        </InfoCandidateLeft>
        <InfoCandidateRight>
          <InputBlock dataInput={positionInfo} title="Vị trí ứng tuyển" />
          <SeparateLine color="#DBDFE1" height={1} width="100%" className="mt-16 mb-4" />
          <InputBlock dataInput={personalInfo} title="Thông tin cá nhân" />
          <InputBlock dataInput={identityInfo} title="Thông tin giấy tờ" />
          <InputBlock dataInput={addressInfo} title="Thông tin liên hệ" />
          <RelationshipInfoEre dataDetail={dataDetail?.candidate?.relationships} ereConfig={ereConfig} />
          <SeparateLine color="#DBDFE1" height={1} width="100%" className="mt-24 mb-4" />
          <WorkExperienceInfoEre dataDetail={dataDetail?.candidate?.workingExperiences} ereConfig={ereConfig} />
          <SeparateLine color="#DBDFE1" height={1} width="100%" className="mt-24 mb-4" />
          <DocumentInfoEre
            docFiles={dataDetail?.files}
            dataTableDocument={dataTableDocument}
            onlyView={onlyView}
            setDataTableDocument={setDataTableDocument}
            setShowGallery={setShowGallery}
          />
          {/* <div className="mt-16">
            <Note title="Lưu ý" content={note} backgroundColor="#EDEFF0" />
          </div> */}
          <SeparateLine color="#DBDFE1" height={1} width="100%" className="mt-24 mb-4" />
          <BankInfoEre dataDetail={dataDetail?.candidate} />
          <SeparateLine color="#DBDFE1" height={1} width="100%" className="mt-24 mb-4" />
          <BenefitDeclarationSection coiFormData={dataDetail?.coiForm} ereConfig={ereConfig} />
          <SeparateLine color="#DBDFE1" height={1} width="100%" className="mt-20 mb-20 sm-mb-16 sm-mt-16" />
          {!isApproveView ? (
            <FlexBox alignItem="center" justifyContent="flex-end" wrap="wrap" gap={8}>
              <FlexBox alignItem="center" gap={4}>
                <Icons icon="icon-time-past" fill="#E87722" width={24} height={24} />
                <h6 className="h8 color-primary pointer" onClick={() => setShowHistoryAccepted(true)}>
                  Lịch sử duyệt
                </h6>
              </FlexBox>
            </FlexBox>
          ) : (
            <>
              <CommentWrapper>
                <FlexBox alignItem="center" justifyContent="space-between" wrap="wrap" gap={8}>
                  <TitleComment className="color-primary h7">
                    Nhận xét về ứng viên từ Người phê duyệt nếu có:
                  </TitleComment>
                  <FlexBox alignItem="center" gap={4}>
                    <Icons icon="icon-time-past" fill="#E87722" width={24} height={24} />
                    <h6 className="h8 color-primary pointer" onClick={() => setShowHistoryAccepted(true)}>
                      Lịch sử duyệt
                    </h6>
                  </FlexBox>
                </FlexBox>
                <SubQuestion className="body-5 mt-8">
                  Bằng việc bấm nút “<span className="color-primary">Đồng ý tuyển dụng</span>”, Tôi xác nhận là tôi đã
                  xem, kiểm tra và đối chiếu các thông tin được kê khai trên hồ sơ với các hồ sơ cá nhân được cung cấp
                  từ ứng viên (bao gồm hồ sơ ứng viên, Bảng kê khai xung đột lợi ích và nội dung cam kết của ứng viên)
                  đảm bảo tính chính xác, thống nhất và đầy đủ của hồ sơ ứng viên theo quy định của FWD, đồng thời đồng
                  ý tuyển dụng ứng viên này qua "<span className="color-primary">Cổng thông tin kinh doanh</span>”.
                </SubQuestion>
                <RadioButton
                  label="Đồng ý tuyển dụng"
                  haveBorder={false}
                  selected={actionSubmit === "APPROVE"}
                  onChange={() => setActionSubmit("APPROVE")}
                />
                <RadioButton
                  haveBorder={false}
                  label="Cần cân nhắc thêm"
                  selected={actionSubmit === "REJECT"}
                  onChange={() => setActionSubmit("REJECT")}
                />
                <InputFieldSet
                  type="textarea"
                  placeholder="Ghi chú"
                  required={actionSubmit === "REJECT"}
                  value={noteSubmit}
                  onChange={(e) => setNoteSubmit(e.target.value)}
                />
              </CommentWrapper>
            </>
          )}
        </InfoCandidateRight>
      </DetailCandidateWrapper>
      <Modal
        title="Lịch sử duyệt"
        size="slg"
        backgroundMobile="#fff"
        borderHeader
        show={showHistoryAccepted}
        onClose={() => setShowHistoryAccepted(false)}
      >
        <HistoryAcceptModal data={dataDetail?.approvalLogs} />
      </Modal>
      <Modal
        title={showGallery?.title}
        size="slg"
        backgroundMobile="#fff"
        borderHeader
        show={showGallery.show}
        isFooterSticky
        onClose={() => setShowGallery({ show: false, indexItem: null })}
      >
        <GallerySlide onUpdateData={handleUpdateTableDoc} payload={showGallery} />
        <ModalFooter>
          <ButtonPrimary onClick={() => setShowGallery({ show: false, indexItem: null })} long>
            Xác nhận
          </ButtonPrimary>
        </ModalFooter>
      </Modal>
      <Modal title="Thông báo" show={showModalException.show} size="sm" showClose={false}>
        <ul>
          {showModalException.content.map((item, index) => (
            <li key={index}>{item}</li>
          ))}
        </ul>
        <ModalFooter>
          <ButtonPrimary onClick={() => setShowModalException({ show: false, content: [""] })}>Xác nhận</ButtonPrimary>
        </ModalFooter>
      </Modal>
    </ModalFullPage>
  );
};

export default DetailCandidateScreen;
