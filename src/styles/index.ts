import styled, { css } from "styled-components";
import { device } from "./media";

type JustifyContent =
  | "start-end"
  | "flex-end"
  | "flex-start"
  | "center"
  | "space-between"
  | "space-around"
  | "space-evenly";

interface ColProps {
  md?: number;
  sm?: number;
  lg?: number;
  flex?: number;
  pd?: number;
}

export const Spacing = styled.div`
  flex: 1;
`;

export const ImagePercentWrapper = styled.div<{ percent?: number; percentMobile?: number; radius?: number }>`
  height: 0;
  position: relative;
  overflow: hidden;
  padding-top: ${({ percent }) => percent}%;
  border-radius: ${({ radius }) => radius ?? 0}px;

  img {
    width: 100%;
    height: 100%;

    position: absolute;
    top: 0;
    left: 0;
    object-fit: cover;
  }

  @media ${device.mobile} {
    ${({ percentMobile }) => (percentMobile ? `padding-top:${percentMobile}%` : null)};
  }
`;

export const HideByOpacityStyle = css`
  opacity: 0;
  user-select: none;
  pointer-events: none;
`;

export const HideByOpacity = styled.div`
  width: 0px;
  height: 0px;

  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;

  ${HideByOpacityStyle}
`;

export const Error = styled.span`
  margin-top: 4px;
  color: ${({ theme }) => theme.color.status.red};
  display: block;
  font-size: 12px;
`;

//inside to ignore margin of RowItem inside RowItem
//if WRONG, need add className for your case
export const RowItem = styled.div<{ distance?: number; inside?: boolean }>`
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  flex-wrap: wrap;

  margin-left: -${({ distance = 16 }) => distance / 2}px;
  margin-right: -${({ distance = 16 }) => distance / 2}px;
  margin-bottom: ${({ distance = 16 }) => distance}px;

  & > div {
    margin-left: ${({ distance = 16 }) => distance / 2}px;
    margin-right: ${({ distance = 16 }) => distance / 2}px;

    flex: 1;
  }

  ${({ inside }) => (inside ? `margin: 0px !important;` : "")};

  @media ${device.mobile} {
    width: 100%;
    margin: 0px;
    margin-bottom: ${({ distance = 16 }) => distance}px;

    flex-direction: column;

    & > div {
      width: 100%;
      margin: 0px;

      :not(:last-child) {
        margin-bottom: ${({ distance = 16 }) => distance}px !important;
      }
    }
  }
`;

interface FlexBoxProps {
  direction?: string;
  alignItem?: "flex-end" | "flex-start" | "center";
  justifyContent?: JustifyContent;
  shrink?: number;
  gap?: number;
  wrap?: "wrap" | "nowrap" | "wrap-reverse";
  mbGap?: number;
  mbDirection?: string;
}

export const FlexBox = styled.div<FlexBoxProps>`
  display: flex;
  flex-direction: ${({ direction }) => direction ?? "row"};
  align-items: ${({ alignItem }) => alignItem};
  justify-content: ${({ justifyContent }) => justifyContent};
  flex-shrink: ${({ shrink }) => shrink};
  gap: ${({ gap }) => gap}px;
  flex-wrap: ${({ wrap }) => wrap};

  @media ${device.mobile} {
    gap: ${({ mbGap }) => mbGap}px;
    flex-direction: ${({ mbDirection, direction }) => (mbDirection ? mbDirection : direction ? direction : "row")};
  }
`;

export const TitlePage = styled.h6`
  margin-bottom: 24px;
`;

//input picker
interface FieldSetProp {
  readonly disabled?: boolean;
  readonly active: boolean;
  readonly error: string | boolean;
}

export const FieldSet = styled.fieldset<FieldSetProp>`
  max-width: 100%;
  padding: 0;
  margin: 0;
  border-radius: 4px;

  position: relative;
  transition: all 0.3s ease-in-out;
  border: 1px solid ${({ active, error }) => (error ? "#B30909" : active ? "#E87722" : "#DBDFE1")};

  :hover {
    border: 1px solid
      ${({ theme, active, disabled }) =>
        active ? theme.color.text.primary : disabled ? theme.color.text.disabled : theme.color.text.placeholder};
  }

  span {
    color: ${({ theme, disabled }) => (disabled ? theme.color.text.disabled : theme.color.status.red)};
  }
`;

export const NoData = styled.div`
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px 0px;

  font-size: 16px;
  line-height: 24px;
  font-weight: 500;

  color: ${({ theme }) => theme.color.text.disabled};
`;

export const PolicyNo = styled.p<{ primary?: boolean }>`
  font-size: 14px;
  line-height: 125%;

  ${({ primary }) =>
    primary
      ? css`
          font-weight: 500;

          color: ${({ theme }) => theme.color.status.primary};
          cursor: pointer;

          :hover {
            text-decoration: underline;
          }
        `
      : null}
`;

export const Separate = styled.div<{ mgTop?: number; mgBottom?: number }>`
  border-top: 1px solid ${({ theme }) => theme.color.status.grey};

  margin-top: ${({ mgTop = 16 }) => mgTop}px;
  margin-bottom: ${({ mgBottom = 16 }) => mgBottom}px;
`;

export const ScrollNoBar = css`
  overflow: auto;

  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  ::-webkit-scrollbar {
    display: none;
  }
`;

export const StickyWrapper = styled.div<{ top?: number; zIndex?: number }>`
  position: sticky;
  top: ${({ top }) => top ?? "0"}px;
  z-index: ${({ zIndex }) => zIndex};
`;

export const RelativeWrapper = styled.div`
  position: relative;
`;

export const HR = styled.div<{ distance?: number }>`
  width: 100%;
  margin: ${({ distance }) => distance ?? 16}px 0px;

  border-top: 1px solid ${({ theme }) => theme.color.status.grey};
`;

export const ScrollBarStyle = css`
  ::-webkit-scrollbar {
    width: 7px;
  }

  ::-webkit-scrollbar-track {
    background: #e3e1e19e;
    border-radius: 6px;
  }

  ::-webkit-scrollbar-thumb {
    background: #888;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #666666;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 8px;
  }
`;

export const ScrollBarWrapper = styled.div<{ maxHeight: string; maxHeightMobile?: string }>`
  height: ${({ maxHeight }) => maxHeight};

  @media ${device.mobile} {
    height: ${({ maxHeightMobile }) => maxHeightMobile};
  }
`;

interface ColProps {
  md?: number;
  sm?: number;
  lg?: number;
  flex?: number;
  pd?: number;
}

export const Col = styled.div<ColProps>`
  width: ${({ md }) => 100 * (md / 12)}%;
  padding: ${({ pd }) => (pd ? `0 ${pd}px` : "0 10px")};

  flex: ${({ flex }) => flex};

  @media ${device.mobile} {
    width: ${({ sm = 12 }) => 100 * (sm / 12)}%;
  }
`;

interface RowProps {
  align?: "center" | "start" | "end";
  justify?: "center" | "start" | "end";
  rowGap?: number;
  rowGapMb?: number;
  spaceBetween?: number;
  isBlock?: boolean;
}

export const Row = styled.div<RowProps>`
  min-width: 100%;
  margin: ${({ spaceBetween }) => (spaceBetween ? `0 -${spaceBetween}px` : "0 -10px")};

  display: flex;
  flex-wrap: wrap;
  align-items: ${({ align }) => align || "start"};
  justify-content: ${({ justify }) => justify || "flex-start"};

  :not(:last-child) {
    margin-bottom: ${({ rowGap, isBlock }) => (isBlock ? "unset" : `${rowGap}px` || "0px")};
  }

  ${({ isBlock, rowGapMb }) => isBlock && `row-gap: ${rowGapMb}px;`}

  .${Col.styledComponentId} {
    padding: ${({ spaceBetween }) => (spaceBetween ? `0 ${spaceBetween}px` : "0 10px")} !important;
  }

  @media ${device.mobile} {
    row-gap: ${({ rowGapMb }) => rowGapMb || 0}px;

    :not(:last-child) {
      margin-bottom: ${({ rowGapMb, isBlock }) => (isBlock ? "unset" : `${rowGapMb}px` || "0px")};
    }
  }
`;

interface FlexItemProps {
  flex?: number;
  order?: number;
  flexGrow?: number;
  flexShrink?: number;
  flexBasis?: string;
  alignSelf?: "auto" | "flex-start" | "flex-end" | "center" | "baseline" | "stretch";
}

export const FlexItem = styled.div<FlexItemProps>`
  flex: ${({ flex }) => flex};
  order: ${({ order }) => order || 0};

  flex-grow: ${({ flexGrow }) => flexGrow || 0};
  flex-shrink: ${({ flexShrink }) => flexShrink || 1};
  flex-basis: ${({ flexBasis }) => flexBasis || "auto"};
  align-self: ${({ alignSelf }) => alignSelf || "auto"};
`;

//Grid
interface CellProps {
  className?: string;
  width?: number;
  height?: number;
  top?: number | string;
  left?: number | string;
  middle?: boolean;
  center?: boolean;
}

export const Cell = styled.div<CellProps>`
  height: 100%;
  min-width: 0;

  grid-column-end: ${({ width = 1 }) => `span ${width}`};
  grid-row-end: ${({ height = 1 }) => `span ${height}`};

  ${({ left }) => left && `grid-column-start: ${left}`};
  ${({ top }) => top && `grid-row-start: ${top}`};
  ${({ center }) => center && `text-align: center`};

  ${({ middle }) =>
    middle &&
    `
    display: inline-flex;
    flex-flow: column wrap;
    justify-content: center;
    justify-self: stretch;
  `};
`;

const layoutGenerate = (value: any) => (typeof value === "number" ? `repeat(${value}, 1fr)` : value);

interface GridProps {
  className?: string;
  columns?: string | number;
  gap?: number;
  columnGap?: number;
  rowGap?: number;
  height?: string;
  minRowHeight?: string;
  flow?: string;
  rows?: string | number;
  justifyContent?: string;
  alignContent?: string;
  maxWidthChildren?: boolean;
  mbColumns?: number;
}

export const Grid = styled.div<GridProps>`
  display: grid;
  height: ${({ height = "auto" }) => height};

  grid-auto-flow: ${({ flow = "row" }) => flow};
  grid-auto-rows: ${({ minRowHeight }) => minRowHeight && `minmax(${minRowHeight}, auto)`};

  grid-template-columns: ${({ columns = 12 }) => layoutGenerate(columns)};
  grid-template-rows: ${({ rows }) => layoutGenerate(rows)};

  grid-gap: ${({ gap }) => gap}px;
  column-gap: ${({ columnGap }) => columnGap}px;
  row-gap: ${({ rowGap }) => rowGap}px;

  ${({ justifyContent }) => justifyContent && `justify-content: ${justifyContent}`};
  ${({ alignContent }) => alignContent && `align-content: ${alignContent}`};

  ${({ maxWidthChildren }) =>
    maxWidthChildren &&
    `
  div{
    max-width:100%;
  }
  `}

  @media ${device.mobile} {
    grid-template-columns: ${({ mbColumns }) => layoutGenerate(mbColumns)};
  }
`;

export const ButtonFooterSticky = styled.div`
  margin-top: 24px;

  button {
    width: 200px;
    margin: 0 auto;
  }

  @media ${device.mobile} {
    margin-top: 0px;

    padding: 16px;
    width: 100%;
    z-index: 1;
    background: white;

    position: fixed;
    bottom: 0;
    left: 0;

    box-shadow: 0px -1px 0px #dbdfe1;

    button {
      width: 100%;
      max-width: 100%;
    }
  }
`;
