import { createGlobalStyle, css } from "styled-components";
import { device } from "./media";

function renderSpace(type: "margin" | "padding", prefix = "") {
  return [0, 2, 4, 6, 8, 10, 12, 14, 16, 20, 24, 27, 30, 32, 36, 40, 60]
    .map((size) =>
      ["", "-top", "-left", "-bottom", "-right"]
        .map(
          (dir) => `.${prefix}${type.slice(0, 1)}${dir.slice(1, 2)}-${size} { ${type}${dir}: ${size}px !important; }`
        )
        .join("\n")
    )
    .join("\n");
}

const style = css`
  html,
  body {
    font-family:
      "FWD",
      -apple-system,
      "sans-serif",
      "Segoe UI",
      "Roboto",
      "Oxygen",
      "Ubuntu",
      "Cantarell",
      "Fira Sans",
      "Droid Sans",
      "Helvetica Neue";
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-text-size-adjust: 100%;

    width: 100%;
    padding: 0;
    margin: 0;

    color: ${({ theme }) => theme.color.text.body};
  }

  body {
    overflow: hidden !important;
  }

  *,
  :after,
  :before {
    box-sizing: border-box;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  .h7,
  .h8 {
    margin: 0;
    font-weight: 700;
    line-height: 125%;
  }

  h1 {
    font-size: 61px;
  }

  h2 {
    font-size: 49px;
  }

  h3 {
    font-size: 39px;
  }

  h4 {
    font-size: 31px;
  }

  h5 {
    font-size: 25px;
  }

  h6 {
    font-size: 20px;
  }

  .h7 {
    font-size: 16px;
  }

  .h8 {
    font-size: 14px;
  }

  p,
  .body-1,
  .body-2,
  .body-3,
  .body-4,
  .body-5,
  .body-6 {
    margin: 0;
    font-weight: 400;
    line-height: 150%;
  }

  .body-1 {
    font-size: 25px;
  }

  .body-2 {
    font-weight: 500;
    font-size: 20px;
  }

  .body-3 {
    font-size: 20px;
  }

  p,
  .body-4 {
    font-size: 16px;
  }

  .body-5 {
    font-size: 14px;
  }

  .body-6 {
    font-size: 12px;
  }

  a {
    font-weight: 400;
    font-size: 16px;
    line-height: 125%;

    text-decoration: none;
    color: ${({ theme }) => theme.color.text.primary};
  }

  label {
    font-weight: 500;
    font-size: 16px;
    line-height: 125%;
  }

  .label-2 {
    font-weight: 400;
  }

  .label-3 {
    font-weight: 500;
    font-size: 14px;
  }

  .label-4 {
    font-weight: 400;
    font-size: 14px;
  }

  .label-5 {
    font-weight: 500;
    font-size: 12px;
  }

  .label-6 {
    font-weight: 400;
    font-size: 12px;
  }

  .label-7 {
    font-weight: 400;
    font-size: 10px;
  }

  .label-8 {
    font-weight: 400;
    font-size: 10px;
  }

  input,
  textarea,
  select,
  button {
    font-family:
      "FWD",
      -apple-system,
      "sans-serif",
      "Segoe UI",
      "Roboto",
      "Oxygen",
      "Ubuntu",
      "Cantarell",
      "Fira Sans",
      "Droid Sans",
      "Helvetica Neue";
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    font-weight: 700;
    font-size: 16px;
    line-height: 125%;

    border: 0 none;
    outline: 0 none;
    background: transparent;

    cursor: pointer;

    :disabled {
      opacity: 1;
    }
  }

  input {
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
  }

  input,
  textarea {
    color: ${({ theme }) => theme.color.text.body};

    ::placeholder {
      color: ${({ theme }) => theme.color.text.placeholder};
    }
  }

  img {
    max-width: 100%;
    height: auto;
    display: block;
  }

  dl,
  ol,
  ul {
    margin: 0;
    padding-left: 20px;
    color: ${({ theme }) => theme.color.text.body};
  }

  .hide-mobile {
    @media ${device.mobile} {
      display: none !important;
    }
  }

  .hide-desktop {
    @media ${device.noMobile} {
      display: none !important;
    }
  }

  .inline-block {
    display: inline-block !important;
  }

  .color-primary {
    color: ${({ theme }) => theme.color.text.primary} !important;
  }

  .color-error {
    color: ${({ theme }) => theme.color.status.red} !important;
  }

  .color-description {
    color: ${({ theme }) => theme.color.status.grey_darker} !important;
  }

  .color-text-body {
    color: ${({ theme }) => theme.color.text.body} !important;
  }

  .color-grey-darkest {
    color: ${({ theme }) => theme.color.status.grey_darkest} !important;
  }

  /* ${() => {
    // const isMac = /(Mac|iPhone|iPod|iPad)/i.test(navigator.platform);
    // if (!isMac) {
    return css`
      html *::-webkit-scrollbar {
        border-radius: 0;
        width: 5px;
      }
      html *::-webkit-scrollbar-thumb {
        border-radius: 4px;
        background-color: #babac0;
      }
      html *::-webkit-scrollbar-track {
        border-radius: 0;

        background-color: rgba(0, 0, 0, 0);
      }
    `;
    // }
  }} */

  .full-width {
    width: 100%;
  }

  .tooltip-wrapper {
    background-color: transparent;
    margin: 7px;
    margin-top: 10px;
    transition: opacity 0.3s;
    z-index: 2147483647;
  }

  .tooltip-arrow::before {
    display: none;
  }

  .tooltip-arrow[data-placement*="bottom"]::after {
    z-index: 1;
    position: absolute;
    top: 0px;
    left: -7px;
    border-width: 0px 15px 10px 15px;
    border-color: transparent transparent #fef9f4 transparent;
  }

  .text-uppercase {
    text-transform: uppercase;
  }

  .text-lowercase {
    text-transform: lowercase;

    :first-letter,
    :first-line {
      text-transform: capitalize;
    }
  }

  .relative {
    position: relative !important;
  }

  .pointer {
    cursor: pointer;
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .text-medium {
    font-weight: 500 !important;
  }

  .text-bold {
    font-weight: 700 !important;
  }

  .d-flex {
    display: flex;
  }

  .justify-content-center {
    justify-content: center;
  }

  .justify-content-start {
    justify-content: start;
  }

  .justify-content-end {
    justify-content: end;
  }

  ${renderSpace("margin")};
  ${renderSpace("padding")};

  .text-white {
    color: #fff !important;
  }

  @media ${device.mobile} {
    ${renderSpace("margin", "sm-")}
    ${renderSpace("padding", "sm-")}

    .sm-m-0 {
      margin: 0px !important;
    }

    .sm-fw-500 {
      font-weight: 500 !important;
    }

    .sm-bd-solid {
      border: 1px solid #dbdfe1 !important;
    }

    .sm-w-100 {
      width: 100% !important;
      max-width: 100% !important;
    }
  }
`;

export const GlobalStyle = createGlobalStyle`
  ${style}
`;
