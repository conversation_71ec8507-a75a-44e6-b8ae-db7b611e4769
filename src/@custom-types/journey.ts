export type TripModalType = "attend-journey" | "reject-journey" | "history-trip" | "image-gallery" | "";
export type AttendType = "trip" | "conference" | "";

export interface JourneyInput {
  agentId: string;
  channelCd: string;
}

export interface JourneyDetailInput {
  agentId: string;
  eventCode: string;
  eventId: string;
}

export interface JourneyFileUpload {
  agentId: string;
  fileContent: string;
  fileName: string;
  fileType: string;
  mime?: string;
}

export interface RejectJourneyInput {
  agentId: string;
  eventCode: string;
  eventId: string;
  filesUpload?: JourneyFileUpload[];
  reason: any;
}

export interface ListNewJourneyOutPut {
  listData: ListNewJourneyData[];
}

export interface ListNewJourneyData {
  eventCode: string;
  eventId: string;
  eventName: string;
  fromDate: string;
  toDate: string;
  eventType: string;
  regisFrom: string;
  regisTo: string;
  ticketQuatity: number;
}

export interface ListHistoryJourneyOutput {
  listData: ListHistoryJourneyData[];
}

export interface ListHistoryJourneyData {
  agentId: string;
  eventCode: string;
  eventId: string;
  eventName: string;
  fromDate: string;
  toDate: string;
  eventType: string;
  acceptedJourney: string;
  registratedDate: string;
}

export interface AgentInfoJourney {
  listData: AgentInfoJourneyData[];
}

export interface AgentInfoJourneyData {
  agentId: string;
  agentName: string;
  channelCd: string;
  locationOffice: string;
}

export interface RegisterJourneyInput {
  agentId: string;
  eventCode: string;
  eventId: string;
  detailTickets: any;
  ticketQuantity: number;
}

export interface RegisterTripData {
  food: any;
  sizeClothes: any;
  identityType: any;
  identityId: string;
  identityIssueDate: string;
  identityIssuePlace: string;
  identityExpiryDate: string;
  mobileNumber: string;
  address: string;
  contactName: string;
  contactRelationship: any;
  contactPhone: string;
  contactAddress: string;
  roomOnHotel: string;
  roomateAgentId: string;
  roomateAgentName: string;
  roomateOffice: string;
  relativeAgentId: string;
  relativeName: string;
  relativeGender: any;
  relativeRelationship: any;
  filesTicket: JourneyFileUpload[];
  filesTicketRelative: JourneyFileUpload[];
  otherRelativeRelationship: string; //mối quan hệ TVTC được mời KHÁC
  otherRelativeRelationshipUrgent: string; //mối quan hệ Người thân liên hệ khẩn cấp KHÁC
}

export interface ConfirmJourneyDetail {
  eventCode: string;
  eventId: string;
  eventName: string;
  ticketQuatity: string;
  reason: string;
  fromDate: string;
  toDate: string;
  detailTickets: any;
}

export interface ConfirmJourneyDetailData {
  food: string;
  sizeClothes: string;
  identityType: string;
  identityId: string;
  identityIssueDate: string;
  identityIssuePlace: string;
  identityExpiryDate: string;
  mobileNumber: string;
  address: string;
  contactName: string;
  contactRelationship: string;
  contactPhone: string;
  contactAddress: string;
  roomOnHotel: string;
  roomateAgentId: string;
  roomateAgentName: string;
  roomateOffice: string;
  relativeAgentId: string;
  relativeName: string;
  relativeGender: string;
  relativeRelationship: string;
  [key: string]: string;
}

/** DROPDOWN */

export interface DropdownJourneyForm {
  data: DropdownFormData;
}

export interface DropdownFormData {
  FOOD: DropdownJourneyFormDataValue[];
  GENDER: DropdownJourneyFormDataValue[];
  IDENTITYTYPE: DropdownJourneyFormDataValue[];
  RELATIVE_RELATIONSHIP: DropdownJourneyFormDataValue[];
  SIZECLOTHES: DropdownJourneyFormDataValue[];
}

export interface DropdownJourneyFormDataValue {
  typeValue: string;
  valueId: string;
  valueName: string;
}
