import { PerformanceType } from "./performance";

//Hierarchy
export interface HierarchyInput {
  agentId: string;
}
export interface HierarchyOutput {
  listData: HierarchyData[];
}

export interface HierarchyData {
  agentCode: string;
  agentName: string;
  designationCd: string;
  supervisorId: string;
  level: string;
  manager: boolean;
  branchLvl: number;
  children?: HierarchyData[];
}

//FYP less than 10 millions
export interface FYPLessThan10MilInput {
  agentId: string;
  month: string;
  typeMonth: string;
  year: string;
}

export interface FYPLessThan10MilOutput {
  listData: FypLessThan10MilData[];
}

export interface FypLessThan10MilData {
  agentCode: string;
  agentName: string;
  designationCd: string;
  supervisorId: string;
  supervisorName: string;
  fwmCode: string;
  fwmName: string;
  fwmDesignationCd: string;
  fwdCode: string;
  fwdName: string;
  fwdDesignationCd: string;
  fyp: number;
}

//contribute page
export interface TeamInput {
  agentId: string;
}

export interface TeamSetTargetInput {
  agentId?: string;
  agentCode?: string;
  fyp: number;
  cases: number;
  ape: number;
}

export interface FypTeamOutput {
  fypTeam: number;
}

export interface FypTeamData {
  agentCode: string;
  agentName: string;
  designation: string; //CUBE-471
  fyp: number;
  rank: number;
  cases?: number;
  rankFypOnRootTeam: number;
  rankCaseOnRootTeam: number;
}

export interface Top10Output {
  listData: FypTeamData[];
}

//home team page

export interface TotalMemberOutput {
  totalAgent: number;
}

export interface Top3FYPData {
  agentCode: string;
  agentName: string;
  designation: string;
  fyp: number;
  rank: number;
  rankFypOnRootTeam: number;
}

export interface Top3FYPOutput {
  listData: Top3FYPData[];
}

//modal sale personal page
export interface AgentInfoData {
  agentCode: string;
  agentName: string;
  k2Rate: number;
  fyp: number;
  cases: number;
  email: string;
  phoneNumber: string;
  designation: string; //CUBE-471
}

export interface AgentInfoOutput {
  data: AgentInfoData;
}

//modal edit
export interface TargetForMemberData {
  agentCode: string;
  agentName: string;
  lastYearFYP: number;
  lastYearCases: number;
  lastYearApe: number;
  lastYear: number;
  targetFyp: number;
  targetCases: number;
  targetApe: number;
  thisYear: number;
}

export interface TargetForMemberOutput {
  listData: TargetForMemberData[];
}

export interface TargetForTeamData {
  agentCode: string;
  lastYearFYP: number;
  lastYearCases: number;
  lastYearApe: number;
  lastYear: number;
  targetFyp: number;
  targetCases: number;
  targetApe: number;
  thisYear: number;
}

export interface TargetForTeamOutput {
  data: TargetForTeamData;
}

export interface TeamPerformanceChannelProps {
  dataDetail: any;
  dataChartSales?: any;
}

export interface OverViewTeamDetailInput {
  payload: OverviewTeamInput;
  type: PerformanceType;
}

//Overview Agency
export interface OverviewTeamInput {
  agentId: string;
  channelCd: string;
  day: string;
  month: string;
  year: string;
}
export interface OverviewTeamAgencyMonthlyOutput {
  data: OverviewTeamAgencyMonthlyData;
}
export interface OverviewTeamAgencyMonthlyData {
  agentCode: string;
  agentName: string;
  fyp: number;
  agentNewJoin: number;
  agentTerminated: number;
  totalAgentOnlySA: number;
  totalAgentIncludingSA: number;
  totalAgentNotIncludingSA: number;
  agentActiveIP: number;
  morethan1Case: number;
  fypLessthan10Cur: number;
  fypLessthan10In2Month: number;
  fypLessthan10In3Month: number;
  totalAgentActive: number;
  agentActiveNewInforce: number;
  agentActiveOldInforce: number;
  agentNotActive: number;
  submitIP: number;
  submitApe: number;
  submitCases: number;
  pendingIP: number;
  pendingApe: number;
  pendingCases: number;
  pendingPreMonthIP: number;
  pendingPreMonthApe: number;
  pendingPreMonthCases: number;
  issueIP: number;
  issueApe: number;
  issueCases: number;
  adjIP: number;
  adjApe: number;
  adjCases: number;
  cancelIP: number;
  cancelApe: number;
  cancelCases: number;
  netIssueIP: number;
  netIssueApe: number;
  netIssueCases: number;
  fwdlt: number;
  fwm: number;
  fwo: number;
  fwp: number;
  dfwo: number;
  fwd: number;
  dfwp: number;
}

export interface OverviewQTDAgency {
  agentActiveIP: number;
  agentCode: string;
  agentName: string;
  agentNewJoin: number;
  agentTerminated: number;
  dfwo: number;
  dfwp: number;
  fwd: number;
  fwdlt: number;
  fwm: number;
  fwo: number;
  fwp: number;
  fyp: number;
  issueApe: number;
  issueCases: number;
  issueIP: number;
  morethan1Case: number;
  netIssueApe: number;
  netIssueCases: number;
  netIssueIP: number;
  submitApe: number;
  submitCases: number;
  submitIP: number;
  totalAgentIncludingSA: number;
  totalAgentNotIncludingSA: number;
  totalAgentOnlySA: number;
}

export interface OverviewAgencyYTDOutput {
  data: OverviewYTDAgency;
}

export interface OverviewYTDAgency {
  agentCode: string;
  agentName: string;
  fyp: number;
  fwdlt: number;
  fwd: number;
  fwm: number;
  fwo: number;
  fwp: number;
  dfwo: number;
  dfwp: number;
  agentActiveIP: number;
  morethan1Case: number;
  totalAgentOnlySA: number;
  totalAgentIncludingSA: number;
  totalAgentNotIncludingSA: number;
  submitIP: number;
  submitApe: number;
  submitCases: number;
  netIssueIP: number;
  netIssueApe: number;
  netIssueCases: number;
  //table rookies
  rookieHeader: RookieHeader;
  rookieContent: RookieContent[];
  //chart
  fypChart: FYPChartData[];
  apeChart: APEChartData[];
}

export interface RookieHeader {
  recruit: string;
  retain: string;
  retainExcludeSa: string;
  col0: number;
  col1: number;
  col2: number;
  col3: number;
  col4: number;
  col5: number;
  col6: number;
  col7: number;
  col8: number;
  col9: number;
  col10: number;
  col11: number;
}

export interface RookieContent {
  mmYYYY: string;
  recruit: number;
  retain: number;
  retainExcludeSa: number;
  col0: number;
  col1: number;
  col2: number;
  col3: number;
  col4: number;
  col5: number;
  col6: number;
  col7: number;
  col8: number;
  col9: number;
  col10: number;
  col11: number;
}

export interface FYPChartData {
  mmyyyy: string;
  allTeamFyp: number;
  directTeamFyp: number;
  agentCode: string;
}

export interface APEChartData {
  mmyyyy: string;
  allTeamApe: number;
  directTeamApe: number;
  agentCode: string;
}

//KPI Overview
export interface KPIOverviewTeamOutput {
  data: KPIOverviewTeamData;
}

export interface KPIOverviewTeamData {
  fyp: number;
  ape: number;
  targetFyp: number;
  targetApe: number;
}

//Overview Banca
export interface OverviewBancaDailyOutput {
  data: OverviewBancaDailyData;
}
export interface OverviewBancaDailyData {
  agentCode: string;
  agentName: string;
  submitIP: number;
  submitApe: number;
  submitCases: number;
  pendingIP: number;
  pendingApe: number;
  pendingCases: number;
  issueIP: number;
  issueApe: number;
  issueCases: number;
  cancelIP: number;
  cancelApe: number;
  cancelCases: number;
  salesByBranch: [];
  onlineCancelApe: number;
  onlineCancelCaseCount: number;
  onlineCancelIp: number;
  onlineIssueApe: number;
  onlineIssueCaseCount: number;
  onlineIssueIp: number;
  onlinePendingApe: number;
  onlinePendingCaseCount: number;
  onlinePendingIp: number;
  onlineSubmitApe: number;
  onlineSubmitCaseCount: number;
  onlineSubmitIp: number;
  dulcancelApe: number;
  dulcancelCaseCount: number;
  dulcancelIp: number;
  dulissueApe: number;
  dulissueCaseCount: number;
  dulissueIp: number;
  dulpendingApe: number;
  dulpendingCaseCount: number;
  dulpendingIp: number;
  dulsubmitApe: number;
  dulsubmitCaseCount: number;
  dulsubmitIp: number;
}

export interface OverviewBancaMonthlyOutput {
  data: OverviewBancaMonthlyData;
}

export interface OverviewBancaMonthlyData {
  agentCode: string;
  agentName: string;
  agentTerminated: number;
  agentActiveIP: number;
  totalAgentActive: number;
  submitIP: number;
  submitApe: number;
  submitCases: number;
  pendingIP: number;
  pendingApe: number;
  pendingCases: number;
  issueIP: number;
  issueApe: number;
  issueCases: number;
  adjIP: number;
  adjApe: number;
  adjCases: number;
  cancelIP: number;
  cancelApe: number;
  cancelCases: number;
  netIssueIP: number;
  netIssueApe: number;
  netIssueCases: number;
  salesByBranch: [];
}

export interface OverviewBancaYearlyOutput {
  data: OverviewBancaYearlyData;
}

export interface OverviewBancaYearlyApeChartData {
  agentCode: string;
  mmyyyy: string;
  allTeamApe: number;
  directTeamApe: number;
}

export interface OverviewBancaYearlyFypChartData {
  agentCode: string;
  mmyyyy: string;
  allTeamFyp: number;
  directTeamFyp: number;
}

export interface OverviewBancaYearlyData {
  agentCode: string;
  agentName: string;
  fyp: number;
  totalAgentActive: number;
  agentInforce: number;
  agentActiveIP: number;
  submitIP: number;
  submitApe: number;
  submitCases: number;
  netIssueIP: number;
  netIssueApe: number;
  netIssueCases: number;
  apeChart: OverviewBancaYearlyApeChartData[];
  fypChart: OverviewBancaYearlyFypChartData[];
}

//ifa

export interface IfaDesignation {
  agentCode: string;
  yyyymm: string;
  headerName: string;
  designationCd: string;
  cnt: number;
}

export interface OverviewTeamIfaMonthlyData {
  agentCode: string;
  agentName: number;
  fyp: number;
  agentActiveIP: number;
  fyplessthan10Cur: number;
  fyplessthan10In2Month: number;
  fyplessthan10In3Month: number;
  totalAgentActive: number;
  agentActiveNewInforce: number;
  agentActiveOldInforce: number;
  agentNotActive: number;
  submitIP: number;
  submitApe: number;
  submitCases: number;
  pendingIP: number;
  pendingApe: number;
  pendingCases: number;
  pendingPreMonthIP: number;
  pendingPreMonthApe: number;
  pendingPreMonthCases: number;
  issueIP: number;
  issueApe: number;
  issueCases: number;
  adjIP: number;
  adjApe: number;
  adjCases: number;
  cancelIP: number;
  cancelApe: number;
  cancelCases: number;
  netIssueIP: number;
  netIssueApe: number;
  netIssueCases: number;
  ifaDesignations: IfaDesignation[];
  totalAgentOnlySA: number;
  agentNewJoin: number;
  agentTerminated: number;
  totalAgentIncludingSA: number;
  totalAgentNotIncludingSA: number;
}

export interface OverviewTeamIfaMonthlyOutput {
  data: OverviewTeamIfaMonthlyData;
}
export interface OverviewBancaBOTOOutput {
  listData: OverviewBancaBOTOData[];
}

export interface OverviewBancaBOTOData {
  yyyyMM: string;
  agentCode: string;
  agentName: string;
  designationCd: string;
  type: string;
  supervisorId: null;
  ip: number;
  ape: number;
  cc: number;
  caseSize: number;
  fyp: number;
  botoInforced: number;
  botoActived: number;
  botoPercent: number;
  k2Rate: number;
  //add for render table
  children?: OverviewBancaBOTOData[];
  level?: number;
  depth?: number;
}

export interface OverviewTeamIfaYearlyData {
  agentCode: string;
  agentName: string;
  fyp: number;
  agentActiveIP: number;
  submitIP: number;
  submitApe: number;
  submitCases: number;
  netIssueIP: number;
  netIssueApe: number;
  netIssueCases: number;
  ifaDesignations: IfaDesignation[];
  fypChart: FYPChartData[];
  apeChart: APEChartData[];
  totalAgentOnlySA: number;
  agentNewJoin: number;
  agentTerminated: number;
  totalAgentIncludingSA: number;
  totalAgentNotIncludingSA: number;
}

export interface OverviewTeamIfaYearlyOutput {
  data: OverviewTeamIfaYearlyData;
}

export interface AgentInfoPerformanceInput extends TeamInput {
  channelCd: string;
}

/** CUBE 204 DANH SÁCH PHỤC VỤ TƯ VẤN */
export interface ReportSAInput {
  agentCode: string;
  designationCd: string;
  month: string;
  year: string;
}

export interface ListSAData {
  agentId: string;
  agentName: string;
  birthDate: string;
  branchId: string;
  designationCd: string;
  directSupervisorCode: string;
  directSupervisorDesignationCd: string;
  directSupervisorName: string;
  dispatchAddress: string;
  email: string;
  eziLock: string;
  fwdCode: string;
  fwdName: string;
  fwmCode: string;
  fwmName: string;
  fwoCode: string;
  fwoName: string;
  genderCd: string;
  joiningDate: string;
  mobileNumber: string;
  permanentAddress: string;
  regionCd: string;
  saChangeDate: string;
  totalMonthFromJoiningDate: number;
  totalMonthFromSaChangeDate: number;
}

export interface LOSChartData {
  agentTotal: number;
  losBetween0And6Month: number;
  losBetween12And24Month: number;
  losBetween6And12Month: number;
  losGreaterThan24Month: number;
}

export interface SAChartData {
  saMonthBetween0And3Month: number;
  saMonthBetween3And6Month: number;
  saMonthBetween6And12Month: number;
  saMonthGreaterThan12Month: number;
  saTotal: number;
}

//Not include SA

export interface NoSAData {
  agentId: string;
  agentName: string;
  agentStatus: string;
  avpSrRdCode: string;
  avpSrRdName: string;
  birthDate: string;
  branchId: string;
  dadCode: string;
  dadName: string;
  designationCd: string;
  designationChangeDate: string;
  directSupervisorCode: string;
  directSupervisorDesignationCd: string;
  directSupervisorName: string;
  drdCode: string;
  drdName: string;
  fwdCode: string;
  fwdName: string;
  fwdltCode: string;
  fwdltName: string;
  fwmCode: string;
  fwmName: string;
  fwoCode: string;
  fwoName: string;
  genderCd: string;
  joiningDate: string;
  mobileNumber: string;
  rdCode: string;
  rdName: string;
  regionCd: string;
  srAdCode: string;
  srAdName: string;
}
