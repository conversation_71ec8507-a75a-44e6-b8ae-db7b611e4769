export interface TrainingClassSearchInput {
  agentId: string;
  classId: string;
  className: string;
  fromDate: string;
  toDate: string;
}

export interface ListRegisTrainingOutput {
  listData: ListRegisTrainingData[];
}
export interface ListRegisTrainingData {
  classId: string;
  className: string;
  dayStart: number;
  hourStart: string;
  dayEnd: number;
  hourEnd: string;
  detail: string;
  isRegis: string;
}

export interface RegisAndCancelInput {
  agentId: string;
  listClassIdsRegis: string[];
  listClassIdsCancel: string[];
}
