export type SOAType =
  | "total-income"
  | "commission-tab"
  | "detail-commission"
  | "policy-reward"
  | "emulation-bonus"
  | "adjust"
  | "reason-detain"
  | "introduction-reward"
  | "monthly-allowance"
  | "commission-detail";

export type SOAPolicyRewardType =
  | "month-bonus"
  | "quarter-bonus"
  | "persistency-bonus"
  | "management-bonus"
  | "dev590-bonus"
  | "qm-bonus"
  | "sales-first-year-fsc-reward"
  | "sales-second-year-fsc-reward"
  | "sales-quarterly-fsc-reward"
  | "business-year-fsc-reward"
  | "business-month-bsm-reward"
  | "business-quarterly-bsm-reward"
  | "bonus-support-team"
  | "leader-persisbon"
  | "business-year-bsm-reward";

export type SOAMonthlyAllowanceType =
  | "number-pgd-allocated-bsm"
  | "number-active-pgd-of-bsm"
  | "number-fsc-beginning"
  | "number-fsc-midterm"
  | "number-fsc-end";

export interface ErrorSOA {
  month?: string;
}

export interface SOAInput {
  agentId: string;
  paymentDt: string;
  typeCd?: string;
}

export interface SOADocumentInput {
  agentId: string;
  channelCd: string;
}

export interface SOADocumentData {
  data: string[];
}

export interface SOACompensationInput {
  agentId: string;
  language: string;
  listDocType: SOADocumentData;
  month: number;
  year: number;
}

export interface SOACompensationData {
  agentId: string;
  longDesc: string;
  issueDate: number;
  awplIndex: string;
  agentName: string;
  documentCd: string;
  designationCd: string;
}
export interface SOADownloadPDFInput {
  agentId: string;
  awplIndex: string;
}

export interface SOAValue {
  description: string;
  amount: number;
}

export interface BSMFSCMonthlyValue {
  branchId: string;
  branchName: string;
}

export interface ListDataBSMFSCBonus {
  polNum: string;
  issuedDate: number;
  insuranceFee: number;
  ackDate: number;
}

// Tổng thu nhập
export interface TotalIncomeAgencyData {
  month: string;
  year: string;
  period: string;
  fromDate: string;
  toDate: string;
  agentName: string;
  agentCode: string;
  joiningDate: string;
  officeName: string;
  parentName: string;
  adName: string;
  positionName: string;
  status: string;
  taxNumber: string;
  bankName: string;
  bankBranch: string;
  bankAccount: string;
  income: number;
  commisionFirstYear: number;
  bonusBeSupleProduct: number;
  commisionContinuous: number;
  bonusQuarter: number;
  bonusMaintainPol: number;
  monthBonus: number;
  clb590Bonus: number;
  incomeManageAgent: number;
  bonusNewManage: number;
  bonusQuarterManager: number;
  bonusPromotion: number;
  bonusNewDevelop: number;
  bonusTransferFWD: number;
  dev590Bonus: number;
  lifeBenIncome: number;
  incomeOther: number;
  bonusByCash: number;
  bonusNotByCash: number;
  adjust: number;
  totalTaxableIncome: number;
  deduct: number;
  personalIncomeTax: number;
  examFeeShineClass: number;
  deducBonusNotByCash: number;
  deductOther: number;
  ipadDeduct: number;
  acdpemdpDeduct: number;
  otherDeduct: number;
  incomeNonTax: number;
  incomeHoldInPreMonth: number;
  incomeFromBank: number;
  taxRefund: number;
  nonTaxAdjust: number;
  incomePeriod: number;
  balanceAmt: number;
  incomeHoldPeriod: number;
  incomeActual: number;
  totalTaxableIncomeInYear: number;
  totalTaxIncomeInYear: number;
  is_show: string;
  flgAdj: string;
  acdp: number;
  emdp: number;
  ifwdreferral: number;
  incomeOtherPs: number;
  bonusByCashPs: number;
  bonusNotByCashPs: number;
  adjustPs: number;
  incomeOtherAl: number;
  bonusByCashAl: number;
  bonusNotByCashAl: number;
  adjustAl: number;
  psTax: number;
  alTax: number;
  incomeActualInd: number; //8.1. Vai trò Cá nhân kinh doanh
  incomeActualHkd: number; //8.2. Vai trò Hộ Kinh doanh
  totalTaxableIncomeInYearPs: number;
  totalTaxDeductInYearPs: number;
  totalTaxableIncomeInYearAl: number;
  totalTaxDeductInYearAl: number;
  taxAndActualPaymentInd: number; // 7.1. Vai trò Cá nhân kinh doanh
  taxAndActualPaymentHkd: number; //7.2. Vai trò Hộ Kinh doanh
  carryAmountInd: number; // 9.1. Vai trò Cá nhân kinh doanh
  carryAmountHkd: number; //9.2. Vai trò Hộ Kinh doanh
  leaderPersisbon: number; //Thù lao hỗ trợ thu phí tái tục năm hai
  bonusSupportTeam: number; // thù lao hỗ trợ nhóm
}
// End tổng thu nhập

// Hoa hồng
export interface TotalFirstYearCom {
  premium: number;
  bonusBenefit: number;
  totalPay: number;
  commission: number;
  // [key: string]: any;
}

export interface LstComFirstYear {
  polnum: string;
  owName: string;
  incurredDate: number;
  premium: number;
  commission: number;
  bonusBenefit: number;
  totalPay: number;
}

export interface TotalRemainCom {
  premium: number;
  commission: number;
  bonusBenefit: number;
  totalPay: number;
}

export interface LstRemainCom {
  polnum: string;
  owName: string;
  incurredDate: number;
  premium: number;
  commission: number;
  bonusBenefit: number;
  totalPay: number;
}

export interface CommissionFirstYearData {
  totalFirstYearCom: TotalFirstYearCom;
  lstComFirstYear: LstComFirstYear[];
  totalRemainCom: TotalRemainCom;
  lstRemainCom: LstRemainCom[];
  designationCdWithDate: string; //Chức danh của TVTC tại thời điểm check request
}

// End Hoa hồng

//Chi tiết hoa hồng
export interface CommissionIncurOutput {
  data: CommissionIncurData;
  designationCdWithDate: string;
}

export interface CommissionIncurData {
  lstIncurreCom: IncurComData[];
  incurreComTotal: number;
  totalIncurreCom: IncurComData;
}

export interface IncurComData {
  ackDate: string;
  agentId: string;
  bonusBenefit: number;
  comType: string;
  commission: number;
  date21: string;
  firstIssueDate: string;
  incurredDate: string;
  owName: string;
  polnum: string;
  premium: number;
  productCode: string;
  productName: string;
  ratioCom: number;
  totalPay: number;
}

//End Chi tiết hoa hồng

//Hoa hồng tạm tính - Chi tiết
export interface CommissionProvisionalInput {
  agentId: string;
  dateType: any;
  fromDate: string;
  owName: string;
  policyNo: string;
  toDate: string;
}

export interface CommissionProvisionalOutput {
  data: CommissionProvisionalData;
}

export interface CommissionProvisionalData {
  provisionalComTotal: number;
  lstProvisionalCom: ProvisionalComData[];
  totalProvisionalCom: ProvisionalComData;
}

export interface ProvisionalComData {
  ackDate: number;
  agentId: string;
  bonusBenefit: number;
  comType: string;
  commission: number;
  date21: number;
  firstIssueDate: number;
  incurredDate: number;
  owName: string;
  polnum: string;
  premium: number;
  productCode: string;
  productName: string;
  ratioCom: number;
  totalPay: number;
}
// End Hoa hồng tạm tính - Chi tiết

// Thưởng theo chính sách (*)
export interface PolicyBonusAgency {
  bonusAgent: number;
  bonusQuarter: number; // Thưởng quý cá nhân => update mapping commnet jira-724
  bonusMaintainPol: number; // thưởng duy trì hop dong bảo hiểm => update mapping commnet jira-724
  incomeManageAgent: number; // 2. Các khoản thưởng dành cho quản lý TVTC => update mapping commnet jira-724
  bonusNewManage: number; // Thù lao quản lý hoạt động khai thác mới nhóm trực tiếp => update mapping commnet jira-724
  bonusQuarterManager: number; // Thù lao hàng quý => update mapping commnet jira-724
  bonusPromotion: number; // Thù lao phát triển quản lý cùng cấp => update mapping commnet jira-724
  bonusNewDevelop: number; // Thù lao phát triển quản lý mới FWO => update mapping commnet jira-724
  bonusTransferFWD: number; // Quyền lợi chuyển giao đội ngũ => update mapping commnet jira-724
  monthBonus: number;
  clb590Bonus: number;
  dev590Bonus: number;
  lifeBenIncome: number;
  bonusSupportTeam: number; // Thù lao hỗ trợ nhóm => update mapping commnet jira-724
  leaderPersisbon: number; // Thù lao hỗ trợ phí tái tục năm 2 => update mapping commnet jira-724
}

// Thưởng tháng
export interface MonthlyBonus {
  totalFypPs: number;
  k2Persistently: number;
  totalNetCase: number;
  bonusRate: number;
  totalFycPs: number;
  mpbFwp: number;
  otherAdjustment: number;
}

export interface MonthlyBonusDetail {
  agentCode: string;
  policyNumber: string;
  fyp: number;
  fyc: number;
  owName: string;
}

export interface PolicyBonusMonthDetailData {
  monthlyBonus: MonthlyBonus;
  monthlyBonusDetail: MonthlyBonusDetail[];
  totalFyp: number;
  totalFyc: number;
}
// End Thưởng tháng

// Thưởng cá nhân (Thưởng quý cho nhân viên)
export interface lstPolicyBonus {
  agentCode: string;
  policyNumber: string;
  fyp: number;
  comFY: number;
  owName: string;
}

export interface PolicyBonusQuarterDetailData {
  fpyQuarter: number;
  fycQuarter: number;
  activeRound: number;
  qpbRatioBonus: number;
  ratioPersistency: number;
  rateBonus: number;
  qpb: number;
  adjust: number;
  lstPolicyBonus: lstPolicyBonusQuarter[];
  totalFyp: number;
  totalFyc: number;
  agyQpB590: number;
}

// End thưởng cá nhân (Thưởng quý cho nhân viên)

// Thưởng duy trì hợp đồng bảo hiểm
export interface PersistencyBonus {
  totalSyc: number;
  k2Persistency: number;
  bonusRate: number;
  persistencyBonus: number;
  otherAdjustment: number;
}

export interface PersistencyDetails {
  agentCode: string;
  policyNumber: string;
  fyp: number;
  fyc: number;
  owName: string;
}

export interface PersistencyDetailData {
  persistencyBonus: PersistencyBonus;
  persistencyDetails: PersistencyDetails[];
}
// End Thưởng duy trì hợp đồng bảo hiểm

// Thưởng quản lý khai thác mới
export interface OverrideY1 {
  totalDTFyp: number; //1. FYP nhóm trực tiếp thực đạt trong tháng
  totalDTFyc: number; // 2. FYC nhóm trực tiếp thực đạt trong tháng
  multiFactor: number; //3. Hệ số nhân
  incentive: number; // 4. Thực nhận tháng này: (*)
  adjustAmount: number; // 5. Các khoản điều chỉnh (nếu có):
}

export interface OverrideDetails {
  agentId: number;
  designationCd: number;
  sourceAgentId: number;
  policyNumber: number;
  whole_team_fyp: number;
  whole_team_fyc: number;
  whole_team_fyc_direct: number;
  whole_team_fyc_indirect: number;
}

export interface PolicyBonusManagerDetailData {
  overrideY1: OverrideY1;
  lsOverrideDetails: OverrideDetails[];
  designationCdWithDate: string;
}

// Thưởng phát triển thành viên CLB 590
export interface Dev590Bonus {
  dev590Bonus: number;
  otherAdjustment: number;
}

export interface Dev590BonusDetail {
  parentAgentCode: string;
  agentCode: string;
  agentName: string;
  joinDate: Date;
  servPeriod: string;
  rookiePolicy: number;
  totalFypPs: number;
  totalFycPs: number;
  bonusCLB590: number;
}

export interface Dev590BonusDetailData {
  dev590Bonus: Dev590Bonus;
  dev590BonusDetail: Dev590BonusDetail[];
}
// End Thưởng phát triển thành viên CLB 590

// Thưởng cho cấp quản lý
export interface PolicyBonusQM {
  data: {
    incomeSum: PolicyBonusQMData;
    lstDetailBonusQM: {
      agentId: string;
      sourceAgentId: string; //đại lý khai thác
      policyNumber: string;
      fyc: number;
      fyp: number;
      designationCd: string;
    }[];
  };
  designationCdWithDate: string;
}
export interface PolicyBonusQMData {
  rateBonus?: number;
  qpbamount?: number;
  adjQPB?: number;
  k2Persistency?: number;
  ratio: number;
  totalFyp?: number;
  totalFyc?: number;
}
// End Thưởng cho cấp quản lý

//Thù lao hỗ trợ nhóm
export interface PolicyBonusSupportTeam {
  indirectORY1: {
    incentive_Indirect: number;
    adjustAmount: number;
    valueTier2: number;
    valueTier3: number;
  };
  indirectORY1Details: {
    agentId: number;
    designationCd: string;
    sourceAgentId: number;
    incentive: number;
    multiFactor: number;
  }[];
  // designationCdWithDate: string;
}

//Thù lao hỗ trợ thu phí tái tục năm hai
export interface PolicyBonusLeaderPersisbon {
  persistencyLeader: {
    leaderPersisBonus: number;
    adjustAmount: number;
    persistencyBonus: number;
    multiFactor: number;
  };
  persistencyLeaderDetails: {
    agentId: number;
    designationCd: number;
    sourceAgentId: number;
    persistencyBonus: number;
  }[];
  // designationCdWithDate: string;
}

// End Thưởng theo chính sách (*)

// Thưởng thi đua
export interface ContentBonusData {
  lstBonusByCash: SOAValue[];
  lstBonusNotByCash: SOAValue[];
  lstDeductBonusNotByCash: SOAValue[];
}
// End Thưởng thi đua

// Điều chỉnh khác
export interface IncomeAdjustmentData {
  lstAdjustDetails: SOAValue[];
  lstDeductOthers: SOAValue[];
  lstBankReturns: SOAValue[];
  lstAdjustOthers: SOAValue[];
}
// End Điều chỉnh khác

// Lý do tạm giữ
export interface LstHold {
  contentName: string;
  amount: number;
  contactDetail: string;
}
export interface HoldReasonData {
  lstHold: LstHold[];
}
// End Lý do tạm giữ

// Thưởng giới thiệu sản phẩm
export interface TotalReferral {}
export interface TotalPremium {}
export interface LsReferralBonus {
  policyNumber: string;
  productCode: string;
  productName: string;
  firstIssueDate: number; //date
  payableDate: number; //date
  ackDate: number; //date
  passAckDate: number; //date
  premium: number;
  rate: number;
  referralBonus: number;
  policyYear: number;
  owName: string;
}
export interface IncomeCommissionReferralData {
  totalReferral: TotalReferral;
  totalPremium: TotalPremium;
  lsReferralBonus: LsReferralBonus[];
}
// End Thưởng giới thiệu sản phẩm

// Download bảng lương
export interface DataForExportExcel {}
// End Download bảng lương

// BANCA
// Tổng thu nhập BANCA
export interface TotalIncomeBancaData {
  month: string;
  year: string;
  period: string;
  fromDate: string;
  toDate: string;
  agentCode: string;
  agentName: string;
  position: string;
  status: string;
  bankAccount: string;
  branchName: string;
  bankName: string;
  totalTaxableIncome: number;
  allowFixMonth: number;
  bonusSalesFirstYear: number;
  bonusSalesSecondYear: number;
  healthCareSupport: number;
  bonusOfQuarterly: number;
  bonusSalesAnnual: number;
  bonusEmulation: number;
  othersIncome: number;
  taxIncurredInMonth: number;
  totalNonTaxBlInc: number;
  incomeHoldInPreMonth: number;
  incomeFromBank: number;
  othersAdjust: number;
  taxRefund: number;
  incomeActual: number;
  contractRetentionRate: number;
  fscRetention: number;
  totalAck01: number;
  totalAck02: number;
  totalFypSecondYear: number;
  personalSupport: number;
  ipAverageInMonth: number;
  perOfOperationAck01: number;
  perOfOperationAck02: number;
  numOfActivities: number;
  perOfManagerSupport: number;
  contractRetentionK2: number;
  ipAverageInQuarter: number;
  totalFypAck02: number;
  numOfActInQuarter: number;
  numOfIssuedQuarter: number;
  perOfProductManager: number;
  perOfContractInYear: number;
  totalAck02InYear: number;
}
// End Tổng thu nhập BANCA

// Trợ cấp cố định tháng (*)
export interface IncomeAllowance {
  allowFixMonth: number;
  totalFeeInYear: number;
  numOfUnitAllocated: number;
  numOfUnitOperation: number;
  numOfFscBeginPeriod: number;
  numOfFscInPeriod: number;
  numOfFscEndPeriod: number;
  perOfOperationInMonth: number;
  perOfRetentionFsc: number;
  position: string;
  numOfUnitAllocatedAgbGrp1: number;
  numOfUnitOperationAgbGrp1: number;
  numOfUnitAllocatedAgbGrp2: number;
  numOfUnitOperationAgbGrp2: number;
  botoFsc: number;
  botoNoFsc: number;
}

export interface LstContract {
  polNum: string;
  issuedDate: number;
  insuranceFee: number;
  ackDate: number;
}

export interface BancaAllowanceFixMonthData {
  incomeAllowance: IncomeAllowance;
  lstContract: LstContract[];
}

// Số lượng PGD/CN được phân bổ cho BSM
export interface BancaAllowanceDistributedData {
  listData: BSMFSCMonthlyValue;
}
// End Số lượng PGD/CN được phân bổ cho BSM

// Số lượng PGD/CN hoạt động của BSM
export interface BancaAllowanceOperationData {
  listData: BSMFSCMonthlyValue;
}
// End Số lượng PGD/CN được phân bổ cho BSM

// Số lượng FSC đầu/giữa/cuối kỳ
export interface ListDataFSC {
  fscCode: string;
  fscName: string;
}
export interface BancaAllowanceFSCData {
  listData: ListDataFSC[];
}
// End Số lượng FSC đầu/giữa/cuối kỳ
// End trợ cấp cố định tháng (*)

// Thưởng theo chính sách BANCA (*)
export interface PolicyBonusBanca {
  bonusSalesFirstYear: number;
  bonusSalesSecondYear: number;
  bonusSalesQuarterly: number;
  bonusSalesAnnual: number;
  bonusBusinessMonthly: number;
  bonusBusinessQuarterly: number;
  bonusBusinessYearly: number;
}

// FSC:Thưởng tháng doanh số năm nhất, BSM: Thưởng tháng
export interface BonusFirstYearData {
  listData: ListDataBSMFSCBonus[];
}
// End FSC:Thưởng tháng doanh số năm nhất, BSM: Thưởng tháng

// FSC:Thưởng tháng theo doanh số năm hai
export interface BonusSecondYearData {
  listData: ListDataBSMFSCBonus[];
}
// End FSC:Thưởng tháng theo doanh số năm hai

// FSC:Thưởng doanh số quý, BSM:Thưởng kinh doanh quý
export interface BonusByQuarterData {
  listData: ListDataBSMFSCBonus[];
}
// End FSC:Thưởng doanh số quý, BSM:Thưởng kinh doanh quý

// FSC:Thưởng doanh số năm, BSM:Thưởng kinh doanh năm
export interface BonusByYearData {
  listData: ListDataBSMFSCBonus[];
}
// End FSC:Thưởng doanh số năm, BSM:Thưởng kinh doanh năm
// End Thưởng theo chính sách BANCA (*)

// Thưởng thi đua BANCA
export interface BancaEmulationBonusData {
  totalAmount: number;
  lstBonusbyCash: SOAValue[];
  lstBonusNotbyCash: SOAValue[];
}
// End Thưởng thi đua BANCA

// Điều chỉnh BANCA
export interface BancaAdjustmentBonusData {
  totalAmount: number;
  lstBonusAdjustment: SOAValue[];
  lstDeductOthers: SOAValue[];
}
// End Điều chỉnh BANCA

//Excel SOA

export interface IncomeProData extends ProvisionalComData {
  updatetodt: string;
}
export interface SOAExcelDownLoadOutput {
  incomeSum: {
    data: TotalIncomeAgencyData;
    designationCdWithDate: string; //Chức danh của TVTC tại thời điểm check request
  };
  incomeComFY: {
    data: {
      totalFirstYearCom: TotalFirstYearCom;
      lstComFirstYear: LstComFirstYear[];
      totalRemainCom: TotalRemainCom;
      lstRemainCom: LstRemainCom[];
    };
    designationCdWithDate: string; //Chức danh của TVTC tại thời điểm check request
  };
  incomeComPro: {
    data: {
      provisionalComTotal: number;
      totalProvisionalCom: IncomeProData[];
      lstProvisionalCom: IncomeProData[];
    };
    designationCdWithDate: string;
  };
  incomeComIncure: {
    data: CommissionIncurData;
    designationCdWithDate: string;
  };
  incomePolicyBonus: {
    data: PolicyBonusAgency;
  };
  incomeAdjust: {
    data: IncomeAdjustmentData;
  };
  incomeHold: {
    lstHold: LstHold[];
  };
  incomeReferral: {
    data: IncomeCommissionReferralData;
    designationCdWithDate: string;
  };
  incomePolicyBonusQuarter: {
    data: PolicyBonusQuarterDetailData;
    designationCdWithDate: string;
  };
  incomePolBonusManager: {
    data: PolicyBonusManagerDetailData;
    designationCdWithDate: string;
  };
  incomeContest: {
    data: ContentBonusData;
  };
  incomePolQM: PolicyBonusQM;
  incomeMonths: PolicyBonusMonthDetailData;
  persistencyBonus: PersistencyDetailData;
  dev590Bonus: Dev590BonusDetailData;
  designationCdWithDate: string;
  indirectORY1: PolicyBonusSupportTeam;
  persistencyLeader: PolicyBonusLeaderPersisbon;
}

export interface lstPolicyBonusQuarter {
  agentId: string;
  fycWoExcess: number;
  fypAck: number;
  owName: string;
  policyNumber: string;
}
