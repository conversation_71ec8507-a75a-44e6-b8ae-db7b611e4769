import ScrollBar from "components/scroll-bar";
import SideBar from "components/sidebar";
import { useAppSelector } from "hooks/use-redux";
import { useRouter } from "next/router";
import React, { useMemo } from "react";
import { MainLayoutContent, MainLayoutWrapper } from "./styled";

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout = ({ children }: MainLayoutProps) => {
  const { username } = useAppSelector((state) => state.rootReducer.user);
  const { pathname } = useRouter();

  const linkNotHandle = !["/_error", "/login", "/"].includes(pathname);

  const background = useMemo(() => {
    if (pathname === "/trang-chu" || !username) {
      return "#ffffff";
    } else {
      return "#e5e5e5";
    }
  }, [pathname, username]);

  return (
    <MainLayoutWrapper background={background}>
      {username || !linkNotHandle ? (
        <>
          {linkNotHandle ? <SideBar /> : null}
          <ScrollBar>
            <MainLayoutContent>{children}</MainLayoutContent>
          </ScrollBar>
        </>
      ) : null}
    </MainLayoutWrapper>
  );
};

export default MainLayout;
