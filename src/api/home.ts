import {
  AgentIdInput,
  AgentIdInputWidthChannel,
  BirthdayReminderInput,
  GetHomePageOverviewInput,
  LinkVoiceRecordingInput,
  RecordVoiceRecordingInput,
  VoiceRecordCheckEnroll,
} from "@custom-types/home";
import request from "api";
import { CancelToken } from "axios";

export const getAchievementList = (body: AgentIdInputWidthChannel) => {
  return request.post("/cube-api/home/<USER>", body);
};

export const getPolicyInDueList = (body: AgentIdInputWidthChannel) => {
  return request.post("/cube-api/home/<USER>", body);
};

export const getPolicyOverDueList = (body: AgentIdInputWidthChannel) => {
  return request.post("/cube-api/home/<USER>", body);
};

export const getPolicyNewBusiness = (body: GetHomePageOverviewInput) => {
  return request.post("/cube-api/home/<USER>", body);
};

export const getPerformanceMTD = (body: GetHomePageOverviewInput) => {
  return request.post("/cube-api/home/<USER>", body);
};

export const getPerformanceYTD = (body: GetHomePageOverviewInput) => {
  return request.post("/cube-api/home/<USER>", body);
};

export const getNotificationList = (body: AgentIdInputWidthChannel) => {
  return request.post("/cube-api/home/<USER>", body);
};

export const readNotification = (body: AgentIdInput) => {
  return request.post("/cube-api/home/<USER>", body);
};

//Birthday
export const getBirthdayReminder = (body: BirthdayReminderInput, cancelToken: CancelToken) => {
  return request.post("/cube-api/home/<USER>", body, { cancelToken });
};

//Announcement
export const getAnnouncementHomePage = (body: AgentIdInputWidthChannel) => {
  return request.post("/cube-api/agent/announcement/getTopThumbnail", body);
};

export const getAnnouncementType = (body: AgentIdInputWidthChannel) => {
  return request.post("/cube-api/agent/announcement/type", body);
};

export const getAnnouncementList = (body: AgentIdInputWidthChannel) => {
  return request.post("/cube-api/agent/announcement/getAll", body);
};

export const getBannerHomePage = (body: AgentIdInputWidthChannel) => {
  return request.post("/cube-api/agent/announcement/getBanners", body);
};

//Voice recording
export const getLinkVoiceRecording = (body: LinkVoiceRecordingInput) => {
  return request.post("/smart2/api/avr/enrollments", body);
};

export const recordVoiceRecording = (body: RecordVoiceRecordingInput) => {
  return request.put(`/smart2/api/avr/enrollments/${body.agentCode}`, body.body);
};

/**
 * Kiểm tra trạng thái đăng ký giọng nói của nhân viên tư vấn
 */
export const checkEnrollStatus = (body: { agentCode: string }, cancelToken?: CancelToken) => {
  return request.get<VoiceRecordCheckEnroll>(`/smart2/api/avr/enrollments/${body.agentCode}`, { cancelToken });
};
