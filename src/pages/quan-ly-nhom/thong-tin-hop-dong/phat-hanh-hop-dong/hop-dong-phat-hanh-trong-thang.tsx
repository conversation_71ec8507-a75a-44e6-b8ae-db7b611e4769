import BackNavigate from "components/back-navigate";
import SidBarSub from "components/sidebar/sub";
import Tab from "components/tab";
import { useAppSelector } from "hooks/use-redux";
import { useMemo } from "react";
import ContractReleaseInMonth from "screens/policy-tracking/phat-hanh-hop-dong-hsycbh/hop-dong-phat-hanh-trong-thang";
import { policyTeamContractList, policyTeamContractReleaseList } from "services/menu-link";

const ContractReleasedInMonthPage = () => {
  const { channel, designation } = useAppSelector((state) => state.rootReducer.user);

  const sideBarSubList = useMemo(() => {
    return policyTeamContractReleaseList(channel, designation).filter((item) => item.show);
  }, [channel, designation]);

  return (
    <>
      <BackNavigate title="Thông tin hợp đồng" link="/quan-ly-nhom" />
      <Tab type="link" fakeBackgroundMobile list={policyTeamContractList} />
      <SidBarSub list={sideBarSubList}>
        <ContractReleaseInMonth />
      </SidBarSub>
    </>
  );
};

export default ContractReleasedInMonthPage;
