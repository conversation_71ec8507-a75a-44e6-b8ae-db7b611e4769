import useConditionRedirect from "hooks/use-condition-redirect";
import { useAppSelector } from "hooks/use-redux";
import Performance from "screens/performance";
import { checkIsAD } from "services/untils";

const PerformancePage = () => {
  const { username, channel } = useAppSelector((state) => state.rootReducer.user);

  useConditionRedirect({
    condition: channel === "AGENCY" && checkIsAD(username),
  });

  return <Performance />;
};

export default PerformancePage;
