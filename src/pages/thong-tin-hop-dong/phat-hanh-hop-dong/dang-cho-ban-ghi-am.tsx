import SidBarSub from "components/sidebar/sub";
import Tab from "components/tab";
import useConditionRedirect from "hooks/use-condition-redirect";
import { useAppSelector } from "hooks/use-redux";
import AudioRecordPending from "screens/policy-tracking/phat-hanh-hop-dong-hsycbh/dang-cho-ban-ghi-am";
import { policyContractList, policyContractReleaseList } from "services/menu-link";
import { checkIsAD } from "services/untils";

const AudioRecordPendingPage = () => {
  const { username, channel } = useAppSelector((state) => state.rootReducer.user);

  useConditionRedirect({
    condition: channel === "AGENCY" && checkIsAD(username),
  });

  return (
    <>
      <Tab type="link" fakeBackgroundMobile list={policyContractList} />
      <SidBarSub list={policyContractReleaseList}>
        <AudioRecordPending />
      </SidBarSub>
    </>
  );
};

export default AudioRecordPendingPage;
