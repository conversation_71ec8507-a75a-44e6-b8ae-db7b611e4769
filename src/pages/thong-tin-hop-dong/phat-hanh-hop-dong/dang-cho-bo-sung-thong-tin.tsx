import SidBarSub from "components/sidebar/sub";
import Tab from "components/tab";
import useConditionRedirect from "hooks/use-condition-redirect";
import { useAppSelector } from "hooks/use-redux";
import InformationPending from "screens/policy-tracking/phat-hanh-hop-dong-hsycbh/dang-cho-bo-sung-thong-tin";
import { policyContractList, policyContractReleaseList } from "services/menu-link";
import { checkIsAD } from "services/untils";

const WaitingInformationPage = () => {
  const { username, channel } = useAppSelector((state) => state.rootReducer.user);

  useConditionRedirect({
    condition: channel === "AGENCY" && checkIsAD(username),
  });

  return (
    <>
      <Tab type="link" fakeBackgroundMobile list={policyContractList} />
      <SidBarSub list={policyContractReleaseList}>
        <InformationPending />
      </SidBarSub>
    </>
  );
};

export default WaitingInformationPage;
