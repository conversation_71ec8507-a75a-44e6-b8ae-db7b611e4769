import SidBarSub from "components/sidebar/sub";
import Tab from "components/tab";
import useConditionRedirect from "hooks/use-condition-redirect";
import { useAppSelector } from "hooks/use-redux";
import FeePaid from "screens/policy-tracking/quan-ly-hop-dong/phi-bh-da-nop";
import { policyContractList, policyContractManagementList } from "services/menu-link";
import { checkIsAD } from "services/untils";

const InsurancePaidPage = () => {
  const { username, channel } = useAppSelector((state) => state.rootReducer.user);

  useConditionRedirect({
    condition: channel === "AGENCY" && checkIsAD(username),
  });

  return (
    <>
      <Tab type="link" fakeBackgroundMobile list={policyContractList} />
      <SidBarSub list={policyContractManagementList}>
        <FeePaid />
      </SidBarSub>
    </>
  );
};

export default InsurancePaidPage;
