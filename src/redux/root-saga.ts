import { all } from "redux-saga/effects";
import documentSaga from "screens/bieu-mau-va-ung-dung/bieu-mau/saga";
import leadsSaga from "screens/leads/saga";
import performanceSaga from "screens/performance/saga";
import teamSaga from "screens/team/performance/saga";
import contestSaga from "screens/thi-dua-va-khen-thuong/saga";

export default function* rootSaga() {
  yield all([teamSaga(), performanceSaga(), contestSaga(), documentSaga(), leadsSaga()]);
}
