import { configureStore } from "@reduxjs/toolkit";
import createSagaMiddleware from "redux-saga";

import journeyReducer from "../screens/danh-sach-chuyen-di/slice";
import ereReducer from "../screens/duyet-ung-vien/slice";
import homeReducer from "../screens/home/<USER>";
import k2Reducer from "../screens/k2-va-bao-cao/k2/slice";
import leadsReducer from "../screens/leads/slice";
import performanceReducer from "../screens/performance/slice";
import policyTrackingReducer from "../screens/policy-tracking/slice";
import teamPerformanceReducer from "../screens/team/performance/slice";
import contestReducer from "../screens/thi-dua-va-khen-thuong/slice";
import ecoachReducer from "../screens/ecoach/slice";
import rootReducer from "./root-reducer";
import rootSaga from "./root-saga";
import gaReducer from "../screens/bang-phi-dich-vu/slice";

// const loadState = () => {
//   if (typeof sessionStorage !== "undefined") {
//     const localState = sessionStorage.getItem(`vip-care`);
//     if (localState) {
//       return JSON.parse(localState);
//     }
//   }
//   return {};
// };

const sagaMiddleware = createSagaMiddleware();

export const store = configureStore({
  reducer: {
    rootReducer,
    homeReducer,
    performanceReducer,
    policyTrackingReducer,
    teamPerformanceReducer,
    k2Reducer,
    ereReducer,
    contestReducer,
    journeyReducer,
    leadsReducer,
    gaReducer,
    ecoachReducer,
  },
  middleware: (getDefaultMiddle) =>
    getDefaultMiddle({
      serializableCheck: false,
    }).concat(sagaMiddleware),
  // preloadedState: loadState(),
});

export type SagaTypes = "SAGA" | "ROOT";
export type SagaInjector = { key: SagaTypes; saga: any };

function createSagaInjector(runSaga: any, rootSaga: any) {
  // Create a dictionary to keep track of injected sagas
  const injectedSagas = new Map();

  const isInjected = (key: string) => injectedSagas.has(key);

  const injectSaga = (injector: SagaInjector) => {
    // We won't run saga if it is already injected
    if (isInjected(injector.key)) return;

    // Sagas return task when they executed, which can be used
    // to cancel them
    const task = runSaga(injector.saga);

    // Save the task if we want to cancel it in the future
    injectedSagas.set(injector.key, task);
  };

  // Inject the root saga as it a staticlly loaded file,
  injectSaga({ key: "ROOT", saga: rootSaga });

  return injectSaga;
}

// sagaMiddleware.run(rootSaga); // run after create store => important// Add injectSaga method to our store
export const injectSaga = createSagaInjector(sagaMiddleware.run, rootSaga);

//Store redux to localStorage/sessionStorage
// const saveState = (state: any) => {
//   try {
//     const serializedState = JSON.stringify(state);
//     sessionStorage.setItem("vip-care", serializedState);
//   } catch {}
// };

// store.subscribe(() => {
//   const state = store.getState();
//   saveState(state);
// });

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type AppDispatch = typeof store.dispatch;

export function createSagaAction<T>(type: string) {
  return (payload: T) => ({
    type: type,
    payload,
  });
}
