import { ChannelType } from "@custom-types/user";
import { IconType } from "components/icons";

export interface LinkProps {
  icon?: IconType;
  label?: string;
  href?: string;
  value?: any;
  type?: "link" | "button";
  children?: LinkProps[];
  show?: boolean;
}

export const sideBarList: LinkProps[] = [
  {
    icon: "menu-home",
    label: "Trang chủ",
    href: "/trang-chu",
    type: "link",
  },
  {
    icon: "icon-approval",
    label: "Duyệt ứng viên",
    href: "/duyet-ung-vien/danh-sach-duyet-ung-vien",
    type: "link",
  },
  {
    icon: "menu-policy",
    label: "TT Hợp Đồng",
    href: "/thong-tin-hop-dong",
    type: "link",
  },
  {
    href: "/ket-qua-kinh-doanh",
    label: "Kết quả KD",
    icon: "menu-performance",
    type: "link",
  },
  {
    href: "/bang-phi-dich-vu",
    label: "Bảng phí <br/>dịch vụ",
    icon: "ga-service",
    type: "link",
  },
  {
    href: "/k2-va-bao-cao",
    label: "K2 & Báo cáo",
    icon: "menu-k2",
    type: "link",
  },
  {
    href: "/quan-ly-nhom",
    label: "Quản lý Nhóm",
    icon: "menu-team",
    type: "link",
  },
  {
    href: "/thi-dua-va-khen-thuong",
    label: "Thi đua & <br/>Khen thưởng",
    icon: "menu-emulation-and-reward",
    type: "link",
  },
  {
    href: "/danh-sach-chuyen-di",
    label: "DS chuyến đi",
    icon: "menu-trip",
    type: "link",
  },
  {
    href: "/bieu-mau-va-ung-dung",
    label: "Biểu mẫu & <br/>Ứng dụng",
    icon: "search-document",
    type: "link",
  },
  // {
  //   href: "/hoc-vien",
  //   label: "Học viện",
  //   icon: "menu-academy",
  //   type: "link",
  // },
  {
    href: "",
    label: "Cài đặt",
    icon: "setting",
    type: "button",
    children: [
      { href: "", label: "Đổi mật khẩu", icon: "change-password", type: "button", value: "change-password" },
      { href: "", label: "Đăng xuất", icon: "logout", type: "button", value: "log-out" },
    ],
  },
];

export const sideBarDOList: LinkProps[] = [
  {
    icon: "menu-home",
    label: "Trang chủ",
    href: "/trang-chu",
    type: "link",
  },
  {
    icon: "menu-leads",
    label: "KH tiềm năng",
    href: "/leads",
    type: "link",
  },
  {
    href: "/quan-ly-nhom/phan-bo-khach-hang-tiem-nang",
    label: "QL Nhóm",
    icon: "menu-team",
    type: "link",
  },
  {
    href: "",
    label: "Cài đặt",
    icon: "setting",
    type: "button",
    children: [
      { href: "", label: "Đổi mật khẩu", icon: "change-password", type: "button", value: "change-password" },
      { href: "", label: "Đăng xuất", icon: "logout", type: "button", value: "log-out" },
    ],
  },
];

export const policyContractList: LinkProps[] = [
  {
    label: "Phát hành hợp đồng (HSYCBH)",
    href: "/thong-tin-hop-dong/phat-hanh-hop-dong",
  },
  {
    label: "Quản lý hợp đồng",
    href: "/thong-tin-hop-dong/quan-ly-hop-dong",
  },
  {
    label: "Giải quyết quyền lợi bảo hiểm",
    href: "/thong-tin-hop-dong/giai-quyet-quyen-loi-bao-hiem",
  },
  {
    label: "Hợp đồng thuộc chương trình",
    href: "/thong-tin-hop-dong/hop-dong-thuoc-chuong-trinh",
  },
];

export const policyContractManagementList: LinkProps[] = [
  {
    icon: "menu-policy-contract-management-1",
    label: "HĐ Đến hạn thu phí",
    href: "/thong-tin-hop-dong/quan-ly-hop-dong/hop-dong-den-han-thu-phi",
  },
  {
    icon: "menu-policy-contract-management-2",
    label: "HĐ Mất hiệu lực",
    href: "/thong-tin-hop-dong/quan-ly-hop-dong/hop-dong-mat-hieu-luc",
  },
  {
    icon: "menu-policy-contract-management-3",
    label: "Thông tin HĐ",
    href: "/thong-tin-hop-dong/quan-ly-hop-dong/thong-tin-hop-dong",
  },
  {
    icon: "menu-policy-contract-management-4",
    label: "Phí BH đã nộp",
    href: "/thong-tin-hop-dong/quan-ly-hop-dong/phi-bao-hiem-da-nop",
  },
  {
    icon: "menu-policy-contract-management-5",
    label: "Các thay đổi của HĐ",
    href: "/thong-tin-hop-dong/quan-ly-hop-dong/cac-thay-doi-cua-hop-dong",
  },
  {
    icon: "menu-policy-contract-management-6",
    label: "Thông tin giao nhận bộ HĐBH",
    href: "/thong-tin-hop-dong/quan-ly-hop-dong/thong-tin-giao-nhan-bo-hdbh",
  },
];

export const policyContractReleaseList: LinkProps[] = [
  {
    icon: "icon-policy-confirm",
    label: "Khách hàng xác nhận TT",
    href: "/thong-tin-hop-dong/phat-hanh-hop-dong/khach-hang-xac-nhan-thong-tin",
  },
  {
    icon: "menu-policy-contract-release-1",
    label: "Đang thẩm định",
    href: "/thong-tin-hop-dong/phat-hanh-hop-dong/dang-tham-dinh",
  },
  {
    icon: "menu-policy-contract-release-2",
    label: "Đang chờ bổ sung TT",
    href: "/thong-tin-hop-dong/phat-hanh-hop-dong/dang-cho-bo-sung-thong-tin",
  },
  {
    icon: "menu-policy-contract-release-5",
    label: "Đang chờ bản ghi âm",
    href: "/thong-tin-hop-dong/phat-hanh-hop-dong/dang-cho-ban-ghi-am",
  },
  {
    icon: "menu-policy-contract-release-3",
    label: "HĐ phát hành trong tháng",
    href: "/thong-tin-hop-dong/phat-hanh-hop-dong/hop-dong-phat-hanh-trong-thang",
  },
  {
    icon: "menu-policy-contract-release-4",
    label: "Không được phát hành",
    href: "/thong-tin-hop-dong/phat-hanh-hop-dong/khong-duoc-phat-hanh",
  },
  {
    icon: "menu-clinic-list",
    label: "DS phòng khám",
    href: "/thong-tin-hop-dong/phat-hanh-hop-dong/danh-sach-phong-kham",
  },
];

export const policyContractSolve: LinkProps[] = [
  {
    icon: "menu-policy-contract-solve-1",
    label: "Thông tin HS GQ QLBH",
    href: "/thong-tin-hop-dong/giai-quyet-quyen-loi-bao-hiem/thong-tin-ho-so-gq-qlbh",
  },
  {
    icon: "menu-policy-contract-solve-2",
    label: "Hướng dẫn nộp YCGQ QLBH",
    href: "/thong-tin-hop-dong/giai-quyet-quyen-loi-bao-hiem/huong-dan-nop-ycgq-qlbh",
  },
  {
    icon: "menu-policy-contract-solve-4",
    label: "Liên hệ",
    href: "/thong-tin-hop-dong/giai-quyet-quyen-loi-bao-hiem/lien-he",
  },
];

//Team
export const menuTeamList: LinkProps[] = [
  {
    icon: "data",
    label: "Thông tin hợp đồng",
    href: "/quan-ly-nhom/thong-tin-hop-dong",
    type: "link",
  },
  {
    icon: "icon-document-team",
    label: "Đội ngũ kinh doanh",
    type: "button",
    value: "business-team",
  },
  {
    icon: "target",
    label: "Quản lý mục tiêu nhóm",
    type: "button",
    value: "team-target",
  },
  // {
  //   icon: "menu-leads",
  //   label: "Quản lý Khách hàng tiềm năng",
  //   href: "/leads/quan-ly-khach-hang-tiem-nang",
  //   type: "link",
  // },
  // {
  //   icon: "menu-team",
  //   label: "Phân bổ Khách hàng tiềm năng",
  //   href: "/leads/phan-bo-khach-hang-tiem-nang",
  //   type: "link",
  // },
  // {
  //   icon: "target",
  //   label: "Duyệt bảng minh họa",
  //   href: "/leads/duyet-bang-minh-hoa",
  //   type: "link",
  // },
];

export const policyTeamContractList: LinkProps[] = [
  {
    label: "Phát hành hợp đồng (HSYCBH)",
    href: "/quan-ly-nhom/thong-tin-hop-dong/phat-hanh-hop-dong",
  },
  {
    label: "Quản lý hợp đồng",
    href: "/quan-ly-nhom/thong-tin-hop-dong/quan-ly-hop-dong",
  },
  {
    label: "Giải quyết quyền lợi bảo hiểm",
    href: "/quan-ly-nhom/thong-tin-hop-dong/giai-quyet-quyen-loi-bao-hiem",
  },
  {
    label: "Hợp đồng thuộc chương trình",
    href: "/quan-ly-nhom/thong-tin-hop-dong/hop-dong-thuoc-chuong-trinh",
  },
];

export const policyTeamContractManagementList: LinkProps[] = [
  {
    icon: "menu-policy-contract-management-1",
    label: "HĐ Đến hạn thu phí",
    href: "/quan-ly-nhom/thong-tin-hop-dong/quan-ly-hop-dong/hop-dong-den-han-thu-phi",
  },
  {
    icon: "menu-policy-contract-management-2",
    label: "HĐ Mất hiệu lực",
    href: "/quan-ly-nhom/thong-tin-hop-dong/quan-ly-hop-dong/hop-dong-mat-hieu-luc",
  },
  {
    icon: "menu-policy-contract-management-3",
    label: "Thông tin HĐ",
    href: "/quan-ly-nhom/thong-tin-hop-dong/quan-ly-hop-dong/thong-tin-hop-dong",
  },
  {
    icon: "menu-policy-contract-management-4",
    label: "Phí BH đã nộp",
    href: "/quan-ly-nhom/thong-tin-hop-dong/quan-ly-hop-dong/phi-bao-hiem-da-nop",
  },
  {
    icon: "menu-policy-contract-management-5",
    label: "Các thay đổi của HĐ",
    href: "/quan-ly-nhom/thong-tin-hop-dong/quan-ly-hop-dong/cac-thay-doi-cua-hop-dong",
  },
  {
    icon: "menu-policy-contract-management-6",
    label: "Thông tin giao nhận bộ HĐBH",
    href: "/quan-ly-nhom/thong-tin-hop-dong/quan-ly-hop-dong/thong-tin-giao-nhan-bo-hdbh",
  },
];

export const policyTeamContractReleaseList = (channel: ChannelType, designation: string): LinkProps[] => [
  {
    icon: "icon-policy-confirm",
    label: "Khách hàng xác nhận TT",
    href: "/quan-ly-nhom/thong-tin-hop-dong/phat-hanh-hop-dong/khach-hang-xac-nhan-thong-tin",
    show: designation === "BSM" || channel === "BROKER",
  },
  {
    icon: "menu-policy-contract-release-1",
    label: "Đang thẩm định",
    href: "/quan-ly-nhom/thong-tin-hop-dong/phat-hanh-hop-dong/dang-tham-dinh",
    show: true,
  },
  {
    icon: "menu-policy-contract-release-2",
    label: "Đang chờ bổ sung TT",
    href: "/quan-ly-nhom/thong-tin-hop-dong/phat-hanh-hop-dong/dang-cho-bo-sung-thong-tin",
    show: true,
  },
  {
    icon: "menu-policy-contract-release-3",
    label: "HĐ phát hành trong tháng",
    href: "/quan-ly-nhom/thong-tin-hop-dong/phat-hanh-hop-dong/hop-dong-phat-hanh-trong-thang",
    show: true,
  },
  {
    icon: "menu-policy-contract-release-4",
    label: "Không được phát hành",
    href: "/quan-ly-nhom/thong-tin-hop-dong/phat-hanh-hop-dong/khong-duoc-phat-hanh",
    show: true,
  },
  {
    icon: "menu-clinic-list",
    label: "DS phòng khám",
    href: "/quan-ly-nhom/thong-tin-hop-dong/phat-hanh-hop-dong/danh-sach-phong-kham",
    show: true,
  },
];

export const policyTeamContractSolve: LinkProps[] = [
  {
    icon: "menu-policy-contract-solve-1",
    label: "Thông tin HS GQ QLBH",
    href: "/quan-ly-nhom/thong-tin-hop-dong/giai-quyet-quyen-loi-bao-hiem/thong-tin-ho-so-gq-qlbh",
  },
  {
    icon: "menu-policy-contract-solve-2",
    label: "Hướng dẫn nộp YCGQ QLBH",
    href: "/quan-ly-nhom/thong-tin-hop-dong/giai-quyet-quyen-loi-bao-hiem/huong-dan-nop-ycgq-qlbh",
  },
  {
    icon: "menu-policy-contract-solve-4",
    label: "Liên hệ",
    href: "/quan-ly-nhom/thong-tin-hop-dong/giai-quyet-quyen-loi-bao-hiem/lien-he",
  },
];

//BIEU MAU VA BAO CAO
export const formAndReportMenu: LinkProps[] = [
  {
    label: "Biểu mẫu",
    href: "/bieu-mau-va-ung-dung/bieu-mau",
  },
  {
    label: "Ứng dụng",
    href: "/bieu-mau-va-ung-dung/ung-dung",
  },
  {
    label: "Link iFWD",
    href: "/bieu-mau-va-ung-dung/link-ifwd",
  },
];

//Thi dua va khen thuong
export const emulationAndRewardSubMenuList = (channel: ChannelType): LinkProps[] => {
  return [
    {
      icon: "menu-emulation-and-reward-1",
      label: "Thông tin thi đua",
      href: "/thi-dua-va-khen-thuong/thong-tin-thi-dua",
      show: true,
    },
    {
      icon: "menu-emulation-and-reward-2",
      label: "Thông tin báo cáo",
      href: "/thi-dua-va-khen-thuong/thong-tin-bao-cao",
      show: channel === "BANCA",
    },
    {
      icon: "menu-emulation-and-reward-3",
      label: "Chính sách thù lao",
      href: "/thi-dua-va-khen-thuong/chinh-sach-thu-lao",
      show: channel === "BROKER",
    },
    {
      icon: "icon-cup-contest",
      label: "Thông tin tham chiếu",
      href: "/thi-dua-va-khen-thuong/thong-tin-tham-chieu",
      show: channel === "AGENCY",
    },
  ];
};

//Danh sach chuyen di
export const tripTabList: LinkProps[] = [
  { label: "Danh sách chuyến đi", href: "/danh-sach-chuyen-di/danh-sach-chuyen-di" },
  { label: "Lịch sử xác nhận chuyến đi", href: "/danh-sach-chuyen-di/lich-su-xac-nhan-chuyen-di" },
];

//K2 va Bao cao
export const k2AndReportSubSideBar = (channel: ChannelType): LinkProps[] => {
  return [
    { label: "Tỷ lệ duy trì K2", href: "/k2-va-bao-cao/ty-le-duy-tri-k2", icon: "menu-k2", show: true },
    { label: "Doanh số", href: "/k2-va-bao-cao/doanh-so", icon: "menu-sale-report", show: channel === "AGENCY" },
    {
      label: "Chi tiết giao dịch phí HĐBH",
      href: "/k2-va-bao-cao/transaction-report",
      icon: "menu-transaction-report",
      show: channel === "BROKER",
    },
    {
      label: "Hợp đồng hủy trong tháng",
      href: "/k2-va-bao-cao/policy-cancellation-report",
      icon: "menu-policy-cancellation-report",
      show: channel === "BROKER",
    },
    {
      label: "Hợp đồng nộp",
      href: "/k2-va-bao-cao/submission-report",
      icon: "submission-report",
      show: channel === "BROKER",
    },
    {
      label: "Kết quả KD tổng",
      href: "/k2-va-bao-cao/summary-sales-report",
      icon: "summary-sales-report",
      show: channel === "BROKER",
    },
    {
      label: "Thông tin ACK hợp đồng",
      href: "/k2-va-bao-cao/thong-tin-ack-hop-dong",
      icon: "icon-menu-ack-k2",
      show: channel === "BROKER",
    },
  ];
};
