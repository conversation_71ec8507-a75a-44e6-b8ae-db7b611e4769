import { ERROR, ERROR_API_MESSAGE } from "@constants/message";
import { ValueLabel } from "@custom-types";
import { TotalGaOutputData } from "@custom-types/service-fee";
import { getTotalGa } from "api/service-fee";
import { Alert } from "components/alert";
import DropDownFieldset from "components/drop-down-fieldset";
import FormSearchTemplate from "components/form-search-template";
import Icons from "components/icons";
import MonthInput from "components/input-month";
import LoadingSection from "components/loading";
import Tab from "components/tab";
import TabButton from "components/tab-button";
import { ListTabsProps } from "components/tab-for-mobile";
import TableCollapse, { HeadTableCollapse, TableCollapseData } from "components/table-collapse";
import TableCollapseMobile from "components/table-collapse-mobile";
import useActionApi from "hooks/use-action-api";
import { useAppSelector } from "hooks/use-redux";
import { cloneDeep, isNil, isNull } from "lodash";
import moment from "moment";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import { setIncomeGAForm } from "screens/bang-phi-dich-vu/slice";
import Information, {
  InformationData,
} from "sections/performance/components/income-info/income-soa/components/total-income/information";
import {
  InformationWrapper,
  Label,
  TitleInfo,
} from "sections/performance/components/income-info/income-soa/components/total-income/styled";
import { TabMobileBox, TabWrapper } from "sections/performance/components/income-info/income-soa/styled";
import { FlexBox, NoData, RowItem } from "styles";
import { ButtonIcon } from "styles/buttons";
import { ServiceFeeWrapper } from "./styled";
import { exportGAExcel } from "./excel";

export interface ServiceFeeForm {
  month: string;
  period: ValueLabel;
}

const headTable: HeadTableCollapse[][] = [
  [
    { label: "Chi tiết", colSpan: 2 },
    { label: "Số tiền", colSpan: 2 },
  ],
];

const tabList: ListTabsProps[] = [{ value: "total-fee", label: "Tổng phí" }];

export const getTotalGaFee = (data: any) => {
  if (isNil(data)) {
    return 0;
  }

  const sum = Object.keys(data).reduce((acc, key) => {
    if (
      [
        "gaMallow",
        "gaMonth",
        "gaPersist",
        "gaQuarter",
        "gaYear",
        "gaLti",
        "gaTotalContest",
        "gaSignon",
        "gaAdj",
      ].includes(key)
    ) {
      return acc + data[key];
    }
  }, 0);
  return sum;
};

const ServiceFeeSection = () => {
  const [data, setData] = useState<TotalGaOutputData>(null);
  const [submit, setSubmit] = useState(false);
  const [error, setError] = useState<{
    month: string;
  }>({
    month: null,
  });

  const {
    rootReducer: {
      user: { username },
      loading: { getGaTotalLoading },
    },
    gaReducer: { incomeGAForm },
  } = useAppSelector((state) => state);
  const { month, period } = incomeGAForm;

  const dispatch = useDispatch();

  const actionGetTotalGa = useActionApi(getTotalGa);

  const dataTable: TableCollapseData[] = useMemo(() => {
    return [
      {
        label: "1. Biểu phí dịch vụ",
        value: getTotalGaFee(data),
        type: "number",
        colSpan: 2,
        children: [
          {
            rowKey: "",
            label: "Phí hỗ trợ phát triển kinh doanh cơ bản",
            value: data?.gaMallow,
            type: "number",
            colSpan: 2,
          },
          {
            rowKey: "",
            label: "Phí Tháng",
            value: data?.gaMonth,
            type: "number",
            colSpan: 2,
          },
          {
            rowKey: "",
            label: "Phí hỗ trợ kinh doanh chất lượng",
            value: data?.gaPersist,
            type: "number",
            colSpan: 2,
          },
          {
            rowKey: "",
            label: "Phí Quý",
            value: data?.gaQuarter,
            type: "number",
            colSpan: 2,
          },
          {
            rowKey: "",
            label: "Phí Năm",
            value: data?.gaYear,
            type: "number",
            colSpan: 2,
          },
          {
            rowKey: "",
            label: "Phí cung cấp dịch vụ lâu dài và chất lượng",
            value: data?.gaLti,
            type: "number",
            colSpan: 2,
          },
          {
            rowKey: "",
            label: "Phí từ các chương trình thi đua",
            value: data?.gaTotalContest,
            type: "number",
            colSpan: 2,
          },
          {
            rowKey: "",
            label: "Các khoản phí khác",
            value: data?.gaAdj,
            type: "number",
            colSpan: 2,
          },
        ],
      },
      {
        label: "2. Tổng phí dịch vụ",
        value: data?.gaTotalIncome,
        type: "number",
        colSpan: 2,
      },
      {
        label: "3. Các khoản cấn trừ",
        value: data?.gaTotalDeduct,
        type: "number",
        colSpan: 2,
      },
      {
        label: "4. Số tiền thực nhận",
        value: data?.gaTotalEarning,
        type: "number",
        colSpan: 2,
      },
    ];
  }, [data]);

  const dateList: ValueLabel[] = useMemo(() => {
    const fromDate = moment(month, "MM/YYYY").startOf("month").add(14, "days");
    const toDate = moment(month, "MM/YYYY").endOf("month");

    return [
      { label: `Kỳ 1: ${fromDate.format("DD-MM-YYYY")}`, value: fromDate.format("YYYY-MM-DD") },
      { label: `Kỳ 2: ${toDate.format("DD-MM-YYYY")}`, value: toDate.format("YYYY-MM-DD") },
    ];
  }, [month]);

  const informationGA: InformationData[][] = useMemo(() => {
    return [
      [
        { label: "Đơn vị cung cấp", value: data?.hhBusinessName, type: "string" },
        { label: "Giấy chứng nhận số", value: data?.hhCertificateNum, type: "string" },
      ],
      [
        { label: "Mã số thuế", value: data?.hhTaxNum, type: "string" },
        { label: "Địa chỉ trụ sở", value: data?.hhAddress, type: "string" },
      ],
    ];
  }, [data]);

  const handleChangeData = useCallback(
    (key: string, value: any) => {
      const formClone = cloneDeep(incomeGAForm);

      if (key === "period") {
        formClone.period = value;
      }

      if (key === "month") {
        formClone.month = value;
        formClone.period = { value: "", label: "" };
        handleCheckError(value);
      }
      dispatch(setIncomeGAForm(formClone));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [incomeGAForm]
  );

  const handleCheckError = (value: string) => {
    if (value.length < 7) {
      setError({ month: "Vui lòng nhập đúng định dạng: mm/yyyy" });
    } else {
      if (moment(value, "MM/YYYY").isAfter(moment(), "month")) {
        setError({
          month: "Dữ liệu được cập nhật đến thời điểm hiện tại. Vui lòng không chọn tháng/năm trong tương lai.",
        });
      } else {
        setError({ month: "" });
      }
    }
  };

  const handleSubmit = () => {
    setSubmit(true);
    actionGetTotalGa({
      body: {
        agentId: username,
        paymentDt: period.value,
      },
      loading: {
        name: "getGaTotalLoading",
        type: "local",
      },
    })
      .then(({ data }) => {
        setData(data?.data);
      })
      .catch((err) => {
        console.log(err);
        Alert(ERROR, ERROR_API_MESSAGE);
      });
  };

  const handleChangeTab = useCallback((tab: any) => {}, []);

  useEffect(() => {
    handleCheckError(month);
  }, [month]);

  return (
    <ServiceFeeWrapper>
      <FormSearchTemplate
        title="Thông tin"
        onSubmit={handleSubmit}
        disableSubmit={!period?.value || Boolean(error.month)}
        loading={Boolean(getGaTotalLoading)}
      >
        <RowItem>
          <MonthInput
            placeholder="Tháng"
            value={month}
            error={error.month}
            onChange={(value) => handleChangeData("month", value)}
          />
          <DropDownFieldset
            placeholder="Kỳ"
            value={period}
            list={dateList}
            onChange={(value) => handleChangeData("period", value)}
          />
        </RowItem>
      </FormSearchTemplate>
      {Boolean(getGaTotalLoading) ? (
        <div className="mt-16">
          <LoadingSection loading={true} />
        </div>
      ) : isNull(data) ? null : (
        <>
          <FlexBox justifyContent="space-between" alignItem="center" className="mt-20 hide-mobile">
            <TabWrapper className="mt-0 mb-0">
              <TabButton
                size="small"
                list={tabList}
                active={tabList[0].value}
                onChange={(tab: any) => handleChangeTab(tab)}
              />
            </TabWrapper>
            <ButtonIcon onClick={() => exportGAExcel({ data, agentCode: username })}>
              <Icons icon="download-icon-border" />
            </ButtonIcon>
          </FlexBox>
          <TabMobileBox className="hide-desktop">
            <Tab
              noPaddingTitle={true}
              type="button"
              list={tabList}
              currentTab={tabList[0].value}
              onChange={(tab) => handleChangeTab(tab.value)}
            />
            <FlexBox justifyContent="center" alignItem="center">
              <ButtonIcon onClick={() => exportGAExcel({ data, agentCode: username })}>
                <Icons icon="download-icon" />
                <h6 className="h8">Tải bảng kê phí dịch vụ</h6>
              </ButtonIcon>
            </FlexBox>
          </TabMobileBox>
          <InformationWrapper className="mt-20">
            <Label className="label-5">Công ty TNHH Bảo hiểm nhân thọ FWD Việt Nam</Label>
            <TitleInfo className="h7 mt-8">BẢNG KÊ PHÍ DỊCH VỤ</TitleInfo>
            <Information data={informationGA} />
          </InformationWrapper>
          <TableCollapse head={headTable} body={dataTable} loading={false} onViewDetail={() => null} />
          <TableCollapseMobile head={headTable} body={dataTable} />
        </>
      )}
      {isNil(data) && !Boolean(getGaTotalLoading) && submit ? <NoData>Không có dữ liệu</NoData> : null}
    </ServiceFeeWrapper>
  );
};

export default ServiceFeeSection;
