import styled from "styled-components";

export const SettingViewWrapper = styled.div``;

export const ListItemWrapper = styled.ul`
  padding: 0px 16px;
  margin-bottom: 50px;
`;

export const ButtonWrapper = styled.div`
  padding: 0px 16px;

  display: flex;
  justify-content: center;

  button {
    width: 100%;
  }
`;

export const ItemViewWrapper = styled.li`
  display: flex;
  align-items: center;

  :not(:last-child) {
    margin-bottom: 16px;
  }
`;

export const Card = styled.div`
  width: 100%;
  padding: 16px;
  margin-left: 16px;

  display: flex;

  border-radius: 12px;
  border: 1px solid ${({ theme }) => theme.color.status.grey};

  svg {
    margin-right: 8px;
  }
`;
