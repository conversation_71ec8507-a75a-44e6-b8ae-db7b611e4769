import styled, { css } from "styled-components";

export const NotificationSectionWrapper = styled.div<{ isNewNotify: boolean }>`
  ${({ isNewNotify }) =>
    isNewNotify
      ? css`
          .icon-notify {
            position: relative;
            &::after {
              width: 11px;
              height: 11px;
              background-color: #e72929;
              position: absolute;
              content: "";
              top: 0;
              right: 1px;
              border-radius: 50%;
            }
          }
        `
      : null}
`;

export const NotificationWrapper = styled.div`
  display: flex;
  flex-direction: column;

  label {
    padding: 8px 16px;
  }
`;

const UnreadStyled = css`
  background: ${({ theme }) => theme.color.status.primary_5};

  h6 {
    padding-left: 16px;
    position: relative;

    font-weight: 700;
    color: ${({ theme }) => theme.color.status.grey_darkest};

    :before {
      content: "";
      width: 8px;
      height: 8px;

      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);

      border-radius: 50%;
      background: ${({ theme }) => theme.color.status.red};
    }
  }
`;

export const NotificationItem = styled.div<{ isUnRead: boolean }>`
  padding: 16px;
  padding-left: 49px;
  cursor: pointer;

  position: relative;

  border-bottom: 1px solid ${({ theme }) => theme.color.status.grey};

  svg {
    position: absolute;
    top: 16px;
    left: 16px;
  }

  p {
    margin: 8px 0px;
  }

  ${({ isUnRead }) => (isUnRead ? UnreadStyled : null)}
`;
