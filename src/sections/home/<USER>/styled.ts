import styled from "styled-components";
import { device } from "styles/media";

export const ProfileDetailModalWrapper = styled.div<{ background?: string }>`
  width: 100%;
  min-height: calc(100 * var(--vh) - 55px);
  padding: 16px;

  background: ${({ background }) => background ?? "#ffffff"};
`;

export const Wrapper = styled.div`
  display: flex;
  gap: 24px;

  z-index: 1;
  position: relative;
`;

export const OverLay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;

  width: 100%;
  min-height: calc(100 * var(--vh));

  background-color: #fef9f4;
`;

export const ContentLeftOverView = styled.div`
  width: 344px;
  padding: 32px 16px;
  min-height: 706px;
  max-height: 706px;

  border-radius: 16px;
  background-color: ${({ theme }) => theme.color.status.primary_5};

  flex-shrink: 0;

  position: sticky !important;
  top: calc(55px + 16px);

  @media ${device.mobile} {
    width: 100%;
    min-height: max-content;
    padding: 32px 0px;

    overflow-x: hidden;
  }
`;

export const StickyWrap = styled.div`
  position: sticky;
  top: 16px;
`;

export const ContentRight = styled.div`
  flex: 1;
`;

export const ProfileWrap = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`;

export const AvatarWrap = styled.div`
  width: 120px;
  height: 120px;
  position: relative;

  img {
    border-radius: 50%;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

export const CameraWrap = styled.div`
  position: absolute;
  bottom: -20px;
  right: -5px;

  cursor: pointer;
`;

export const TabWrap = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

export const TabItem = styled.div<{ active: boolean }>`
  display: flex;
  gap: 12px;
  justify-content: space-between;
  align-items: center;

  padding: 10px 12px;
  cursor: pointer;
  transition: all 0.3s ease;

  :hover {
    background-color: #fae4d3;
    border-radius: 12px;
  }

  ${({ active }) =>
    active &&
    `background-color: #fae4d3;
    border-radius: 12px;`}
`;

export const ToolTipCamera = styled.div`
  max-width: 319px;

  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.25);

  p {
    font-size: 14px;
  }

  @media ${device.mobile} {
    max-width: 217px;
  }
`;

export const UploadWrap = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  width: 100%;
  height: 100%;

  background-color: rgba(255, 255, 255, 0.4);
  border-radius: inherit;

  display: flex;
  align-items: center;
  justify-content: center;
`;

export const WarningWrapper = styled.div`
  position: absolute;
  top: 0px;
  right: -24px;
`;
