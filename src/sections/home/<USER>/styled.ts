import styled from "styled-components";
import { device } from "styles/media";

export const TaskListWrapper = styled.div`
  p {
    color: ${({ theme }) => theme.color.status.grey_darkest};
  }
`;

export const BirthdayCard = styled.div`
  width: 100%;
  padding: 16px 12px 16px 64px;

  display: flex;
  flex-direction: column;
  justify-content: center;

  position: relative;

  background-color: #ffffff;
  border-radius: 16px;

  > svg {
    position: absolute;
    top: 50%;
    left: 12px;
    transform: translateY(-50%);
  }

  h6 {
    margin-bottom: 4px;
  }

  :not(:last-child) {
    margin-bottom: 12px;
  }
`;

export const BirthdayCardTitle = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;

  @media ${device.mobile} {
    align-items: flex-start;
  }
`;

export const Calendar = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;

  label {
    margin-left: 4px;
    color: ${({ theme }) => theme.color.status.grey_darker};
  }
`;
