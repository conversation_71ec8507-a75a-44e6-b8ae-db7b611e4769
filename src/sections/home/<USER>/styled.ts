import { BLockContent } from "components/block/styled";
import styled from "styled-components";
import { HideByOpacityStyle } from "styles";
import { device } from "styles/media";

export const CampaignWrapper = styled.div`
  .${BLockContent.styledComponentId} {
    overflow: visible;
  }

  @media ${device.mobile} {
    padding: 0px 16px;
    margin-bottom: 24px;

    .${BLockContent.styledComponentId} {
      margin-top: 0px;
    }
  }
`;

export const CardContractList = styled.div``;

//Item
export const CardContractItem = styled.div<{ color?: any; hide?: boolean }>`
  width: calc((100% / 3) - 12px);
  min-width: calc((100% / 3) - 12px);
  max-width: calc((100% / 3) - 12px);

  margin: 6px;

  display: flex;
  flex-direction: column;
  position: relative;

  border-radius: 12px;
  padding: 16px;
  border: 1px solid ${({ color }) => color?.primary};
  background: ${({ color }) => color?.secondary};

  cursor: pointer;

  ${({ hide }) => (hide ? HideByOpacityStyle : null)}

  p {
    margin: 8px 0 4px 0;
  }

  h3 {
    color: ${({ theme }) => theme.color.status.primary};
  }

  @media ${device.mobile} {
    padding: 12px 8px;

    p {
      font-size: 14px;
    }

    h3 {
      font-size: 25px;
    }
  }
`;

export const Header = styled.div`
  display: flex;
`;

export const Warning = styled.div`
  padding: 2px 8px;
  margin-left: 4px;

  font-weight: 400;
  font-size: 12px;
  line-height: 150%;

  color: #ffffff;
  background: ${({ theme }) => theme.color.status.red};
  border-radius: 4px;

  @media ${device.mobile} {
    padding: 2px 4px;
    font-size: 10px;
  }
`;

export const Note = styled.div`
  width: 100%;
  min-height: 86px;
  padding: 6px 14px;
  padding-left: 97px;
  margin-top: 25px;

  display: flex;
  align-items: center;
  justify-content: space-between;

  position: relative;
  z-index: 1;

  background: ${({ theme }) => theme.color.status.primary_5};
  border-radius: 16px;

  :before {
    content: "";
    width: 40px;
    height: 40px;

    position: absolute;
    z-index: -1;
    top: -10px;
    left: calc(((100% / 3) - 16px) / 2.5);

    background: ${({ theme }) => theme.color.status.primary_5};
    transform: rotate(45deg);
  }

  svg {
    position: absolute;
    top: 50%;
    left: 14px;
    transform: translateY(-50%);
  }

  @media ${device.mobile} {
    padding-left: 86px;

    svg {
      width: 63px;
      height: 63px;
    }

    p {
      font-size: 12px;
    }
  }
`;

export const FYPWrapper = styled.div`
  padding-left: 42px;
  padding-right: 10px;

  display: flex;
  flex-direction: column;

  p {
    padding: 0px !important;
  }

  h5 {
    color: ${({ theme }) => theme.color.status.primary};
  }

  @media ${device.mobile} {
    p {
      font-size: 12px;
    }

    h5 {
      font-size: 12px;
      font-weight: 700;
    }
  }
`;
