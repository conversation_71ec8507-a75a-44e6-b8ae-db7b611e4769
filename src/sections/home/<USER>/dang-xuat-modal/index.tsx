import Modal from "components/modal";
import { LogoutContent } from "components/sidebar/components/desktop/styled";
import { useRouter } from "next/router";
import React from "react";
import { ButtonWrapper } from "screens/login/styled";
import { ButtonPrimary, ButtonSecondary } from "styles/buttons";

interface LogoutModalProps {
  show: boolean;
  onCloseModal: () => void;
}
const LogoutModal = ({ show, onCloseModal }: LogoutModalProps) => {
  const router = useRouter();

  const handleLogout = () => {
    //clear cookies
    router.push("/login");
  };
  return (
    <Modal borderHeader backgroundMobile="#ffffff" title="Đăng xuất" size="sm" show={show} onClose={onCloseModal}>
      <LogoutContent>
        <p className="body-4">TVTC có chắc đăng xuất hệ thống?</p>
      </LogoutContent>
      <ButtonWrapper>
        <ButtonSecondary size="small" onClick={onCloseModal}>
          Hủy bỏ
        </ButtonSecondary>
        <ButtonPrimary size="small" onClick={handleLogout}>
          Đăng xuất
        </ButtonPrimary>
      </ButtonWrapper>
    </Modal>
  );
};

export default LogoutModal;
