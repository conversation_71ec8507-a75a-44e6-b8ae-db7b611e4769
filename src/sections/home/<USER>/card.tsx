import { AnnouncementData } from "@custom-types/home";
import Icons from "components/icons";
import { LoadingWrapper, UploadLoading } from "components/loading/styled";
import { useAppSelector } from "hooks/use-redux";
import { useCallback } from "react";
import { useDispatch } from "react-redux";
import { downloadDocumentAction } from "screens/bieu-mau-va-ung-dung/bieu-mau/action";
import { ButtonPrimary } from "styles/buttons";
import { AnnouncementCardWrapper, Thumbnail, Title, TitleWrapper } from "./styled";

interface CardProps {
  data: AnnouncementData;
}

const AnnouncementCard = ({ data }: CardProps) => {
  const {
    loading: { downloadDocumentLoading },
  } = useAppSelector((state) => state.rootReducer);

  const dispatch = useDispatch();

  const handleClick = useCallback(() => {
    dispatch(
      downloadDocumentAction({
        fileId: data?.fileId,
        link: data?.link,
      })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  return (
    <AnnouncementCardWrapper>
      <Thumbnail url={`data:image/jpg;base64,${data?.thumbnail}`}>
        {Boolean(downloadDocumentLoading) ? (
          <LoadingWrapper isFullContent>
            <UploadLoading />
          </LoadingWrapper>
        ) : null}
        <TitleWrapper>
          <Title>{data?.name}</Title>
          <ButtonPrimary size="tiny" onClick={handleClick}>
            <Icons icon="arrow-right-in-header" width={24} height={24} fill="#ffffff" />
          </ButtonPrimary>
        </TitleWrapper>
      </Thumbnail>
    </AnnouncementCardWrapper>
  );
};

export default AnnouncementCard;
