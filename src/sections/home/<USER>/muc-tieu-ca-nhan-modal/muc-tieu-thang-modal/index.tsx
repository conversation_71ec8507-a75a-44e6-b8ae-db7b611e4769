import { ERROR_API_MESSAGE } from "@constants/message";
import { Alert } from "components/alert";
import Icons from "components/icons";
import InputNumber from "components/input-number";
import LoadingSection from "components/loading";
import BasicTooltipTrigger from "components/tooltip/basic-tooltip-trigger";
import useActionApi from "hooks/use-action-api";
import { useAppSelector } from "hooks/use-redux";
import { useEffect, useState } from "react";
import BlockTeamTarget from "sections/team/agent-detail-modal/components/block-team-target";
import { ProfileTargetMonthlyData, SetProfileTargetMonthlyInput } from "@custom-types/profile-detail";
import { setProfileAgentTargetMonthly } from "api/profile-detail";
import ConfirmModal from "components/confirm-modal";
import ModalFullPage from "components/modal-full-page";
import moment from "moment";
import { ProfileDetailModalWrapper } from "sections/home/<USER>/styled";
import TargetUser from "sections/team/agent-detail-modal/components/target-user";
import { Footer, InfoPersonal, Tooltip } from "sections/team/agent-detail-modal/edit-pesonal-modal/styled";
import { FlexBox, RowItem } from "styles";
import { ButtonIcon, ButtonPrimary, ButtonSecondary } from "styles/buttons";
import { renderTextTitle } from "..";
import { ModalPayload, TabAndModalType } from "../..";
import { EditMonthModalItem, EditMonthModalWrapper } from "./styled";
import { getDateTo } from "services/untils";

export interface ModalProps {
  modalType: TabAndModalType;
  data: any;
  onGetPerformanceNewBusiness: () => void;
  onCloseModal: () => void;
  onOpenNewModal?: ({ type, title, data }: ModalPayload) => void;
}

const TargetMonthDetailMonth = ({
  data,
  modalType,
  onOpenNewModal,
  onGetPerformanceNewBusiness,
  onCloseModal,
}: ModalProps) => {
  const [openConfirmModal, setOpenConfirmModal] = useState(false);
  const [modifiedData, setModifiedData] = useState<ProfileTargetMonthlyData[]>([]);
  const {
    loading: { getTargetIndividualYearlyLoading },
    user: { username },
  } = useAppSelector((state) => state.rootReducer);

  const actionSetTargetAgentMonth = useActionApi<SetProfileTargetMonthlyInput, any>(setProfileAgentTargetMonthly);

  useEffect(() => {
    setModifiedData(data);
  }, [data]);

  const handleChangeInput = (index: number, name: keyof ProfileTargetMonthlyData, value: any) => {
    setModifiedData((pre: any) => {
      const clone = [...pre];
      clone[index] = {
        ...clone[index],
        [name]: value,
      };
      return clone;
    });
  };

  const handleCancelModal = () => {
    setOpenConfirmModal(false);
  };

  const handleSubmit = () => {
    const payload = modifiedData.reduce((result, data, index) => {
      return {
        ...result,
        [index + 1]: {
          fyp: Number(data.targetFyp),
          cases: Number(data.targetCases),
          ape: Number(data.targetApe),
          month: data.monthNumber,
        },
      };
    }, {});

    actionSetTargetAgentMonth({
      body: {
        agentId: username,
        data: {
          ...payload,
        },
      },
      loading: {
        type: "global",
        name: "setTargetAgentMonthlyLoading",
      },
    })
      .then(({ data }) => {
        if (data) {
          setOpenConfirmModal(false);
          handleClose();
          onGetPerformanceNewBusiness();
        } else {
          Alert("ERROR", "Cập nhật thông tin thất bại");
        }
      })
      .catch((e) => {
        Alert("ERROR", ERROR_API_MESSAGE);
      });
  };

  const handleClose = () => {
    onOpenNewModal({
      type: "target-info",
      title: "Quản lý mục tiêu cá nhân",
      data: null,
    });
  };

  return (
    <ModalFullPage
      show={modalType === "edit-month-target"} //handle modal edit-month
      title={`Chỉnh sửa mục tiêu từng tháng trong năm ${getDateTo("YYYY")}`}
      onClose={handleClose}
    >
      <ProfileDetailModalWrapper background="#e5e5e5">
        {!getTargetIndividualYearlyLoading ? (
          <EditMonthModalWrapper>
            {modifiedData.map((item: ProfileTargetMonthlyData, index) => (
              <EditMonthModalItem key={index}>
                <FlexBox alignItem="center" justifyContent="space-between">
                  <InfoPersonal>
                    <h6 className="h7">
                      Tháng{" "}
                      <span className="color-primary">
                        {item.monthNumber}/{item.thisYear}
                      </span>
                    </h6>
                  </InfoPersonal>

                  <BasicTooltipTrigger
                    placement="bottom-end"
                    tooltip={
                      <Tooltip>
                        <BlockTeamTarget
                          renderTitle={renderTextTitle(
                            "Kết quả đạt được tháng",
                            `${item.monthNumber}/${item.lastYear}`
                          )}
                          showButton={false}
                          subBlock
                        >
                          <TargetUser
                            subTarget
                            fyp={item.lastYearFYP}
                            ape={item.lastYearApe}
                            cases={item.lastYearCases}
                          />
                        </BlockTeamTarget>
                      </Tooltip>
                    }
                  >
                    <ButtonIcon>
                      <Icons icon="question-icon" />
                    </ButtonIcon>
                  </BasicTooltipTrigger>
                </FlexBox>
                <RowItem>
                  <InputNumber
                    value={item.targetFyp ?? 0}
                    placeholder="FYP"
                    onChange={(value) => handleChangeInput(index, "targetFyp", value)}
                    disabled={moment(`${item.monthNumber}/${item.thisYear}`, "M/YYYY")
                      .add(1, "month")
                      .isBefore(moment(), "m")}
                    maxLength={19}
                    allowNegative={false}
                  />
                  <InputNumber
                    value={item.targetApe ?? 0}
                    placeholder="APE"
                    onChange={(value) => handleChangeInput(index, "targetApe", value)}
                    disabled={moment(`${item.monthNumber}/${item.thisYear}`, "M/YYYY")
                      .add(1, "month")
                      .isBefore(moment(), "m")}
                    maxLength={19}
                    allowNegative={false}
                  />
                  <InputNumber
                    value={item.targetCases ?? 0}
                    placeholder="Case"
                    onChange={(value) => handleChangeInput(index, "targetCases", value)}
                    disabled={moment(`${item.monthNumber}/${item.thisYear}`, "M/YYYY")
                      .add(1, "month")
                      .isBefore(moment(), "m")}
                    maxLength={19}
                    allowNegative={false}
                  />
                </RowItem>
              </EditMonthModalItem>
            ))}
          </EditMonthModalWrapper>
        ) : (
          <LoadingSection isFullContent loading={Boolean(getTargetIndividualYearlyLoading)} />
        )}

        <Footer>
          <ButtonSecondary onClick={handleClose}>Quay lại</ButtonSecondary>
          <ButtonPrimary onClick={() => setOpenConfirmModal(true)}>Lưu</ButtonPrimary>
        </Footer>
      </ProfileDetailModalWrapper>
      <ConfirmModal
        onConfirm={handleSubmit}
        onCancel={handleCancelModal}
        show={openConfirmModal}
        title="Lưu thay đổi?"
        content="TVTC có chắc lưu thông tin đã được cập nhật?"
      />
    </ModalFullPage>
  );
};

export default TargetMonthDetailMonth;
