import { ERROR_API_MESSAGE } from "@constants/message";
import {
  GetProfileTargetMonthlyOutput,
  GetProfileTargetYearlyOutput,
  ProfileDetailInput,
  ProfileTargetMonthlyData,
  ProfileTargetYearlyData,
  SetProfileTargetYearlyInput,
} from "@custom-types/profile-detail";
import { getProfileAgentTargetMonthly, getProfileAgentTargetYear, setProfileAgentTargetYear } from "api/profile-detail";
import { Alert } from "components/alert";
import ConfirmModal from "components/confirm-modal";
import InputNumber from "components/input-number";
import LoadingSection from "components/loading";
import ModalFullPage from "components/modal-full-page";
import useActionApi from "hooks/use-action-api";
import { useAppSelector } from "hooks/use-redux";
import { useCallback, useEffect, useMemo, useState } from "react";
import BlockTeamTarget from "sections/team/agent-detail-modal/components/block-team-target";
import TargetUser from "sections/team/agent-detail-modal/components/target-user";
import CardPersonal from "sections/team/agent-detail-modal/team-target-modal/card-personal";
import { TargetInputWrapper } from "sections/team/agent-detail-modal/team-target-modal/styled";
import { ModalPayload, TabAndModalType } from "..";
import { ProfileDetailModalWrapper } from "../styled";
import { CardPersonalWrap, TargetInfoWrap, TargetLeft, TargetRight } from "./styled";
import { getDateTo } from "services/untils";

export interface ChildrenData {
  label: string;
  value: number;
  primary?: boolean;
  onClick?: (value: any) => void;
}

export interface CardData {
  renderTitle: React.ReactNode;
  listTarget: ChildrenData[];
}

export interface UpdateTarget {
  month: boolean;
  personal: boolean;
}

export interface ModalProps {
  modalType: TabAndModalType;
  data: any;
  onCloseModal: () => void;
  onOpenNewModal?: ({ type, title, data }: ModalPayload) => void;
  onGetPerformanceNewBusiness: () => void;
}

export const renderTextTitle = (text: string, year: string | number) => {
  return (
    <>
      {text}
      <span> {year}</span>
    </>
  );
};

const TargetInfoModal = ({
  data,
  modalType,
  onCloseModal,
  onOpenNewModal,
  onGetPerformanceNewBusiness,
}: ModalProps) => {
  const [openConfirmModal, setOpenConfirmModal] = useState(false);

  const [updateTarget, setUpdateTarget] = useState<UpdateTarget>({
    month: true,
    personal: true,
  });
  const [modifiedData, setModifiedData] = useState<ProfileTargetYearlyData>();
  const [listTargetMonth, setListTargetMonth] = useState<ProfileTargetMonthlyData[]>([]);

  const {
    loading: { getTargetAgentYearlyLoading, getTargetAgentMonthlyLoading },
    user: { username, channel },
  } = useAppSelector((state) => state.rootReducer);

  //api
  const actionSetTargetAgentYearly = useActionApi<SetProfileTargetYearlyInput, any>(setProfileAgentTargetYear);
  const actionGetTargetAgentYearly = useActionApi<ProfileDetailInput, GetProfileTargetYearlyOutput>(
    getProfileAgentTargetYear
  );
  const actionGetTargetAgentMonth = useActionApi<ProfileDetailInput, GetProfileTargetMonthlyOutput>(
    getProfileAgentTargetMonthly
  );

  const dataMonthAgentTarget = useMemo(
    () =>
      listTargetMonth.map((list) => {
        return {
          name: "",
          urlImage: ``,
          list: [
            {
              renderTitle: renderTextTitle("Tháng", `${list.monthNumber}/${list.thisYear}`),
              listTarget: [
                { label: "FYP", value: list.targetFyp },
                { label: "APE", value: list.targetApe },
                { label: "Cases", value: list.targetCases },
              ],
            },
            {
              renderTitle: renderTextTitle("Kết quả đạt được tháng", `${list.monthNumber}/${list.lastYear}`),
              listTarget: [
                { label: "FYP", value: list.lastYearFYP },
                { label: "APE", value: list.lastYearApe },
                { label: "Cases", value: list.lastYearCases },
              ],
            },
          ],
        };
      }),
    [listTargetMonth]
  );

  const handleChangeInput = (name: string, value: any) => {
    setModifiedData((pre) => ({
      ...pre,
      [name]: value,
    }));
  };

  const handleUpdateTarget = (name: string, value: boolean) => {
    setUpdateTarget((pre) => ({
      ...pre,
      [name]: false,
    }));

    if (name == "personal" && value == true) {
      setOpenConfirmModal(true);
    }

    if (name == "month") {
      //show modal when click update right
      onOpenNewModal({
        type: "edit-month-target",
        title: `Chỉnh sửa mục tiêu từng tháng trong năm ${data?.thisYear}`,
        data: listTargetMonth,
      });
    }
  };

  const handleCancelModal = () => {
    setOpenConfirmModal(false);
    setUpdateTarget((pre) => ({
      ...pre,
      personal: true,
    }));
  };

  const handleSubmitContentLeft = useCallback(() => {
    const payload = {
      agentId: username,
      fyp: Number(modifiedData?.targetFyp),
      cases: Number(modifiedData?.targetCases),
      ape: Number(modifiedData?.targetApe),
    };
    if (username) {
      actionSetTargetAgentYearly({
        body: payload,
        loading: {
          type: "global",
          name: "setTargetTeamYearlyLoading",
        },
      })
        .then(({ data }) => {
          if (data) {
            setOpenConfirmModal(false);
            setUpdateTarget((pre) => ({
              ...pre,
              personal: true,
            }));
            handleGetTargetAgentYearly();
            onGetPerformanceNewBusiness();
          } else {
            Alert("ERROR", "Cập nhật thông tin thất bại");
          }
        })
        .catch((e) => {
          Alert("ERROR", ERROR_API_MESSAGE);
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [username, modifiedData]);

  const handleGetTargetAgentYearly = useCallback(() => {
    actionGetTargetAgentYearly({
      body: {
        agentId: username,
        channelCd: channel,
      },
      loading: {
        type: "local",
        name: "getTargetAgentYearlyLoading",
      },
    })
      .then(({ data }) => {
        setModifiedData(data.data);
      })
      .catch((e) => Alert("ERROR", ERROR_API_MESSAGE));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [username, channel]);

  useEffect(() => {
    if (username) {
      handleGetTargetAgentYearly();
    }
  }, [username, handleGetTargetAgentYearly]);

  useEffect(() => {
    if (username) {
      actionGetTargetAgentMonth({
        body: {
          agentId: username,
          channelCd: channel,
        },
        loading: {
          type: "local",
          name: "getTargetAgentMonthlyLoading",
        },
      })
        .then(({ data }) => {
          setListTargetMonth(data.listData);
        })
        .catch((e) => Alert("ERROR", ERROR_API_MESSAGE));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [username, channel]);

  return (
    <ModalFullPage
      show={modalType === "target-info"} //handle modal target info
      title="Quản lý mục tiêu cá nhân"
      onClose={onCloseModal}
    >
      <ProfileDetailModalWrapper background="#e5e5e5">
        <TargetInfoWrap>
          <TargetLeft>
            <BlockTeamTarget
              name="personal"
              background="#ffffff"
              renderTitle={renderTextTitle("Mục tiêu cá nhân", modifiedData?.thisYear)}
              showButton={true}
              updateTarget={updateTarget}
              onUpdateTarget={handleUpdateTarget}
            >
              {!getTargetAgentYearlyLoading ? (
                <>
                  {updateTarget.personal ? (
                    <TargetUser
                      fyp={modifiedData?.targetFyp}
                      ape={modifiedData?.targetApe}
                      cases={modifiedData?.targetCases}
                    />
                  ) : (
                    <TargetInputWrapper>
                      <InputNumber
                        value={modifiedData?.targetFyp ?? 0}
                        placeholder="FYP"
                        maxLength={19}
                        allowNegative={false}
                        onChange={(value) => handleChangeInput("targetFyp", value)}
                      />
                      <InputNumber
                        value={modifiedData?.targetApe ?? 0}
                        placeholder="APE"
                        maxLength={19}
                        allowNegative={false}
                        onChange={(value) => handleChangeInput("targetApe", value)}
                      />
                      <InputNumber
                        value={modifiedData?.targetCases ?? 0}
                        placeholder="Case"
                        maxLength={19}
                        allowNegative={false}
                        onChange={(value) => handleChangeInput("targetCases", value)}
                      />
                    </TargetInputWrapper>
                  )}

                  <BlockTeamTarget
                    renderTitle={renderTextTitle("Kết quả cá nhân đạt được năm ", modifiedData?.lastYear)}
                    showButton={false}
                    subBlock
                  >
                    <TargetUser
                      subTarget
                      fyp={modifiedData?.lastYearFYP}
                      ape={modifiedData?.lastYearApe}
                      cases={modifiedData?.lastYearCases}
                    />
                  </BlockTeamTarget>
                </>
              ) : (
                <LoadingSection loading={Boolean(getTargetAgentYearlyLoading)} />
              )}
            </BlockTeamTarget>
          </TargetLeft>
          <TargetRight>
            <BlockTeamTarget
              name="month"
              background="#ffffff"
              renderTitle={renderTextTitle("Mục tiêu từng tháng trong năm ", getDateTo("YYYY"))}
              showButton={true}
              updateTarget={updateTarget}
              onUpdateTarget={handleUpdateTarget}
            >
              {!getTargetAgentMonthlyLoading ? (
                <CardPersonalWrap>
                  <CardPersonal data={dataMonthAgentTarget} haveAvatar={false} />
                </CardPersonalWrap>
              ) : (
                <LoadingSection loading={Boolean(getTargetAgentMonthlyLoading)} />
              )}
            </BlockTeamTarget>
          </TargetRight>
        </TargetInfoWrap>
      </ProfileDetailModalWrapper>
      <ConfirmModal
        onConfirm={handleSubmitContentLeft}
        onCancel={handleCancelModal}
        show={openConfirmModal}
        title="Lưu thay đổi?"
        content="TVTC có chắc lưu thông tin đã được cập nhật?"
      />
    </ModalFullPage>
  );
};

export default TargetInfoModal;
