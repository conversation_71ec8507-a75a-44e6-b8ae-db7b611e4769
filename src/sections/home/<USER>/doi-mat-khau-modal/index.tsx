import Modal from "components/modal";
import { useAppSelector } from "hooks/use-redux";
import { useRouter } from "next/router";
import ChangePasswordForm from "screens/login/change-password";
import { ChangePassWrap } from "./styled";

interface ChangePasswordModalProps {
  show: boolean;
  onCloseModal: () => void;
}

const ChangePasswordModal = ({ show, onCloseModal }: ChangePasswordModalProps) => {
  const router = useRouter();
  // const [showModal, setShowModal] = useState(false);

  const { username } = useAppSelector((state) => state.rootReducer.user);

  const handleLogout = () => {
    //clear cookies
    router.push("/login");
  };
  return (
    <ChangePassWrap>
      {/* <Title>Bảo mật</Title>
      <FlexBox justifyContent="space-between" alignItem="center" className="mt-20">
        <LabelSetting className="body-4"><PERSON><PERSON><PERSON> mật khẩu </LabelSetting>
        <InfoSettingWrap className="pointer" onClick={() => setShowModal(true)}>
          <InfoSetting className="body-4">Cập nhật lần cuối 1/1/2022</InfoSetting>
          <Icons icon="arrow-right" />
        </InfoSettingWrap>
      </FlexBox> */}
      <Modal
        isFooterSticky
        borderHeader
        backgroundMobile="#ffffff"
        title="Đổi mật khẩu"
        show={show}
        onClose={onCloseModal}
      >
        <ChangePasswordForm username={username} showTitle={false} callback={handleLogout} onClose={onCloseModal} />
      </Modal>
    </ChangePassWrap>
  );
};

export default ChangePasswordModal;
