import { BannerData } from "@custom-types/home";
import Carousel from "components/carousel";
import LoadingSection from "components/loading";
import { useAppSelector } from "hooks/use-redux";
import { useDispatch } from "react-redux";
import { downloadDocumentAction } from "screens/bieu-mau-va-ung-dung/bieu-mau/action";
import { BannerImage, BannerWrapper } from "./styled";

export interface BannerProps {
  onClickBannerContest: (data: BannerData) => void;
}
const Banner = ({ onClickBannerContest }: BannerProps) => {
  const { getBannerHomePageLoading } = useAppSelector((state) => state.rootReducer.loading);
  const bannerHomePage = useAppSelector((state) => state.homeReducer.bannerHomePage);

  const dispatch = useDispatch();

  const handleClick = (data: BannerData) => {
    if (data?.typeBanner === "BANNER_CONTEST") {
      onClickBannerContest(data);
    } else {
      dispatch(
        downloadDocumentAction({
          fileId: data?.redirectId,
          link: data?.redirectLink,
        })
      );
    }
  };

  return (
    <BannerWrapper>
      {Boolean(getBannerHomePageLoading) ? (
        <LoadingSection loading />
      ) : bannerHomePage?.length ? (
        <Carousel paginationInside count={bannerHomePage.length}>
          {bannerHomePage.map((item, index) => (
            <BannerImage key={index} url={`data:image/jpg;base64, ${item.banner}`} onClick={() => handleClick(item)} />
          ))}
        </Carousel>
      ) : null}
    </BannerWrapper>
  );
};

export default Banner;
