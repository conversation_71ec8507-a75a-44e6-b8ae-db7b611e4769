import Icons from "components/icons";
import ModalLeft from "components/modal-left";
import { cloneDeep } from "lodash";
import { useCallback, useState } from "react";
import {
  DragDropContext,
  Draggable,
  DraggableProvided,
  Droppable,
  DroppableProvided,
  DropResult,
} from "react-beautiful-dnd";
import { useDispatch } from "react-redux";
import { setSettingView } from "screens/home/<USER>";
import { LinkProps } from "services/menu-link";
import { ButtonIcon, ButtonPrimary } from "styles/buttons";
import { ButtonWrapper, Card, ItemViewWrapper, ListItemWrapper, SettingViewWrapper } from "./styled";

const list: LinkProps[] = [
  { value: "overview", label: "Doanh số phát hành thuần", icon: "setting-view-overview" },
  { value: "release-policy", label: "Phát hành hợp đồng", icon: "setting-view-release-policy" },
  { value: "campaign", label: "<PERSON><PERSON> hoạt động", icon: "setting-view-campaign" },
  { value: "announcement", label: "Thông báo", icon: "icon-setting-view-noti" },
];

const SettingView = () => {
  const [open, setOpen] = useState(false);
  const [config, setConfig] = useState<LinkProps[]>(list);

  const dispatch = useDispatch();

  const handleClose = () => {
    setOpen(false);
  };

  const handleOnDragEnd = useCallback(
    (result: DropResult) => {
      if (!result.destination) {
        return;
      }

      const tampAttribute = cloneDeep(config);
      const [dragItem] = tampAttribute.splice(result.source.index, 1);
      tampAttribute.splice(result.destination.index, 0, dragItem);

      setConfig(tampAttribute);
    },
    [config]
  );

  const handleSaveSettingView = useCallback(() => {
    dispatch(setSettingView(config.map((item) => item.value)));
    setOpen(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [config]);

  return (
    <SettingViewWrapper>
      <ButtonIcon onClick={() => setOpen(true)}>
        <Icons icon="setting-view" />
      </ButtonIcon>
      <ModalLeft title="Cài đặt hiển thị" show={open} onClose={handleClose}>
        <DragDropContext onDragEnd={handleOnDragEnd}>
          <Droppable droppableId="setting-drag">
            {(provided: DroppableProvided) => (
              <ListItemWrapper {...provided.droppableProps} ref={provided.innerRef}>
                {config.map((item, index) => {
                  return (
                    <Draggable key={item.value} draggableId={item.value} index={index}>
                      {(provided: DraggableProvided) => (
                        <ItemViewWrapper
                          key={index}
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                        >
                          <ButtonIcon>
                            <Icons icon="setting-vew-hamburger" />
                          </ButtonIcon>
                          <Card>
                            <Icons icon={item.icon} />
                            <p>{item.label}</p>
                          </Card>
                        </ItemViewWrapper>
                      )}
                    </Draggable>
                  );
                })}
                {provided.placeholder}
              </ListItemWrapper>
            )}
          </Droppable>
        </DragDropContext>
        <ButtonWrapper>
          <ButtonPrimary onClick={handleSaveSettingView}>Lưu</ButtonPrimary>
        </ButtonWrapper>
      </ModalLeft>
    </SettingViewWrapper>
  );
};

export default SettingView;
