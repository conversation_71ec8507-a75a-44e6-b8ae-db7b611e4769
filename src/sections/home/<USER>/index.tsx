import { BirthdayReminderData } from "@custom-types/home";
import Block from "components/block";
import { BlockContentWrapper } from "components/block/styled";
import Icons from "components/icons";
import LoadingSection from "components/loading";
import { PhoneNumber } from "components/task/styled";
import { useAppSelector } from "hooks/use-redux";
import { useState } from "react";
import { formatValue } from "services/format-value";
import BirthdayModal from "../danh-sach-sinh-nhat";
import { BirthdayCard, BirthdayCardTitle, Calendar, TaskListWrapper } from "./styled";

const BirthdayReminder = ({ data }: { data: BirthdayReminderData[] }) => {
  const [showModal, setShowModal] = useState(false);

  const { getGetBirthdayReminderLoading, userMeLoading } = useAppSelector((state) => state.rootReducer.loading);

  return (
    <Block type="see-more" title="Sinh nhật" background="#FAE4D3" onClick={() => setShowModal(true)}>
      <BlockContentWrapper>
        <TaskListWrapper>
          {Boolean(getGetBirthdayReminderLoading) || Boolean(userMeLoading) ? (
            <LoadingSection loading />
          ) : data?.length ? (
            data.map((item, index) => (
              <BirthdayCard key={index}>
                <Icons icon="icon-birthday" />
                <BirthdayCardTitle>
                  <h6 className="h7 text-uppercase">{item.fullName}</h6>
                  <Calendar>
                    <Icons icon="icon-calendar" width={14} height={14} fill="#8B8E8F" />
                    <label className="label-5">{formatValue(item.birthdate, "date")}</label>
                  </Calendar>
                </BirthdayCardTitle>
                <PhoneNumber>
                  <Icons icon="phone-line" width={16} height={16} />
                  <a href={`tel:${item.phoneNumber}`}>{item.phoneNumber}</a>
                </PhoneNumber>
              </BirthdayCard>
            ))
          ) : (
            <p className="body-5">Không có sinh nhật của KH trong hôm nay.</p>
          )}
        </TaskListWrapper>
      </BlockContentWrapper>
      <BirthdayModal show={showModal} onClose={() => setShowModal(false)} />
    </Block>
  );
};

export default BirthdayReminder;
