import { CampaignData } from "@custom-types/campaign";
import Block from "components/block";
import { BlockContentWrapper } from "components/block/styled";
import Carousel from "components/carousel";
import Icons from "components/icons";
import LoadingSection from "components/loading";
import { useAppSelector } from "hooks/use-redux";
import { get, range } from "lodash";
import { useRouter } from "next/router";
import { useDispatch } from "react-redux";
import { setSelectCampaign } from "screens/home/<USER>";
import { formatNumber } from "services/untils";
import { NoData, Spacing } from "styles";
import { CampaignWrapper, CardContractItem, CardContractList, Header } from "./styled";
import { useMemo } from "react";

const renderColor = [
  {
    primary: "#6ECEB2",
    secondary: "#E2F5F0",
  },
  {
    primary: "#0097A9",
    secondary: "#CCEAEE",
  },
  {
    primary: "#FED141",
    secondary: "#FFF6D9",
  },
  {
    primary: "#03824F",
    secondary: "#F2F9F6",
  },
  {
    primary: "#E87722",
    secondary: "#FEF3F3",
  },
  {
    primary: "#183028",
    secondary: "#EDEFF0",
  },
];

const Campaign = () => {
  const router = useRouter();
  const dispatch = useDispatch();

  const { totalPolicyPerCampaignList } = useAppSelector((state) => state.homeReducer);
  const { getActivityHomeLoading, userMeLoading } = useAppSelector((state) => state.rootReducer.loading);

  const handleRedirect = (item: CampaignData) => {
    dispatch(setSelectCampaign(item));
    router.push("/thong-tin-hop-dong/hop-dong-thuoc-chuong-trinh");
  };

  const formatListCampaign = useMemo(() => {
    const brilliantSummerObj = totalPolicyPerCampaignList?.find((e) => e.campaignCode === "BRILLIANT_SUMMER");
    const otherObjects = totalPolicyPerCampaignList?.filter((e) => e.campaignCode !== "BRILLIANT_SUMMER");

    return brilliantSummerObj ? [brilliantSummerObj, ...otherObjects] : totalPolicyPerCampaignList || [];
  }, [totalPolicyPerCampaignList]);

  return (
    <CampaignWrapper>
      <Block
        type="see-more"
        title="Hợp đồng thuộc chương trình (Campaign)"
        background="#EDEFF0"
        onClick={() => handleRedirect(null)}
      >
        <BlockContentWrapper>
          {Boolean(getActivityHomeLoading) || Boolean(userMeLoading) ? (
            <LoadingSection loading />
          ) : formatListCampaign?.length ? (
            <CardContractList>
              <Carousel count={Math.ceil(formatListCampaign.length / 3)}>
                <>
                  {formatListCampaign.map((item, index) => (
                    <CardContractItem
                      key={index}
                      color={get(renderColor, [index % renderColor.length])}
                      onClick={() => handleRedirect(item)}
                    >
                      <Header>
                        <Icons icon="campaign" width={32} height={32} />
                      </Header>
                      <p>{item.campaignTitle}</p>
                      <Spacing />
                      <h3>{formatNumber(item.totalPolicy)}</h3>
                    </CardContractItem>
                  ))}
                  {formatListCampaign.length > 3
                    ? range(0, (formatListCampaign.length % 3) + 1).map((_, index) => (
                        <CardContractItem key={index} hide></CardContractItem>
                      ))
                    : null}
                </>
              </Carousel>
            </CardContractList>
          ) : (
            <NoData>Không có dữ liệu</NoData>
          )}
        </BlockContentWrapper>
      </Block>
    </CampaignWrapper>
  );
};

export default Campaign;
