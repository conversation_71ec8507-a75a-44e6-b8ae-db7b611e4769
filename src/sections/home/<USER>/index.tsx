import { PolicyOverDueData } from "@custom-types/home";
import Block from "components/block";
import { BlockContentWrapper } from "components/block/styled";
import LoadingSection from "components/loading";
import Task from "components/task";
import { useAppSelector } from "hooks/use-redux";
import { useRouter } from "next/router";
import { TaskListWrapper } from "./styled";

const ContractOverDue = ({
  data,
  onShowDetail,
}: {
  data: PolicyOverDueData[];
  onShowDetail: (data: PolicyOverDueData) => void;
}) => {
  const router = useRouter();

  const { getPolicyOverdueLoading, userMeLoading } = useAppSelector((state) => state.rootReducer.loading);

  const handleRedirect = () => {
    router.push("/thong-tin-hop-dong/quan-ly-hop-dong/hop-dong-mat-hieu-luc");
  };

  return (
    <Block type="see-more" title="<PERSON>ợ<PERSON> đồng quá hạn đóng phí" background="#FAE4D3" onClick={handleRedirect}>
      <BlockContentWrapper>
        <TaskListWrapper>
          {Boolean(getPolicyOverdueLoading) || Boolean(userMeLoading) ? (
            <LoadingSection loading />
          ) : data?.length ? (
            data.map((item, index) => (
              <Task
                onClick={() => onShowDetail(item)}
                key={index}
                status="expired"
                day={item.lapseTime}
                name={item.fullnameOW}
                phoneNumber={item.phoneNumber}
              />
            ))
          ) : (
            <p className="body-5">Không có dữ liệu.</p>
          )}
        </TaskListWrapper>
      </BlockContentWrapper>
    </Block>
  );
};

export default ContractOverDue;
