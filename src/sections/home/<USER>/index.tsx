import { ERROR, ERROR_API_MESSAGE } from "@constants/message";
import {
  AgentIdInputWidthChannel,
  NotificationData,
  NotificationOutput,
  ReadNotificationInput,
} from "@custom-types/home";
import { getNotificationList, readNotification } from "api/home";
import { Alert } from "components/alert";
import Icons from "components/icons";
import LoadingSection from "components/loading";
import ModalLeft from "components/modal-left";
import useActionApi from "hooks/use-action-api";
import { useAppSelector } from "hooks/use-redux";
import useWindowResize from "hooks/use-window-resize";
import moment from "moment";
import { useCallback, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import { setIsNewNotify, setModalProfileDetail, setOpenNotification } from "screens/home/<USER>";
import { coverDateNumberToString } from "services/untils";
import { FlexBox } from "styles";
import { ButtonIcon } from "styles/buttons";
import { NotificationItem, NotificationSectionWrapper, NotificationWrapper } from "./styled";

const Notification = () => {
  const [notificationList, setNotificationList] = useState<NotificationData[]>([]);
  const [loadingItem, setLoadingItem] = useState("");
  const { width } = useWindowResize();
  const dispatch = useDispatch();

  const { openNotification, isNewNotify } = useAppSelector((state) => state.homeReducer);
  const {
    user: { username, channel },
    loading: { getNotificationLoading, readNotificationLoading },
  } = useAppSelector((state) => state.rootReducer);

  const actionGetNotificationList = useActionApi<AgentIdInputWidthChannel, NotificationOutput>(getNotificationList);
  const actionReadNotification = useActionApi<ReadNotificationInput>(readNotification);

  const [today, past] = useMemo(() => {
    return [
      notificationList.filter((item) => coverDateNumberToString(item.createdDate) === moment().format("DD/MM/YYYY")),
      notificationList.filter((item) => coverDateNumberToString(item.createdDate) !== moment().format("DD/MM/YYYY")),
    ];
  }, [notificationList]);

  const handleReadNotification = useCallback(
    (item: NotificationData) => {
      if (item.systemId === "eLetters") {
        dispatch(setOpenNotification(false));
        dispatch(
          setModalProfileDetail({
            show: true,
            type: "e-contract",
            title: "Thư điện tử",
          })
        );
      }

      if (item.link && item.systemId !== "eLetters") {
        var linkRedirect = document.createElement("a");
        linkRedirect.href = item.link;
        linkRedirect.target = "_blank";
        linkRedirect.rel = "noopener noreferrer";
        document.body.appendChild(linkRedirect);
        linkRedirect.click();
        document.body.removeChild(linkRedirect);
      }
      setLoadingItem(item.notifyId);

      actionReadNotification({
        body: { agentId: username, notifyId: item.notifyId, systemId: item.systemId },
        isCache: false,
        loading: {
          type: "local",
          name: "readNotificationLoading",
        },
      }).then((data) => {
        if (data.status === 200) {
          let newNotifyList = notificationList.map((notification) =>
            notification.notifyId === item.notifyId ? { ...notification, isRead: "Y" } : notification
          );
          setNotificationList(newNotifyList);

          if (newNotifyList?.some((e) => e.isRead === "N")) {
            dispatch(setIsNewNotify(true));
          } else {
            dispatch(setIsNewNotify(false));
          }
        }
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [username, notificationList]
  );

  const handleOpenNotification = useCallback(() => {
    dispatch(setOpenNotification(true));

    actionGetNotificationList({
      body: { agentId: username, channelCd: channel },
      isCache: false,
      loading: {
        type: "local",
        name: "getNotificationLoading",
      },
    })
      .then(({ data }) => {
        setNotificationList(data?.listData);
        if (data?.listData?.some((e) => e.isRead === "N")) {
          dispatch(setIsNewNotify(true));
        } else {
          dispatch(setIsNewNotify(false));
        }
      })
      .catch((error) => {
        console.log(error);
        Alert(ERROR, ERROR_API_MESSAGE);
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [username, channel]);

  return (
    <NotificationSectionWrapper isNewNotify={isNewNotify}>
      <ButtonIcon>
        <FlexBox gap={8} className="icon-notify">
          <div onClick={handleOpenNotification}>
            <Icons icon={width > 768 ? "notification" : "notification-mobile"} />
          </div>
        </FlexBox>
      </ButtonIcon>
      <ModalLeft show={openNotification} title="Thông báo" onClose={() => dispatch(setOpenNotification(false))}>
        <NotificationWrapper>
          <label className="label-3">Hôm nay</label>
          {getNotificationLoading ? (
            <LoadingSection loading />
          ) : (
            today.map((item, index) => (
              <NotificationItem key={index} isUnRead={item.isRead === "N"} onClick={() => handleReadNotification(item)}>
                <Icons icon="notification-item" />
                <h5 className="h7">{item.notifyTitle}</h5>
                <p className="body-4">{item.notifyDesc}</p>
                <h6 className="h8">{moment(item.createdDate).fromNow()}</h6>
                <LoadingSection
                  loading={Boolean(readNotificationLoading) && loadingItem === item.notifyId}
                  isFullContent
                />
              </NotificationItem>
            ))
          )}
          <label className="label-3">Thông báo cũ hơn</label>
          {getNotificationLoading ? (
            <LoadingSection loading />
          ) : (
            past.map((item, index) => (
              <NotificationItem key={index} isUnRead={item.isRead === "N"} onClick={() => handleReadNotification(item)}>
                <Icons icon="notification-item" />
                <h5 className="h7">{item.notifyTitle}</h5>
                <p className="body-4">{item.notifyDesc}</p>
                <h6 className="h8">{moment(item.createdDate).fromNow()}</h6>
                <LoadingSection
                  loading={Boolean(readNotificationLoading) && loadingItem === item.notifyId}
                  isFullContent
                />
              </NotificationItem>
            ))
          )}
        </NotificationWrapper>
      </ModalLeft>
    </NotificationSectionWrapper>
  );
};

export default Notification;
