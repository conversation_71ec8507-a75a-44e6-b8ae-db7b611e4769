import Icons from "components/icons";
import { useAppSelector } from "hooks/use-redux";
import { LinkProps } from "services/menu-link";
import SettingView from "../setting-view";
import { IconWrapper, ProjectItem, ProjectsList, ProjectsWrapper, TitleWrapper } from "./styled";
import ScrollBar from "components/scroll-bar";

interface ProjectsProps {
  showSettingView?: boolean;
  list: LinkProps[];
  onRedirect: (item: LinkProps) => void;
}

const Projects = ({ showSettingView = true, list, onRedirect }: ProjectsProps) => {
  const { name } = useAppSelector((state) => state.rootReducer.user);

  return (
    <ProjectsWrapper className="hide-mobile">
      <TitleWrapper>
        <h4>{name ? `Chào ${name}` : null}&nbsp;</h4>
        {showSettingView ? <SettingView /> : null}
        <p>Công cụ hỗ trợ TVTC</p>
      </TitleWrapper>
      <ScrollBar scrollX>
        <ProjectsList>
          {list.map((item, index) =>
            item?.show ? (
              <ProjectItem key={index} onClick={() => onRedirect(item)}>
                <IconWrapper>
                  <Icons icon={item.icon} />
                  <Icons icon="arrow-right" />
                </IconWrapper>
                <p className="body-5">{item.label}</p>
              </ProjectItem>
            ) : null
          )}
        </ProjectsList>
      </ScrollBar>
    </ProjectsWrapper>
  );
};

export default Projects;
