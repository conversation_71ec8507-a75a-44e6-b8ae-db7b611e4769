import LoadingSection from "components/loading";
import { LoadingWrapper, UploadLoading } from "components/loading/styled";
import { useAppSelector } from "hooks/use-redux";
import Notification from "sections/home/<USER>";
import { AvatarWrapper, InfoWrapper, NotificationWrapper, ProfileWrapperDesktop, SocialWrapper } from "./styled";

export interface ProfileDesktop {
  showNotification?: boolean;
  achievementList: string[];
  onShowModal: () => void;
}

const ProfileDesktop = ({ showNotification = true, achievementList, onShowModal }: ProfileDesktop) => {
  const { avatar } = useAppSelector((state) => state.homeReducer);
  const {
    user: { name, designationDesc },
    loading: { getUserInfoLoading, getAchievementProfileLoading, userMeLoading, getAgentAvatarLoading },
  } = useAppSelector((state) => state.rootReducer);

  return (
    <ProfileWrapperDesktop className="hide-mobile">
      <AvatarWrapper>
        <img
          src={`data:image/jpg;base64,${avatar}`}
          alt="avatar"
          onError={({ currentTarget }) => {
            currentTarget.onerror = null;
            currentTarget.src = `${process.env.basePath}/img/avatar-default.jpg`;
          }}
        />
        {getAgentAvatarLoading ? (
          <LoadingWrapper isFullContent>
            <UploadLoading />
          </LoadingWrapper>
        ) : null}
      </AvatarWrapper>
      <InfoWrapper>
        {getUserInfoLoading || getAchievementProfileLoading || userMeLoading ? (
          <LoadingSection loading />
        ) : (
          <>
            <h6 onClick={onShowModal} className="pointer">
              {name}
            </h6>
            <p className="body-5">{designationDesc}</p>
            <SocialWrapper>
              {achievementList?.length
                ? achievementList.map((item, index) => (
                    <img
                      alt="achievement"
                      key={index}
                      src={`${process.env.basePath}/img/achievement/${item?.toLowerCase()}.jpg`}
                    />
                  ))
                : null}
            </SocialWrapper>
          </>
        )}
      </InfoWrapper>
      {showNotification ? (
        <NotificationWrapper>
          <Notification />
        </NotificationWrapper>
      ) : null}
    </ProfileWrapperDesktop>
  );
};

export default ProfileDesktop;
