import styled from "styled-components";
import { device } from "styles/media";

export const ProfileWrapperDesktop = styled.div`
  width: 100%;
  padding-right: 92px;

  display: flex;

  position: relative;
`;

export const AvatarWrapper = styled.div`
  width: 100px;
  height: 100px;
  margin-right: 12px;

  overflow: hidden;
  position: relative;

  border: 2px solid #ffffff;
  border-radius: 50%;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  @media ${device.mobile} {
    width: 80px;
    min-width: 80px;
    height: 80px;
    margin: 0px 14px 0px 12px;
  }
`;

export const InfoWrapper = styled.div`
  flex: 1;

  h6 {
    color: ${({ theme }) => theme.color.text.primary};
  }

  p {
    margin: 4px 0;
  }
`;

export const SocialWrapper = styled.div`
  display: flex;
  flex-direction: row;

  img {
    height: 36px;
    width: 36px;
    margin-right: 2px;

    border-radius: 2px;
  }
`;

export const WrapperIcon = styled.div`
  height: 40px;
  width: 40px;

  cursor: pointer;
`;

export const TaskToday = styled.h6`
  margin: 30px 0px 24px 0px;
`;

export const Flex = styled.div`
  display: flex;
  justify-content: space-between;
`;

export const NotificationWrapper = styled.div`
  position: absolute;
  top: 0;
  right: 0;

  display: flex;
  gap: 8px;
`;
