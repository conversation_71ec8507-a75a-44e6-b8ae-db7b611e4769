import LoadingSection from "components/loading";
import { useAppSelector } from "hooks/use-redux";
import { FlexBox } from "styles";
import { AvatarWrapper } from "../desktop/styled";
import { InfoWrapper, ProfileWrapperMobile, SocialWrapper } from "./styled";

export interface ProfileMobile {
  achievementList: string[];
  onShowModal: () => void;
}

const ProfileMobile = ({ achievementList, onShowModal }: ProfileMobile) => {
  const { avatar } = useAppSelector((state) => state.homeReducer);
  const {
    user: { name, designationDesc },
    loading: { getUserInfoLoading, getAchievementProfileLoading, userMeLoading },
  } = useAppSelector((state) => state.rootReducer);

  return (
    <ProfileWrapperMobile className="hide-desktop">
      <FlexBox justifyContent="flex-start" alignItem="center">
        <AvatarWrapper>
          <img
            src={`data:image/jpg;base64,${avatar}`}
            alt="avatar"
            onError={({ currentTarget }) => {
              currentTarget.onerror = null;
              currentTarget.src = `${process.env.basePath}/img/avatar-default.jpg`;
            }}
          />
        </AvatarWrapper>
        {userMeLoading || getUserInfoLoading || getAchievementProfileLoading ? (
          <LoadingSection loading color="#ffffff" />
        ) : (
          <InfoWrapper>
            <h6 className="h8 pointer" onClick={onShowModal}>
              {name}
            </h6>
            <p className="body-6">{designationDesc}</p>
            {achievementList?.length ? (
              <SocialWrapper>
                {achievementList.map((item, index) => (
                  <img key={index} src={`${process.env.basePath}/img/achievement/${item?.toLowerCase()}.jpg`} alt="" />
                ))}
              </SocialWrapper>
            ) : null}
          </InfoWrapper>
        )}
      </FlexBox>
    </ProfileWrapperMobile>
  );
};

export default ProfileMobile;
