import styled from "styled-components";
import { device } from "styles/media";

//modal
export const AgentDetailModalWrapper = styled.div`
  width: 100%;
  display: flex;

  padding-bottom: 24px;
  background: #e5e5e5;

  @media ${device.mobile} {
    flex-direction: column;
    padding: 24px 16px 100px 16px;
  }
`;

export const ModalNormalWrapper = styled.div`
  width: 100%;
  background: #ffffff;

  @media ${device.mobile} {
    padding: 0px 16px 100px 16px;
  }
`;
