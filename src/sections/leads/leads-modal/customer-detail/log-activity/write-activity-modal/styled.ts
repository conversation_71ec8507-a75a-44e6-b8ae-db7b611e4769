import styled from "styled-components";
import { RowItem } from "styles";
import { device } from "styles/media";

export const WriteActivityModalWrapper = styled.div`
  padding-bottom: 0;

  .${RowItem.styledComponentId} {
    & > div:not(:last-child) {
      margin-right: 16px;
      margin-bottom: 7px;
    }
  }

  button {
    margin: 0;
  }

  @media ${device.mobile} {
    padding: 0 16px 16px 16px;

    .${RowItem.styledComponentId} {
      & > div:not(:last-child) {
        margin-right: 16px;
        margin-bottom: 24px;
      }

      &:not(:last-child) {
        margin-bottom: 24px;
      }
    }
  }
`;
