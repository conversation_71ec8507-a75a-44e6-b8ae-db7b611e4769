import { getLeadsSearch } from "api/leads";
import Block from "components/block";
import Icons from "components/icons";
import useActionApi from "hooks/use-action-api";
import { useAppSelector } from "hooks/use-redux";
import { isNil } from "lodash";
import { useRouter } from "next/router";
import { useEffect, useMemo, useState } from "react";
import {
  listGender,
  listIncome,
  listMarital,
  listOccupation,
  listPineline,
  listProductCare,
  listType,
} from "screens/leads/constants";
import { districts } from "screens/leads/constants/districts";
import { provinces } from "screens/leads/constants/provinces";
import { wards } from "screens/leads/constants/wards";
import { FlexBox } from "styles";
import { ButtonIcon } from "styles/buttons";
import EditCustomerModal from "../edit";
import { CustomerProfileName } from "../styled";
import { PairTextWrapper, ProfileAccountWrapper } from "./styled";

interface Props {
  initial: any;
}

const ProfileAccount = ({ initial }: Props) => {
  const router = useRouter();

  const [showEdit, setShowEdit] = useState(false);
  const [data, setData] = useState<any>();

  const { username, channel } = useAppSelector((state) => state.rootReducer.user);

  const actionGetLeadsSearch = useActionApi(getLeadsSearch);

  useEffect(() => {
    if (router.query.seq) {
      actionGetLeadsSearch({
        body: {
          seq: Number(router.query.seq),
          createdBy: null,
          partner: null,
          status: null,
          page: 0,
          size: 1,
        },
        loading: {
          type: "global",
          name: "",
        },
      })
        .then(({ data }) => {
          if (data.content.length) {
            setData(data.content[0]);
          }
        })
        .catch((error) => {
          // Alert(ERROR, ERROR_API_MESSAGE);
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [username, showEdit, router.query.seq]);

  const dataAccount = useMemo(
    () => [
      {
        title: "Thông tin cá nhân",
        children: [
          {
            title: "Sản phẩm quan tâm",
            value: listProductCare.find((item) => item.value == data?.policySuggestion)?.label,
          },
          {
            title: "Ngày sinh",
            value: data?.dob,
          },
          {
            title: "Tuổi",
            value: data?.age,
          },
          {
            title: "Giới tính",
            value: listGender.find((item) => item.value == data?.gender)?.label,
          },
          {
            title: "Tình trạng hôn nhân",
            value: listMarital.find((item) => item.value == data?.maritalStatus)?.label,
          },
          {
            title: "Số con",
            value: data?.childrenNo,
          },
          {
            title: "Loại giấy tờ",
            value: listType.find((item) => item.value == data?.idType)?.label,
          },
          {
            title: "Số giấy tờ",
            value: data?.idNumber,
          },
          {
            title: "Số điện thoại",
            value: data?.mobilePhone,
          },
          {
            title: "Email",
            value: data?.email,
          },
          {
            title: "Thu nhập hàng tháng ước tính \n (triệu VNĐ)",
            value: listIncome.find((item) => item.value == data?.income)?.label,
          },
          {
            title: "Kênh",
            value: channel,
          },
        ],
      },
      {
        title: "Địa chỉ liên hệ",
        children: [
          {
            title: "Tỉnh/ Thành phố",
            value: provinces.find((item) => item.provincecd == data?.addressCityCd)?.provincedesc,
          },
          {
            title: "Quận/ Huyện",
            value: districts.find((item) => item.districtcd == data?.addressDistrictCd)?.districtdesc,
          },
          {
            title: "Xã/ Phường",
            value: wards.find((item) => item.wardcd == data?.addressWardCd)?.warddesc,
          },
          {
            title: "Tên đường",
            value: data?.addressLine1,
          },
          {
            title: "Số nhà/ Tên tòa nhà",
            value: data?.addressLine2,
          },
        ],
      },
      {
        title: "Thông tin nghề nghiệp",
        children: [
          {
            title: "Nghề nghiệp",
            value: listOccupation.find((item) => item.value == data?.occupationDesc)?.label,
          },
        ],
      },
      {
        title: "Thông tin pipeline",
        children: [
          {
            title: "Pineline",
            value: listPineline.find((item) => item.value == data?.pipeline)?.label,
          },
          {
            title: "Nguồn chiến dịch",
            value: data?.utmSource,
          },
          {
            title: "Thời hạn chiến dịch",
            value: data?.utmTerm,
          },
          {
            title: "Tên chiến dịch",
            value: data?.utmCampaign,
          },
        ],
      },
      {
        title: "Thông tin khác",
        children: [
          {
            title: "Ngày tạo KH",
            value: data?.createdDate,
          },
          {
            title: "Ngày cập nhật gần nhất",
            value: data?.lastModifiedDate,
          },
          {
            title: "Ghi chú",
            value: data?.note,
          },
          {
            title: "Phiếu Chấp Thuận Cho Phép \n Xử Lý Dữ Liệu Cá Nhân",
            value: data?.consent,
            primary: false,
          },
        ],
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [data]
  );
  return (
    <ProfileAccountWrapper>
      <FlexBox direction="column">
        <CustomerProfileName>
          <FlexBox justifyContent="space-between" alignItem="center">
            <FlexBox alignItem="center" gap={12}>
              <Icons icon="icon-customer" height={56} width={56} />

              <FlexBox direction="column">
                <h6 className="color-primary">{data?.fullname}</h6>
                <p className="body-5 mt-4">Ngày tạo KH {data?.createdDate}</p>
              </FlexBox>
            </FlexBox>
            <ButtonIcon onClick={() => setShowEdit(true)}>
              <Icons icon="icon-edit-leads" height={40} width={40} />
            </ButtonIcon>
          </FlexBox>
        </CustomerProfileName>

        {/* fix click block collapse => body no scroll */}
        {!initial && <div style={{ height: "calc(100 * var(--vh) + 1)" }} />}

        <FlexBox direction="column" className="mt-16">
          {dataAccount.map((data) => (
            <CustomerProfileName key={data.title}>
              <Block type="collapse" title={data.title} defaultOpen={true}>
                <FlexBox className="mt-16" direction="column" justifyContent="center" alignItem="flex-start">
                  {data.children.map((item, key) => (
                    <PairTextWrapper primary={item.primary} key={key}>
                      <label className="label-4">{item.title}</label>
                      {!isNil(item.value) ? <label className="label-3">{item.value}</label> : <label>-</label>}
                    </PairTextWrapper>
                  ))}
                </FlexBox>
              </Block>
            </CustomerProfileName>
          ))}
        </FlexBox>
      </FlexBox>

      {showEdit && <EditCustomerModal showEdit={showEdit} setShowEdit={setShowEdit} />}
    </ProfileAccountWrapper>
  );
};

export default ProfileAccount;
