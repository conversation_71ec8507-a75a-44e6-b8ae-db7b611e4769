import { DataTableLeadsProps } from "@custom-types/leads";
import Table from "components/table";
import TableMobile from "components/table-mobile";
import { LeadsTableWrapper } from "./styled";

const LeadsTable = ({ config, data, showOrderNo = false, showPagination = false }: DataTableLeadsProps) => {
  // const { getPolicyTrackingLoading } = useAppSelector((state) => state.rootReducer.loading);

  return (
    <LeadsTableWrapper>
      <Table
        showPagination={showPagination}
        showOrderNo={showOrderNo}
        showConfig={false}
        config={config}
        data={data}
        loading={false}
      />
      <TableMobile config={config} data={data} />
    </LeadsTableWrapper>
  );
};

export default LeadsTable;
