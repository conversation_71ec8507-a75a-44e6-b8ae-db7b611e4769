import { ERROR, ERROR_API_MESSAGE } from "@constants/message";
import { ValueLabel } from "@custom-types";
import { DataTable, TableConfig } from "@custom-types/config-table";
import { AssignSaleLeadsInput, LeadsGetAllActivityStatusOutput, LeadsSearchOutPutAndUpdate } from "@custom-types/leads";
import { assignSaleLeads, getLeadsAllActive } from "api/leads";
import { Alert } from "components/alert";
import DropDownFieldset from "components/drop-down-fieldset";
import LoadingPage from "components/loading-page";
import useActionApi from "hooks/use-action-api";
import { useAppSelector } from "hooks/use-redux";
import { get } from "lodash";
import { Dispatch, SetStateAction, useCallback, useEffect, useMemo, useState } from "react";
import { formatValueTable } from "services/format-value";
import { RoleLeads, grantPermissionLeads } from "services/leads";
import { ButtonFooterSticky, RowItem } from "styles";
import { ButtonPrimary } from "styles/buttons";
import LeadsTable from "../leads-table";
import { InfoAllocationCustomerModalWrapper } from "./styled";

interface InfoAllocationCustomerModalProps {
  data?: {
    listChecked: LeadsSearchOutPutAndUpdate[];
    role: RoleLeads;
    listStatusAll: LeadsGetAllActivityStatusOutput[];
  };
  onClose: () => void;
  setSaveAssign: Dispatch<SetStateAction<boolean>>;
}

const configTable: TableConfig[] = [
  { key: "fullname", show: true, type: "string", label: "Họ Tên" },
  { key: "gender", show: true, type: "string", label: "Giới tính" },
  { key: "dob", show: true, type: "date", label: "Ngày sinh" },
  { key: "mobilePhone", show: true, type: "string", label: "Số điện thoại <br/>liên lạc" },
  { key: "status", show: true, type: "rich-text", label: "Tình trạng" },
  { key: "sale", show: true, type: "string", label: "TVTC được phân bổ" },
  { key: "appointmentDate", show: true, type: "date", label: "Ngày hẹn" },
  { key: "createdDate", show: true, type: "date", label: "Ngày tạo KH" },
  { key: "lastModifiedDate", show: true, type: "date", label: "Ngày cập nhật <br/>gần nhất" },
];

interface FormAssign {
  designation: ValueLabel;
  agentName: ValueLabel;
}

const InfoAllocationCustomerModal = ({ data, onClose, setSaveAssign }: InfoAllocationCustomerModalProps) => {
  const [agentList, setAgentList] = useState<ValueLabel[]>();
  const [designationList, setDesignationList] = useState<ValueLabel[]>();
  const [loading, setLoading] = useState(false);
  const [form, setForm] = useState<FormAssign>({
    designation: null,
    agentName: null,
  });

  const {
    loading: { getListActiveListLoading },
  } = useAppSelector((state) => state.rootReducer);

  const { listChecked, listStatusAll } = data ?? {};

  const actionGetActiveLeadsAndDesignation = useActionApi(getLeadsAllActive);
  const actionAssignSale = useActionApi<AssignSaleLeadsInput, { successful: boolean; messages: string }>(
    assignSaleLeads
  );

  useEffect(() => {
    const listAgent = grantPermissionLeads.find((item) => item.role == "agent"); //only agent

    setDesignationList(
      listAgent.children.map((i) => ({
        label: i,
        value: i,
      }))
    );
  }, []);

  useEffect(() => {
    if (data) {
      actionGetActiveLeadsAndDesignation({
        loading: {
          type: "local",
          name: "getListActiveListLoading",
        },
      })
        .then(({ data }) => {
          if (data.length > 0) {
            setAgentList(
              data.map(([agentCode, designation, agentName]) => ({
                label: `${agentCode} - ${agentName}`,
                value: `${designation}_${agentCode}`,
              }))
            );
          }
        })
        .catch((err) => {
          console.log(err);
          Alert(ERROR, ERROR_API_MESSAGE);
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const formatValueTableLeads = useCallback(
    (data: any, config: TableConfig) => {
      switch (config.key) {
        // case "mobilePhone":
        //   return data[config.key]?.replace("+84", "0");
        case "gender":
          return data[config.key] === "F" ? "Nữ" : "Nam";
        case "status":
          return listStatusAll?.find((i) => i.code === data[config.key])?.name ?? "-";
        default:
          return formatValueTable(data, config);
      }
    },
    [listStatusAll]
  );

  const formatData = useMemo(() => {
    if (data) {
      return listChecked.map((item) => ({
        ...item,
        appointmentDate: item.appointmentDate && `${item.appointmentDate} - ${item.appointmentTime}`,
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  const dataTable: DataTable[][] = useMemo(
    () =>
      formatData?.map((d: any) =>
        configTable.map((config) => ({
          config: config,
          node: formatValueTableLeads(d, config),
          originData: get(d, [config.key]),
        }))
      ) ?? [],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [listChecked]
  );

  const filterAgentList = useMemo(() => {
    return agentList?.filter((i) => i.value?.split("_")[0] === form?.designation?.value);
  }, [agentList, form?.designation?.value]);

  const handleSave = () => {
    let countSuccess = 0;
    let apiErrMsg = "";
    setLoading(true);

    const apiPromises = listChecked.map((item) => {
      return actionAssignSale({
        loading: {
          name: "",
          type: "",
        },
        body: {
          sale: form.agentName.value.split("_")[1], // form agentName: {label:'1',value:'DESIGNATION_agentCode'},  value.split('_')[1] để lấy agentCode.
          seq: item.seq,
        },
      })
        .then(({ data }) => {
          if (data?.successful) {
            countSuccess++;
          } else {
            apiErrMsg = data?.messages;
          }
        })
        .catch((err) => {
          console.log(err);
          Alert(ERROR, ERROR_API_MESSAGE);
        });
    });

    Promise.all(apiPromises)
      .then(() => {
        if (countSuccess === listChecked?.length) {
          setForm({
            agentName: null,
            designation: null,
          });
          onClose();
          setSaveAssign(true);
          Alert("SUCCESSFUL", "Bạn đã phân bổ Khách hàng thành công");
        } else {
          apiErrMsg && Alert("ERROR", apiErrMsg);
        }
      })
      .catch((err) => {
        console.log(err);
        Alert(ERROR, ERROR_API_MESSAGE);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <InfoAllocationCustomerModalWrapper>
      <RowItem className="mb-0">
        <LoadingPage loading={loading} />
        <DropDownFieldset
          required
          placeholder="Chức danh"
          value={form.designation}
          list={designationList}
          loading={getListActiveListLoading}
          onChange={(value) => setForm((pre: any) => ({ agentName: null, designation: value }))}
        />
        <DropDownFieldset
          required
          placeholder="Họ Tên TVTC"
          value={form.agentName}
          list={filterAgentList}
          loading={getListActiveListLoading}
          disabled={!form.designation?.value}
          onChange={(value) => setForm((pre: any) => ({ ...pre, agentName: value }))}
        />
      </RowItem>
      <h6 className="mt-4 mb-16 sm-mt-16">Danh sách khách hàng đã chọn</h6>
      <LeadsTable showOrderNo={true} showPagination={false} data={dataTable} config={configTable} />
      <ButtonFooterSticky>
        <ButtonPrimary disabled={!form.agentName?.value || !form.designation?.value} onClick={handleSave}>
          Lưu
        </ButtonPrimary>
      </ButtonFooterSticky>
    </InfoAllocationCustomerModalWrapper>
  );
};

export default InfoAllocationCustomerModal;
