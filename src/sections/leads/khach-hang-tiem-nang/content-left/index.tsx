import { ERROR, ERROR_API_MESSAGE } from "@constants/message";
import { ValueLabel } from "@custom-types";
import { LeadsGetAllActivityStatusOutput, LeadsSearchOutPutAndUpdate, PaginationPropCus } from "@custom-types/leads";
import { getLeadsAllActivityStatus, getLeadsSearch } from "api/leads";
import { Alert } from "components/alert";
import Icons from "components/icons";
import LoadingSection from "components/loading";
import ModalFullPage from "components/modal-full-page";
import TabButton from "components/tab-button";
import useActionApi from "hooks/use-action-api";
import useDebounce from "hooks/use-debounce";
import { useAppSelector } from "hooks/use-redux";
import useRoleLeads from "hooks/use-role-leads";
import moment from "moment";
import { useRouter } from "next/router";
import { useEffect, useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { setModalLeads } from "screens/leads/slice";
import NewCustomerCreate from "sections/leads/khach-hang-tiem-nang/create-modal";
import { convertLayerToStatusCode, getLayerLeads } from "services/leads";
import { formatNumberTwoString, getDateFrom, getDateTo } from "services/untils";
import { FlexBox, StickyWrapper } from "styles";
import { ButtonIcon, ButtonPrimary } from "styles/buttons";
import LeadsRecommend from "../../components/recommend";
import SearchBox from "../../components/search-box";
import SearchNotFound from "../../components/search-not-found";
import FilterView from "./filter";
import CustomerItem from "./item";
import { ButtonStickyWrapper, CustomerItemList, LeadsOverviewContentLeftWrapper, LeadsWrapper } from "./styled";
import { listProductCare } from "screens/leads/constants";

type TeamLeadsType = "customer-today" | "customer-list";

const tabList: ValueLabel[] = [
  { value: "customer-today", label: "KH hôm nay" },
  { value: "customer-list", label: "Danh sách KH" },
];

interface LeadsOverviewContentLeftProps {}

const LeadsOverviewContentLeft = ({}: LeadsOverviewContentLeftProps) => {
  //hide UI DC Thang 9
  let showSectionActivities = false;

  const router = useRouter();
  const role = useRoleLeads();
  const dispatch = useDispatch();

  const initLengthLeads = 10;
  const initStepLeads = 10;

  const isTele = role == "telesale" || role == "telesaleManager";

  //handle infinity list LEADS
  const refInfinityLeads = useRef<HTMLDivElement>();
  const refLeads = useRef<HTMLDivElement>();
  const countLoadingMore = useRef<number>(initLengthLeads);

  //handle page
  const [loading, setLoading] = useState(false);
  const [loadMore, setLoadMore] = useState(false);
  const [searchText, setSearchText] = useState<string>();
  const [customers, setCustomers] = useState<PaginationPropCus>();
  const [typeTab, setTypeTab] = useState<TeamLeadsType>("customer-today");
  const [showCreateCustomerModal, setShowCreateCustomerModal] = useState(false);
  const [listStatusAll, setListStatusAll] = useState<LeadsGetAllActivityStatusOutput[]>();

  const {
    user: { username },
    loading: { getLeadsAllActivityStatusLoading },
  } = useAppSelector((state) => state.rootReducer);
  const filterData = useAppSelector((state) => state.leadsReducer.filterData);

  const actionGetLeadsSearch = useActionApi(getLeadsSearch);
  const actionGetLeadsAllActivityStatus = useActionApi(getLeadsAllActivityStatus);

  const debounceSearchText = useDebounce(searchText, 500);

  useEffect(() => {
    setLoading(true);
    if (username && role) {
      actionGetLeadsAllActivityStatus({
        body: {
          types: isTele ? "CALL" : "APPOINTMENT",
        },
        loading: {
          type: "local",
          name: "",
        },
      })
        .then(({ data }) => {
          setListStatusAll(data);
        })
        .catch(() => {
          setLoading(false);
          Alert(ERROR, ERROR_API_MESSAGE);
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [username, isTele]);

  const fetchDataLeads = (size: number) => {
    actionGetLeadsSearch({
      body: {
        text: debounceSearchText ? `%${debounceSearchText}%` : undefined, //search Text
        createdBy: null, //khi filter vào khách hàng của tôi thì mới truyền username vào
        partner: filterData.partner?.length ? filterData.partner : null,
        status: filterData?.layers?.length
          ? convertLayerToStatusCode(filterData.layers, listStatusAll, role)
          : listStatusAll.map((item) => item.code),
        page: 0,
        size: size,
        fromCreatedDate: filterData.fromCreatedDate ?? getDateFrom(),
        toCreatedDate: filterData.toCreatedDate ?? getDateTo(),
        sortFields: ["createdDate desc"],
      },
      loading: {
        type: "local",
        name: "getLeadsAllActivityStatusLoading",
      },
    })
      .then(({ data }) => {
        setCustomers(data);
        setLoading(false);
      })
      .catch((error) => {
        setLoading(false);
        Alert(ERROR, ERROR_API_MESSAGE);
      });
  };

  useEffect(() => {
    if (listStatusAll || debounceSearchText) {
      setLoading(true);

      //set size Leads when change tab
      countLoadingMore.current = initLengthLeads;
      fetchDataLeads(countLoadingMore.current);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [listStatusAll, typeTab, debounceSearchText, filterData]);

  const listCustomer: LeadsSearchOutPutAndUpdate[] = useMemo(() => {
    if (customers) {
      const lists: any = customers.content
        .filter((item: any) => {
          //filter list with typeTab
          return typeTab == "customer-today"
            ? moment(item.createdDate, "DD/MM/YYYY").isSame(moment().startOf("d"))
            : item;
        })
        .map((item: any) => ({
          ...item,
          listStatus: getLayerLeads(item.status, listStatusAll, role),
          policySuggestion: listProductCare.find((i) => i.value == item?.policySuggestion)?.label,
        }));

      return lists;
    }
  }, [customers, typeTab, listStatusAll, role]);

  const filterListCustomer: LeadsSearchOutPutAndUpdate[] = useMemo(() => {
    if (listCustomer) {
      return listCustomer.filter((item) => item.fullname?.includes(searchText ?? ""));
    }
  }, [searchText, listCustomer]);

  //handle reloading page and open modal being display in screen
  useEffect(() => {
    if (router.query?.seq && listCustomer) {
      let item = listCustomer?.find((item) => item.seq.toString() == router.query?.seq);
      setTypeTab(router.query.typeTab as TeamLeadsType);

      if (item)
        dispatch(
          setModalLeads({
            modalType: "full",
            type: "agent-profile",
            title: "Thông tin chi tiết",
            data: item,
          })
        );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [listCustomer, router.query?.seq]);

  const handleClickCustomer = (item: LeadsSearchOutPutAndUpdate) => {
    router.push({
      pathname: "",
      query: { seq: item.seq, typeTab: typeTab },
    });

    dispatch(
      setModalLeads({
        modalType: "full",
        type: "agent-profile",
        title: "Thông tin chi tiết",
        data: item,
      })
    );
  };

  //start handle infinity list LEADS
  const handleScroll = () => {
    if (
      refInfinityLeads.current.scrollTop + window.innerHeight > refInfinityLeads.current.scrollHeight - 10 &&
      filterListCustomer.length
    ) {
      countLoadingMore.current = countLoadingMore.current + initStepLeads;
      setLoadMore(true);
    } else {
      setLoadMore(false);
      return;
    }
  };

  //handle if block leads of height => auto load leads more less screen laptop or mac
  // useEffect(() => {
  //   if (window.innerHeight >= refLeads.current.clientHeight && filterListCustomer?.length) {
  //     countLoadingMore.current = countLoadingMore.current + initStepLeads;
  //     fetchDataLeads(countLoadingMore.current);
  //     setLoadMore(true);
  //   } else {
  //     setLoadMore(false);
  //     return;
  //   }
  // }, [loadMore, refLeads.current?.clientHeight, window?.innerHeight]);

  useEffect(() => {
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (countLoadingMore.current > 0 && loadMore && customers?.size <= customers?.totalElements) {
      fetchDataLeads(countLoadingMore.current);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loadMore]);
  //end handle infinity list LEADS

  const renderListCustomer = () => {
    if (loading) {
      return (
        <div className="mt-16">
          <LoadingSection loading={loading} />
        </div>
      );
    }

    if (searchText?.length && !filterListCustomer?.length) {
      return <SearchNotFound />;
    } else if (!filterListCustomer?.length && !searchText?.length) {
      return <LeadsRecommend setType={() => setTypeTab("customer-list")} />;
    } else {
      return (
        <CustomerItemList>
          {filterListCustomer?.map((item, index) => (
            <CustomerItem item={item} key={index} onClick={() => handleClickCustomer(item)} />
          ))}

          <div className="mt-16" style={{ height: "30px" }}>
            <LoadingSection loading={Boolean(getLeadsAllActivityStatusLoading)} />
          </div>
        </CustomerItemList>
      );
    }
  };

  return (
    <LeadsOverviewContentLeftWrapper>
      <LeadsWrapper ref={refInfinityLeads} onScroll={handleScroll}>
        {/* <SmartLinkBlock>
          <h6 className="h7 text-white">Công cụ hỗ trợ TVTC</h6>
          <ProjectLinkBlock
            onClick={() => {
              window.location.href = `${process.env.basePath}/smart2/leads`;
            }}
          >
            <FlexBox gap={8} alignItem="center">
              <Icons icon="project-smart" />
              <h6 className="h7">Bán mới</h6>
            </FlexBox>
            <Icons icon="arrow-right" />
          </ProjectLinkBlock>
        </SmartLinkBlock> */}
        <SearchBox title="Khách hàng tiềm năng" searchText={searchText} onSearchText={setSearchText} />

        <StickyWrapper ref={refLeads} className="mt-24 sm-mt-16" top={24}>
          <TabButton size="full" list={tabList} active={typeTab} onChange={(tab: any) => setTypeTab(tab)} />
          {searchText ? (
            <h6 className="mt-16 mb-16">Kết quả tìm kiếm ({filterListCustomer?.length})</h6>
          ) : (
            <>
              <div>
                <FlexBox justifyContent="space-between" alignItem="center" className="pt-16">
                  <FlexBox alignItem="center">
                    <label className="label-4 mr-4" htmlFor="">
                      Sắp xếp theo <span className="color-primary bold">Mới nhất</span>
                    </label>
                    <Icons icon="icon-up" />
                  </FlexBox>

                  <ButtonIcon>
                    <FilterView />
                  </ButtonIcon>
                </FlexBox>

                <h6 className="mt-12 mb-16">
                  Danh sách khách hàng (
                  {loading
                    ? "00"
                    : typeTab == "customer-today"
                    ? formatNumberTwoString(filterListCustomer?.length)
                    : formatNumberTwoString(customers?.totalElements)}
                  )
                </h6>
              </div>
            </>
          )}

          {renderListCustomer()}

          {showSectionActivities && (
            <>
              {(searchText && !filterListCustomer?.length) || (!filterListCustomer?.length && !searchText) ? null : (
                <ButtonStickyWrapper>
                  <ButtonPrimary onClick={() => setShowCreateCustomerModal(true)}>
                    <Icons icon="plus" />
                    Tạo mới KH
                  </ButtonPrimary>
                </ButtonStickyWrapper>
              )}
            </>
          )}
        </StickyWrapper>
      </LeadsWrapper>
      <ModalFullPage
        show={showCreateCustomerModal}
        title="Thêm mới khách hàng tiềm năng"
        onClose={() => setShowCreateCustomerModal(false)}
      >
        <NewCustomerCreate />
      </ModalFullPage>
    </LeadsOverviewContentLeftWrapper>
  );
};

export default LeadsOverviewContentLeft;
