import { DatePickerAbsoluteWrapper } from "components/input-date/styled";
import styled from "styled-components";
import { device } from "styles/media";

export const FilterViewWrapper = styled.div``;

export const ListsWrapper = styled.div`
  border-top: 1px solid #dbdfe1;

  display: block;
  margin-bottom: 16px;
  padding-top: 16px;
`;
export const ListItemWrapper = styled.ul`
  padding: 0;
  margin-bottom: 24px;
`;

export const ButtonWrapper = styled.div`
  display: flex;
  justify-content: center;

  button {
    width: 100%;
  }
`;

export const ItemViewWrapper = styled.li`
  display: flex;
  align-items: center;
  margin-top: 16px;

  :not(:last-child) {
    margin-bottom: 16px;
  }
`;

export const Card = styled.div`
  width: 100%;
  padding: 16px;

  display: flex;

  border-radius: 12px;
  border: 1px solid ${({ theme }) => theme.color.status.grey};

  svg {
    margin-right: 8px;
  }
`;
export const ModalContentWrapper = styled.div`
  padding: 0 16px 16px 16px;

  label {
    display: block;
    color: ${({ theme }) => theme.color.status.grey_darkest};
  }

  @media ${device.mobile} {
    margin-bottom: 120px;
  }
`;

export const SearchDateWrapper = styled.div`
  svg {
    width: 30px;
  }
`;
export const DateInputWrapper = styled.div`
  .${DatePickerAbsoluteWrapper.styledComponentId} {
    right: unset;
    left: 0;
  }

  @media ${device.mobile} {
    .${DatePickerAbsoluteWrapper.styledComponentId} {
      right: 0;
    }
  }
`;
