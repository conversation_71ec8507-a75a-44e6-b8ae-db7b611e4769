import { ERROR, ERROR_API_MESSAGE } from "@constants/message";
import { getAllSource } from "api/leads";
import { Alert } from "components/alert";
import CheckBox from "components/checkbox";
import Icons from "components/icons";
import DateInput from "components/input-date";
import ModalLeft from "components/modal-left";
import useActionApi from "hooks/use-action-api";
import useRoleLeads from "hooks/use-role-leads";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import { setFilterLeads } from "screens/leads/slice";
import { handleOnChangeCheckValidateLeads, listLayerAgent, listLayerTele } from "services/leads";
import { LinkProps } from "services/menu-link";
import { getDateFrom, getDateTo } from "services/untils";
import { ButtonFooterSticky, RowItem } from "styles";
import { ButtonIcon, ButtonPrimary, ButtonSecondary } from "styles/buttons";
import {
  But<PERSON><PERSON>rap<PERSON>,
  Card,
  DateInputWrapper,
  FilterViewWrapper,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>rap<PERSON>,
  ListItemWrapper,
  ListsWrapper,
  Mo<PERSON><PERSON><PERSON>ntWrapper,
  SearchDateWrapper,
} from "./styled";

interface ListFilter {
  key: string;
  label: string;
  children: LinkProps[];
}

const FilterView = () => {
  const role = useRoleLeads();
  const dispatch = useDispatch();
  const [open, setOpen] = useState(false);
  const [dataCheck, setDataCheck] = useState<any>({ partner: [], status: [] });
  const [sources, setSources] = useState([]);
  const [form, setForm] = useState({
    fromCreatedDate: getDateFrom(),
    toCreatedDate: getDateTo(),
  });
  const [error, setError] = useState({
    fromCreatedDate: null,
    toCreatedDate: null,
  });

  const actionGetAllSource = useActionApi(getAllSource);

  const isTele = role == "telesale" || role == "telesaleManager";

  const list: ListFilter[] = useMemo(
    () => [
      {
        key: "partner",
        label: "Nguồn",
        children: sources,
      },
      {
        key: "status",
        label: "Trạng thái",
        children: isTele ? listLayerTele : listLayerAgent,
      },
    ],
    [role, sources]
  );

  useEffect(() => {
    actionGetAllSource({
      body: {
        seq: null,
        text: null,
        createdBy: null,
        partner: [],
        status: [],
        page: 0,
        size: 10,
        fromCreatedDate: null,
        toCreatedDate: null,
        sortFields: ["seq desc", "createdDate desc", "fullname asc"],
      },
      loading: {
        type: "local",
        name: "getLeadsAllActivityStatusLoading",
      },
    })
      .then(({ data }) => {
        // [CL-92]: dynamic source in filter
        setSources(
          data.map((value) => (value == "import_tool" ? { value, label: "Hệ thống" } : { value, label: value }))
        );
      })
      .catch((error) => {
        Alert(ERROR, ERROR_API_MESSAGE);
      });
  }, []);

  const handleMultipleSelect = useCallback(
    (checkData: any) => {
      const parent = list.find((item) => item.children.find((d) => d.value == checkData.value));

      setDataCheck((pre: any) => {
        return {
          ...pre,
          [parent?.key]: pre[parent.key].includes(checkData.value)
            ? pre[parent.key].filter((item: any) => item != checkData.value)
            : [...pre[parent.key], checkData.value],
        };
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dataCheck, list]
  );

  const handleChangeData = (name: string, value: string) => {
    setForm((pre) => ({
      ...pre,
      [name]: value,
    }));

    if (name === "fromCreatedDate" || name === "toCreatedDate") {
      const newError = handleOnChangeCheckValidateLeads(name, value, form.fromCreatedDate, 3);
      setError((pre) => ({ ...pre, ...newError }));
    }
  };

  const disableSave = useMemo(() => {
    return Object.values(error)?.findIndex((value) => value) > -1;
  }, [error]);

  const handleBlurData = (name: string, valueDateFrom: string) => {
    const newError = handleOnChangeCheckValidateLeads(name, form.toCreatedDate, valueDateFrom, 3);
    setError((pre) => ({ ...pre, ...newError }));
  };

  const handleSaveFilterView = useCallback(() => {
    dispatch(
      setFilterLeads({
        partner: dataCheck.partner,
        layers: dataCheck.status,
        fromCreatedDate: form.fromCreatedDate,
        toCreatedDate: form.toCreatedDate,
      })
    );
    setOpen(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataCheck, open]);

  const handleReset = useCallback(() => {
    setDataCheck({ partner: [], status: [] });
    setForm({ fromCreatedDate: getDateFrom(), toCreatedDate: getDateTo() });
  }, []);

  return (
    <FilterViewWrapper>
      <ButtonIcon onClick={() => setOpen(true)}>
        <Icons icon="icon-filter" />
      </ButtonIcon>

      <ModalLeft title="Filter" show={open} onClose={() => setOpen(false)}>
        <ModalContentWrapper>
          <SearchDateWrapper>
            <label className="label-3 mb-16">Ngày Tạo Khách Hàng</label>

            <RowItem>
              <DateInputWrapper>
                <DateInput
                  placeholder="Từ ngày"
                  value={form.fromCreatedDate}
                  error={error.fromCreatedDate}
                  onBlur={(value) => handleBlurData("toCreatedDate", value)}
                  onChange={(value) => handleChangeData("fromCreatedDate", value)}
                />
              </DateInputWrapper>
              <div>
                <DateInput
                  placeholder="Đến ngày"
                  value={form.toCreatedDate}
                  error={error.toCreatedDate}
                  onChange={(value) => handleChangeData("toCreatedDate", value)}
                />
              </div>
            </RowItem>
          </SearchDateWrapper>
          <ListItemWrapper>
            {list.map((item) => {
              return (
                <ListsWrapper key={item.label}>
                  <label className="label-3">{item.label}</label>
                  {item.children.map((data: any) => (
                    <ItemViewWrapper key={data.value}>
                      <Card>
                        <CheckBox
                          checked={dataCheck[item.key]?.includes(data.value)} //if have key in dataCheck => show Check box  dataCheck[data.value]
                          onChange={() => handleMultipleSelect(data)}
                          label={data.label}
                        />
                      </Card>
                    </ItemViewWrapper>
                  ))}
                </ListsWrapper>
              );
            })}
          </ListItemWrapper>
        </ModalContentWrapper>

        <ButtonFooterSticky className="ml-16 mr-16 sm-ml-0 mb-16 sm-mb-0">
          <ButtonPrimary disabled={disableSave} size="small" maxWidth long onClick={handleSaveFilterView}>
            Lưu
          </ButtonPrimary>

          <ButtonWrapper className="mt-16">
            <ButtonSecondary size="small" maxWidth long onClick={handleReset}>
              Thiết lập lại
            </ButtonSecondary>
          </ButtonWrapper>
        </ButtonFooterSticky>
      </ModalLeft>
    </FilterViewWrapper>
  );
};

export default FilterView;
