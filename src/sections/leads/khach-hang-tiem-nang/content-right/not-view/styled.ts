import styled from "styled-components";
import { device } from "styles/media";

export const NotViewWrapper = styled.div`
  width: 60%;
  padding: 24px;
  background: #fef9f4;

  div {
    height: 100%;

    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
  }

  img {
    :first-child {
      margin-bottom: -120px;
    }
  }

  p,
  h4 {
    max-width: 450px;
    text-align: center;
  }

  @media ${device.mobile} {
    width: 100%;
    min-height: calc(100 * var(--vh) - 100px);

    display: flex;
    align-items: center;
    justify-content: center;

    h4 {
      font-size: 25px;
    }

    img {
      :first-child {
        margin-bottom: -80px;
      }
      :last-child {
        width: 183px;
      }
    }
  }
`;
