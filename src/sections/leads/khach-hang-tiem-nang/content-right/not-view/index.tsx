import React from "react";
import { NotViewWrapper } from "./styled";

interface NotViewProps {}

const NotView = ({}: NotViewProps) => {
  return (
    <NotViewWrapper>
      <div>
        <img src={`${process.env.basePath}/img/leads/city.svg`} alt="bg" />
        <img src={`${process.env.basePath}/img/leads/human.svg`} width={200} alt="bg" />

        <h4 className="color-primary mt-40 mb-12">Sống đầy từ hôm nay!</h4>

        {/* <p className="body-4">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Praesent vitae nibh a nibh venenatis malesuada
        </p> */}
      </div>
    </NotViewWrapper>
  );
};

export default NotView;
