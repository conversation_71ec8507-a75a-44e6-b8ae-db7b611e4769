import { DataActivity, TabType, TargetBoxItemProps } from "@custom-types/leads";
import moment from "moment";
import { useEffect, useMemo, useState } from "react";
import CustomerActivity from "sections/leads/components/customer-activity";
import { AgentTargetDetailWrapper } from "./styles";

interface AgentTargetDetailProps {
  data?: any;
}

const AgentTargetDetail = ({ data }: AgentTargetDetailProps) => {
  const [modifiedData, setModifiedData] = useState<TargetBoxItemProps[]>([
    {
      contact: null,
      appointed: null,
      solution: null,
      submit: null,
    },
  ]);

  const [typeTab, setTypeTab] = useState<TabType>("weekly");

  const dataActivity: DataActivity = useMemo(() => {
    switch (typeTab) {
      case "weekly":
        return {
          titleReport: "Báo cáo chốt hợp đồng tuần này",
          dateReport: moment().subtract(1, "day").format("DD/MM/YYYY"),
          titleTarget: "<PERSON><PERSON>c tiêu so với <PERSON>ố khách hàng tiềm năng thực tế tuần này",
          dateTarget: moment().subtract(3, "day").format("DD/MM/YYYY"),
          textTarget: "Mục tiêu hàng tuần",
          dataTargetChart: [23, 45, 78, 23],
          dataReportChart: {
            totalCus: 100,
            contacted: 58,
            appointed: 35,
            solution: 18,
            submitted: 12,
          },
        };
      case "monthly":
        return {
          titleReport: "Báo cáo chốt hợp đồng trong tháng này",
          dateReport: moment().subtract(5, "day").format("DD/MM/YYYY"),
          titleTarget: "Mục tiêu so với Số khách hàng tiềm năng thực tế tháng này",
          dateTarget: moment().subtract(9, "day").format("DD/MM/YYYY"),
          textTarget: "Mục tiêu hàng tháng",
          dataTargetChart: [34, 80, 20, 54],
          dataReportChart: {
            totalCus: 500,
            contacted: 390,
            appointed: 175,
            solution: 90,
            submitted: 60,
          },
        };
    }
  }, [typeTab]);

  useEffect(() => {
    //call api render data
    if (typeTab === "weekly") {
      setModifiedData([
        {
          contact: 12,
          appointed: 22,
          solution: 34,
          submit: 21,
        },
      ]);
    } else {
      setModifiedData([
        {
          contact: 12,
          appointed: 22,
          solution: 34,
          submit: 21,
        },
        {
          contact: 12,
          appointed: 22,
          solution: 34,
          submit: 21,
        },
        {
          contact: 12,
          appointed: 22,
          solution: 34,
          submit: 21,
        },
        {
          contact: 12,
          appointed: 22,
          solution: 34,
          submit: 21,
        },
      ]);
    }
  }, [typeTab]);

  return (
    <AgentTargetDetailWrapper>
      <CustomerActivity
        typeTab={typeTab}
        setTypeTab={setTypeTab}
        modifiedData={modifiedData}
        dataActivity={dataActivity}
      />
    </AgentTargetDetailWrapper>
  );
};

export default AgentTargetDetail;
