import { DataActivity } from "@custom-types/leads";
import PyramidChart from "sections/leads/components/chart-report";
import TargetChart from "sections/leads/components/chart-target";
import LeadsRightBlock from "sections/leads/components/leads-right-block";
import { ManagerCustomerPotentialWrapper } from "./styled";

interface LeadsOverviewContentRightProps {
  data?: DataActivity;
}

const ManagerCustomerPotentialLeft = ({ data }: LeadsOverviewContentRightProps) => {
  return (
    <ManagerCustomerPotentialWrapper>
      <LeadsRightBlock bg="#ffffff" title={data.titleReport} date={data.dateReport}>
        <PyramidChart data={data.dataReportChart} />
      </LeadsRightBlock>

      <LeadsRightBlock bg="#ffffff" title={data.titleTarget} date={data.dateTarget} text={data.textTarget}>
        <TargetChart data={data.dataTargetChart} />
      </LeadsRightBlock>
    </ManagerCustomerPotentialWrapper>
  );
};

export default ManagerCustomerPotentialLeft;
