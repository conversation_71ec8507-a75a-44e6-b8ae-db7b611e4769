import Icons from "components/icons";
import { useState } from "react";
import { FlexBox } from "styles";
import { ButtonIcon } from "styles/buttons";
import { SearchWrapper, TitleSearch, InputSearch } from "./styled";

const SearchBox = ({
  title,
  searchText,
  onSearchText,
}: {
  title: string;
  searchText: string;
  onSearchText: (value: any) => void;
}) => {
  const [openQuickSearch, setOpenQuickSearch] = useState<boolean>(false);

  const handleQuickSearch = (a: any) => {
    setOpenQuickSearch(true);
  };

  const handleBack = () => {
    setOpenQuickSearch(false);
    onSearchText("");
  };

  return (
    <FlexBox style={{ position: "relative", height: "40px" }} justifyContent="space-between" alignItem="center">
      {openQuickSearch ? (
        <ButtonIcon onClick={handleBack}>
          <Icons icon="search-back" />
        </ButtonIcon>
      ) : (
        <TitleSearch>{title}</TitleSearch>
      )}

      <SearchWrapper active={openQuickSearch} onClick={() => handleQuickSearch(true)}>
        <InputSearch
          name="input-search"
          autoComplete="off"
          id="input-search"
          type="text"
          active={openQuickSearch}
          value={searchText}
          onChange={(e) => onSearchText(e.target.value)}
        />
        {!openQuickSearch ? <Icons icon="search-policy" /> : null}
      </SearchWrapper>
    </FlexBox>
  );
};

export default SearchBox;
