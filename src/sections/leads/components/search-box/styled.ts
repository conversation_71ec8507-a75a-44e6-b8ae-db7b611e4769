import styled from "styled-components";

export const TitleSearch = styled.h6`
  white-space: pre-line;
`;

export const SearchWrapper = styled.div<{ active: boolean }>`
  width: ${({ active }) => (active ? "calc(100% - 40px)" : "40px")};
  height: 40px;

  position: absolute;
  top: 0px;
  right: 0px;

  display: flex;
  justify-content: center;
  align-items: center;

  border-radius: 52px;
  border: 2px solid ${({ theme }) => theme.color.status.primary};
  background: #ffffff;

  transition: 0.3s all ease-in-out;

  svg {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    pointer-events: none;
  }
`;

export const InputSearch = styled.input<{ active: boolean }>`
  width: ${({ active }) => (active ? 100 : 0)}%;

  padding: 8px 16px;

  :-webkit-autofill {
    -webkit-box-shadow: 0 0 0px 1000px #fff inset;
    box-shadow: 0 0 0px 1000px #fff inset;

    height: 36px;
    border-radius: 999px;
    width: 100%;
    background: #ffffff;
  }
`;
