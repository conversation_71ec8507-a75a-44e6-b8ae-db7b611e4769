import { ValueLabel } from "@custom-types";
import { DataActivity, TabType, TargetBoxItemProps } from "@custom-types/leads";
import ScrollBar from "components/scroll-bar";
import TabButton from "components/tab-button";
import { useMemo, useState } from "react";
import { TitlePage } from "styles";

import { CustomerActivityWrapper, LeadsRightWrapper } from "./styled";
import Weekly from "./weekly";
import MonthLy from "./monthly";
import LeadsRightBlock from "../leads-right-block";
import PyramidChart from "../chart-report";
import TargetChart from "../chart-target";
import Modal from "components/modal";
import TargetModal from "sections/leads/khach-hang-tiem-nang/content-right/target-modal";

interface CustomerActivityProps {
  title?: string;
  typeTab: TabType;
  dataActivity: DataActivity;
  modifiedData: TargetBoxItemProps[];
  setTypeTab: (a: TabType) => void;
}

const tabList: ValueLabel[] = [
  { value: "weekly", label: "Theo tuần" },
  { value: "monthly", label: "Theo tháng" },
];

const CustomerActivity = ({ title, typeTab, dataActivity, modifiedData, setTypeTab }: CustomerActivityProps) => {
  const [showModalTarget, setShowModalTarget] = useState(false);

  const Content = useMemo(() => {
    switch (typeTab) {
      case "weekly":
        return Weekly;
      case "monthly":
        return MonthLy;
    }
  }, [typeTab]);

  return (
    <CustomerActivityWrapper>
      <ScrollBar>
        <LeadsRightWrapper>
          {title && <TitlePage className="mb-24 sm-mb-16">{title}</TitlePage>}

          <TabButton size="full" list={tabList} active={typeTab} onChange={(tab: any) => setTypeTab(tab)} />

          <LeadsRightBlock title={dataActivity.titleReport} date={dataActivity.dateReport}>
            <PyramidChart data={dataActivity.dataReportChart} />
          </LeadsRightBlock>

          <LeadsRightBlock
            title={dataActivity.titleTarget}
            date={dataActivity.dateTarget}
            text={dataActivity.textTarget}
          >
            <TargetChart data={dataActivity.dataTargetChart} />
          </LeadsRightBlock>

          {Content ? <Content setShowModal={setShowModalTarget} modifiedData={modifiedData} /> : null}
        </LeadsRightWrapper>
      </ScrollBar>

      <Modal
        size="lg"
        borderHeader
        isFooterSticky
        backgroundMobile="#ffffff"
        title={typeTab == "weekly" ? "Mục tiêu trong tuần" : "Mục tiêu trong tháng"}
        show={showModalTarget}
        onClose={() => setShowModalTarget(false)}
      >
        <TargetModal data={modifiedData} />
      </Modal>
    </CustomerActivityWrapper>
  );
};

export default CustomerActivity;
