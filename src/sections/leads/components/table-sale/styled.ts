import styled from "styled-components";
import { device } from "styles/media";

export const TableRankPerformance = styled.table`
  width: 100%;

  border-spacing: 0;
  border-collapse: separate;
`;
export const THEADRank = styled.thead`
  tr {
    text-align: right;

    th {
      :first-child {
        text-align: left;
      }
    }
  }
`;
export const TBODYRank = styled.tbody`
  tr {
    text-align: right;

    :last-child {
      td {
        border: none;
      }
    }
  }
`;

export const TRRank = styled.tr``;

export const THRank = styled.th`
  :last-child {
    padding-left: 16px;
    padding-right: 42px;
    text-align: right;

    h6 {
      white-space: nowrap;
    }
  }

  @media ${device.mobile} {
    h6 {
      font-size: 14px;
      white-space: nowrap;
    }
  }
`;

export const TDRank = styled.td`
  position: relative;
  padding: 8px 0px;
  min-width: 80px;

  font-weight: 400;
  font-size: 16px;
  line-height: 150%;

  border-bottom: 1px solid ${({ theme }) => theme.color.status.grey};
  cursor: pointer;

  :first-child {
    text-align: left;
  }

  :not(:first-child) {
    text-align: right;
    font-weight: 700;
  }

  :last-child {
    padding-right: 42px;
  }

  svg {
    top: 25%;
    right: 5px;

    position: absolute;
  }

  @media ${device.mobile} {
    padding: 16px 0px;
    font-size: 14px;
    min-width: 45px;

    svg {
      top: 32%;
    }
  }

  @media (max-width: 400px) {
    svg {
      top: 37%;
    }
  }
`;

export const SquareColor = styled.div<{ background: string }>`
  height: 16px;
  width: 16px;
  margin-right: 8px;

  display: block;
  background: ${({ background }) => background};

  border-radius: 2px;
`;
