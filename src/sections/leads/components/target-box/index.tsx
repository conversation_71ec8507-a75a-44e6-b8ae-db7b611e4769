import { TargetBoxItemProps } from "@custom-types/leads";
import { formatNumber } from "services/untils";
import { FlexBox, Spacing } from "styles";
import { TargetBoxItem, TargetBoxWrapper } from "./styled";
import { NumericFormat } from "react-number-format";

interface TargetBoxProps {
  data: any;
  date: string;
  isEdit?: boolean;
  index?: number;
  onChange?: (index: number, name: keyof TargetBoxItemProps, value: any) => void;
}

const TargetBox = ({ index, date, data, isEdit = false, onChange }: TargetBoxProps) => {
  const configTargetBox = [
    {
      key: "contact",
      title: "<PERSON><PERSON><PERSON> hệ",
    },
    {
      key: "appointed",
      title: "Cuộc hẹn",
    },
    {
      key: "solution",
      title: "Trình bày giải pháp",
    },
    {
      key: "submit",
      title: "Nộp",
    },
  ];

  return (
    <TargetBoxWrapper>
      <p className="body-5">{date}</p>

      <FlexBox justifyContent="space-between" className="mt-16 sm-mt-12">
        {configTargetBox.map((item: any) => (
          <TargetBoxItem key={item.key}>
            <p className="label-6 mb-8">{item.title}</p>
            <Spacing />

            {isEdit ? (
              <NumericFormat value={data[item.key] ?? 0} onValueChange={(e) => onChange(index, item.key, e.value)} />
            ) : (
              <p className="label-3">{formatNumber(data[item.key])}</p>
            )}
          </TargetBoxItem>
        ))}
      </FlexBox>
    </TargetBoxWrapper>
  );
};

export default TargetBox;
