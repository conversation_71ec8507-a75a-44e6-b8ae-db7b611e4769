import styled from "styled-components";
import { device } from "styles/media";

export const TargetBoxWrapper = styled.div`
  :not(:first-child) {
    margin-top: 16px;
  }

  p {
    margin: 0 !important;
  }

  input {
    text-align: center;

    :focus {
      caret-color: ${({ theme }) => theme.color.status.primary};
    }
  }

  div:has(input) {
    div:focus-within {
      border: 1px solid ${({ theme }) => theme.color.status.primary};
    }
  }

  @media ${device.mobile} {
    :not(:first-child) {
      margin-top: 12px;
    }
  }
`;

export const TargetBoxItem = styled.div`
  display: flex;
  flex-direction: column;

  padding: 10px 14px;
  border-radius: 12px;
  width: calc((100% / 4) - (12px));

  border: 1px solid ${({ theme }) => theme.color.status.grey};
  background: ${({ theme }) => theme.color.status.grey_20};

  p {
    text-align: center;
    margin: 0 !important;

    :first-child {
      margin-bottom: 8px !important;
    }
  }

  :has(input) {
    :hover {
      border: 1px solid ${({ theme }) => theme.color.status.primary};
      transition: 0.3s all ease-in-out;
    }
  }

  @media ${device.mobile} {
    width: calc((100% / 4) - (8px));

    padding: 10px 8px;
  }
`;
