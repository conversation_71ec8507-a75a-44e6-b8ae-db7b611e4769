import { formatNumber } from "services/untils";
import { ContentTargetGroup, TargetItemGroup } from "./styled";

export interface ITargetUserDetailProps {
  fyp: number;
  ape: number;
  cases: number;
  subTarget?: boolean;
}

const TargetUser = ({ fyp, ape, cases, subTarget }: ITargetUserDetailProps) => {
  return (
    <ContentTargetGroup subTarget={subTarget}>
      <TargetItemGroup>
        <p className="body-4">FYP</p>
        <h5>{formatNumber(fyp)}</h5>
      </TargetItemGroup>
      <TargetItemGroup>
        <p className="body-4">APE</p>
        <h5>{formatNumber(ape)}</h5>
      </TargetItemGroup>
      <TargetItemGroup>
        <p className="body-4">Cases</p>
        <h5>{formatNumber(cases)}</h5>
      </TargetItemGroup>
    </ContentTargetGroup>
  );
};

export default TargetUser;
