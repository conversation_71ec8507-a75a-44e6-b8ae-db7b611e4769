import CardPerformance, { CardData } from "components/card";
import SalesPersonal from "sections/performance/components/sales-personal";
import { YearlyWrapper } from "./styled";

const Yearly = ({ data, dataChart }: { data: CardData[][][]; dataChart: any }) => {
  return (
    <YearlyWrapper>
      <CardPerformance data={data} />
      <SalesPersonal data={dataChart} typeBlock="" />
    </YearlyWrapper>
  );
};

export default Yearly;
