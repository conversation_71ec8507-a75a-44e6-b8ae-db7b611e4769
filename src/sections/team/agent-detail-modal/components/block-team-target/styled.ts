import styled from "styled-components";
import { ButtonSecondary } from "styles/buttons";
import { device } from "styles/media";

export const BlockWrapperTeamTarget = styled.div<{ background: string; subBlock?: boolean }>`
  width: 100%;
  padding: 16px;

  position: relative;

  background: ${({ theme, background }) => background ?? theme.color.status.primary_5};
  border-radius: 16px;

  .${ButtonSecondary.styledComponentId} {
    padding: 6px 11px;
    min-width: 90px;
    min-height: 32px;
  }

  span {
    color: ${({ theme }) => theme.color.status.primary};
  }

  @media ${device.mobile} {
    h6 {
      font-size: 16px;
    }
  }
`;

export const FooterBlock = styled.div`
  .${ButtonSecondary.styledComponentId} {
    width: 100%;
    padding: 10px 11px;

    margin-top: 16px;
  }

  button {
    @media ${device.mobile} {
      max-width: 100%;
    }
  }
`;
export const ButtonWrapper = styled.div``;
