import { ERROR_API_MESSAGE } from "@constants/message";
import { TableConfig } from "@custom-types/config-table";
import { LOSChartData, ListSAData, ReportSAInput, SAChartData } from "@custom-types/team";
import { getChartLosReport, getChartSA, getListSA } from "api/team";
import { Alert } from "components/alert";
import ChartSlide from "components/chart-slide";
import FormSearchTemplate from "components/form-search-template";
import MonthInput from "components/input-month";
import LoadingSection from "components/loading";
import Table from "components/table";
import TableMobile from "components/table-mobile";
import useActionApi from "hooks/use-action-api";
import { useAppSelector } from "hooks/use-redux";
import { isNull } from "lodash";
import moment from "moment";
import { useCallback, useEffect, useMemo, useState } from "react";
import { formatValueTable } from "services/format-value";
import { getDateFrom } from "services/untils";
import { NoData, RelativeWrapper, RowItem } from "styles";
import ChartAgentTotal from "../chart/chart-rank-agent";
import { ChartDesktopWrapper, ChartMobileWrapper } from "../chart/styled";
import { ListBlock, OverViewBlock, SaTeamWrapper, TitleBlock } from "./styled";

const config: TableConfig[] = [
  { label: "Miền", key: "regionCd", show: true },
  { label: "Văn phòng ", key: "branchId", show: true },
  { label: "Mã FWD", key: "fwdCode", show: true },
  { label: "Họ tên FWD", key: "fwdName", show: true },
  { label: "Mã FWM", key: "fwmCode", show: true },
  { label: "Họ tên FWM", key: "fwmName", show: true },
  { label: "Mã FWO", key: "fwoCode", show: true },
  { label: "Họ tên FWO", key: "fwoName", show: true },
  { label: "Mã quản lý <br/> trực tiếp", key: "directSupervisorCode", show: true },
  { label: "Tên quản lý <br/> trực tiếp", key: "directSupervisorName", show: true },
  { label: "Chức danh quản lý <br/> trực tiếp", key: "directSupervisorDesignationCd", show: true },
  { label: "Mã TVTC <br/> phục vụ", key: "agentId", show: true },
  { label: "Tên TVTC <br/> phục vụ", key: "agentName", show: true },
  { label: "Ngày gia nhập", key: "joiningDate", show: true, type: "date" },
  { label: "Số tháng <br/> phục vụ", key: "totalMonthFromJoiningDate", show: true, type: "number" },
  { label: "Ngày trở <br/> thành SA", key: "saChangeDate", show: true, type: "date" },
  { label: "Số tháng <br/> làm SA", key: "totalMonthFromSaChangeDate", show: true, type: "number" },
  { label: "Khóa EZI", key: "eziLock", show: true },
  { label: "Giới tính", key: "genderCd", show: true },
  { label: "Ngày sinh", key: "birthDate", show: true, type: "date" },
  { label: "Số điện thoại", key: "mobileNumber", show: true },
  { label: "Email", key: "email", show: true },
  { label: "Địa chỉ liên lạc", key: "dispatchAddress", show: true, type: "rich-text" },
  { label: "Địa chỉ thường trú", key: "permanentAddress", show: true, type: "rich-text" },
];

const formatValueTableSA = (data: ListSAData, config: TableConfig) => {
  switch (config.key) {
    case "genderCd":
      return data[config.key] === "F" ? "Nữ" : data[config.key] === "M" ? "Nam" : "-";
    case "eziLock":
      return data[config.key] === "Y" ? "Yes" : data[config.key] === "N" ? "No" : "-";
    default:
      return formatValueTable(data, config);
  }
};

const SaTeam = () => {
  const [month, setMonth] = useState(getDateFrom("MM/YYYY"));
  const [error, setError] = useState("");
  const [dataChartLos, setDataChartLos] = useState<LOSChartData>(null);
  const [dataChartSa, setDataChartSa] = useState<SAChartData>(null);
  const [tableSaList, setTableSaList] = useState<ListSAData[]>([]);

  const {
    user: { username, designation },
    loading: { getListSaLoading, getChartSaLoading },
  } = useAppSelector((state) => state.rootReducer);

  //api
  const actionGetChartLos = useActionApi<ReportSAInput, LOSChartData>(getChartLosReport);
  const actionGetChartSa = useActionApi<ReportSAInput, SAChartData>(getChartSA);
  const actionGetSAList = useActionApi<ReportSAInput, ListSAData[]>(getListSA);

  useEffect(() => {
    if (username) {
      const payload: ReportSAInput = {
        agentCode: username,
        designationCd: designation,
        month: moment().format("MM"),
        year: moment().format("YYYY"),
      };
      actionGetChartLos({
        body: payload,
        loading: {
          type: "local",
          name: "getChartSaLoading",
        },
      })
        .then(({ data }) => {
          setDataChartLos(data);
        })
        .catch((err) => {
          console.log(err);
          Alert("ERROR", ERROR_API_MESSAGE);
        });

      actionGetChartSa({
        body: payload,
        loading: {
          type: "local",
          name: "getChartSaLoading",
        },
      })
        .then(({ data }) => {
          setDataChartSa(data);
        })
        .catch((err) => {
          console.log(err);
          Alert("ERROR", ERROR_API_MESSAGE);
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [username, designation]);

  const dataTable = useMemo(() => {
    return tableSaList?.map((d: any) =>
      config.map((config) => ({
        config: config,
        node: formatValueTableSA(d, config),
        originData: d[config.key],
      }))
    );
  }, [tableSaList]);

  const legendListOverviewSA = useMemo(() => {
    return [
      { color: "#0097A9", label: "SA <=3 tháng", value: dataChartSa?.saMonthBetween0And3Month },
      { color: "#E87722", label: "3 tháng < SA <= 6 tháng", value: dataChartSa?.saMonthBetween3And6Month },
      { color: "#03824F", label: "6 tháng < SA <= 12 tháng", value: dataChartSa?.saMonthBetween6And12Month },
      { color: "#FED141", label: "SA > 12 tháng", value: dataChartSa?.saMonthGreaterThan12Month },
    ];
  }, [dataChartSa]);

  const legendListOverviewLos = useMemo(() => {
    return [
      { color: "#6ECEB2", label: "LOS ≤ 6 tháng", value: dataChartLos?.losBetween0And6Month },
      { color: "#B3B6B8", label: "6 tháng < LOS ≤ 12 tháng", value: dataChartLos?.losBetween6And12Month },
      { color: "#FEE8A0", label: "12 tháng < LOS ≤ 24 tháng", value: dataChartLos?.losBetween12And24Month },
      { color: "#7FCBD4", label: "LOS > 24 tháng", value: dataChartLos?.losGreaterThan24Month },
    ];
  }, [dataChartLos]);

  const handleChangeMonth = (value: string) => {
    setMonth(value);

    let error = "";

    if (value.length < 7) {
      error = "Vui lòng nhập đúng định dạng: mm/yyyy";
    } else {
      const checkFutureDate = moment(value, "MM/YYYY").isAfter(moment().startOf("month"));

      if (checkFutureDate) {
        error = "Vui lòng không chọn ngày tháng trong tương lai";
      } else {
        error = "";
      }
    }

    setError(error);
  };

  const handleSubmit = useCallback(() => {
    const payload: ReportSAInput = {
      agentCode: username,
      designationCd: designation,
      month: month.split("/")[0],
      year: month.split("/")[1],
    };
    actionGetSAList({
      body: payload,
      loading: {
        type: "local",
        name: "getListSaLoading",
      },
    })
      .then(({ data }) => {
        setTableSaList(data?.length ? data : null);
      })
      .catch((err) => {
        console.log(err);
        Alert("ERROR", ERROR_API_MESSAGE);
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [username, month, designation]);

  return (
    <SaTeamWrapper>
      <OverViewBlock>
        <TitleBlock>Tổng quan về Tư vấn phục vụ </TitleBlock>
        <RelativeWrapper>
          <ChartDesktopWrapper className="hide-mobile">
            <ChartAgentTotal
              legendList={legendListOverviewSA}
              total={dataChartSa?.saTotal}
              subTitle="Tổng quan số tháng <br/> làm SA trong nhóm"
            />
            <ChartAgentTotal
              legendList={legendListOverviewLos}
              total={dataChartLos?.agentTotal}
              subTitle="Tổng quan LOS <br/> trong nhóm"
              subLegendTitle="LOS: Length of service (thời gian làm việc)"
            />
          </ChartDesktopWrapper>
          <ChartMobileWrapper className="hide-desktop">
            <ChartSlide>
              <ChartAgentTotal
                legendList={legendListOverviewSA}
                total={dataChartSa?.saTotal}
                subTitle="Tổng quan số tháng <br/> làm SA trong nhóm"
              />
              <ChartAgentTotal
                legendList={legendListOverviewLos}
                total={dataChartLos?.agentTotal}
                subTitle="Tổng quan LOS <br/> trong nhóm"
                subLegendTitle="LOS: Length of service (thời gian làm việc)"
              />
            </ChartSlide>
          </ChartMobileWrapper>
          <LoadingSection loading={Boolean(getChartSaLoading)} isFullContent />
        </RelativeWrapper>
      </OverViewBlock>
      <ListBlock>
        <TitleBlock>Danh sách Tư vấn phục vụ </TitleBlock>
        <div className="mt-24 mb-24">
          <FormSearchTemplate
            title="Thông tin"
            disableSubmit={Boolean(getListSaLoading) || Boolean(error)}
            onSubmit={handleSubmit}
          >
            <RowItem>
              <MonthInput placeholder="Tháng" value={month} error={error} onChange={handleChangeMonth} />
              {/* <DropDownFieldset
                list={getMonthList}
                placeholder="Chọn Tháng"
                value={{
                  value: formSearch.month.value,
                  label: `${formSearch.month.value}/${moment().format("YYYY")}`,
                }}
                onChange={(value) => handleChangeData("month", value as any)}
              /> */}
            </RowItem>
          </FormSearchTemplate>
        </div>
        {!Boolean(getListSaLoading) ? (
          !isNull(tableSaList) ? (
            tableSaList?.length ? (
              <>
                <h6 className="h7 sm-mb-16">
                  Tổng cộng <span className="color-primary">{tableSaList?.length}</span> kết quả đã tìm thấy
                </h6>
                <Table config={config} data={dataTable} />
                <TableMobile config={config} data={dataTable} />
              </>
            ) : null
          ) : (
            <NoData>Không có dữ liệu</NoData>
          )
        ) : (
          <LoadingSection loading />
        )}
      </ListBlock>
    </SaTeamWrapper>
  );
};

export default SaTeam;
