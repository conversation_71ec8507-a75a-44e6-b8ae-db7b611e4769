import Tab from "components/tab";
import { useMemo, useState } from "react";
import { LinkProps } from "services/menu-link";
import SaTeam from "./sa-team";
import { BusinessTeamModalWrapper } from "./styled";
import NotSaTeam from "./not-sa-team";

const tabList: LinkProps[] = [
  { value: "sa-team", label: "DS Tư vấn phục vụ (SA)" },
  { value: "not-sa-team", label: "DS Tư vấn (không bao gồm SA)" },
];

const BusinessTeamModal = () => {
  const [currentTab, setCurrentTab] = useState<"sa-team" | "not-sa-team">("sa-team");

  const Content = useMemo(() => {
    switch (currentTab) {
      case "sa-team":
        return SaTeam;
      case "not-sa-team":
        return NotSaTeam;
      default:
        return null;
    }
  }, [currentTab]);

  return (
    <BusinessTeamModalWrapper>
      <Tab type="button" top={56} list={tabList} currentTab={currentTab} onChange={(tab) => setCurrentTab(tab.value)} />
      {Content ? <Content /> : null}
    </BusinessTeamModalWrapper>
  );
};

export default BusinessTeamModal;
