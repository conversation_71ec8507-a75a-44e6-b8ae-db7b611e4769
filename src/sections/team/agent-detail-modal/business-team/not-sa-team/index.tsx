import { ERROR_API_MESSAGE } from "@constants/message";
import { TableConfig } from "@custom-types/config-table";
import { NoSAData, ReportSAInput } from "@custom-types/team";
import { getListNoSA } from "api/team";
import { Alert } from "components/alert";
import LoadingSection from "components/loading";
import Status from "components/status";
import Table from "components/table";
import TableMobile from "components/table-mobile";
import useActionApi from "hooks/use-action-api";
import { useAppSelector } from "hooks/use-redux";
import { get, isNull } from "lodash";
import moment from "moment";
import { useEffect, useMemo, useState } from "react";
import { formatValueTable } from "services/format-value";
import { NoData } from "styles";
import { NotSaTeamWrapper } from "./styled";

const config: TableConfig[] = [
  { label: "Văn phòng", key: "branchId", show: true },
  { label: "Miền kinh <br/> doanh", key: "regionCd", show: true },
  { label: "Mã AVP/ SrRD", key: "avpSrRdCode" },
  { label: "Họ tên AVP/ SrRD", key: "avpSrRdName" },
  { label: "Mã RD", key: "rdCode" },
  { label: "Họ tên RD", key: "rdName" },
  { label: "Mã DRD", key: "drdCode" },
  { label: "Họ tên DRD", key: "drdName" },
  { label: "Mã SrAD", key: "srAdCode" },
  { label: "Họ tên SrAD", key: "srAdName" },
  { label: "Mã AD", key: "adCode" },
  { label: "Họ tên AD", key: "adName" },
  { label: "Mã DAD", key: "dadCode" },
  { label: "Họ tên DAD", key: "dadName" },
  { label: "Mã FWDLT", key: "fwdltCode" },
  { label: "Họ tên FWDLT", key: "fwdltName" },
  { label: "Mã FWD", key: "fwdCode" },
  { label: "Họ tên FWD", key: "fwdName" },
  { label: "Mã FWM", key: "fwmCode" },
  { label: "Họ tên FWM", key: "fwmName" },
  { label: "Mã FWO", key: "fwoCode" },
  { label: "Họ tên FWO", key: "fwoName" },
  { label: "Mã quản lý <br/> trực tiếp", key: "directSupervisorCode", show: true },
  { label: "Tên quản lý <br/> trực tiếp", key: "directSupervisorName", show: true },
  { label: "Chức danh quản lý <br/> trực tiếp", key: "directSupervisorDesignationCd", show: true },
  { label: "Mã TVTC", key: "agentId", show: true },
  { label: "Tên TVTC", key: "agentName", show: true },
  { label: "Ngày gia nhập", key: "joiningDate", show: true, type: "date" },
  { label: "Chức danh", key: "designationCd", show: true },
  { label: "Ngày hiệu lực <br/> chức danh", key: "designationChangeDate", show: true, type: "date" },
  { label: "Tình trạng", key: "agentStatus", show: true, type: "status" },
  { label: "Ngày sinh", key: "birthDate", show: true, type: "date" },
  { label: "Giới tính", key: "genderCd", show: true },
  { label: "Số điện thoại", key: "mobileNumber", show: true },
];

const formatValueTableNotSa = (data: NoSAData, config: TableConfig) => {
  switch (config.key) {
    case "genderCd":
      return data[config.key] === "F" ? "Nữ" : data[config.key] === "M" ? "Nam" : "-";
    case "agentStatus":
      return <Status color={{ background: "#0097A9", text: "#ffffff" }} label={data[config.key]} />;
    default:
      return formatValueTable(data, config);
  }
};

const NotSaTeam = () => {
  const [data, setData] = useState([]);

  const {
    user: { username, designation },
    loading: { getListNoSALoading },
  } = useAppSelector((state) => state.rootReducer);

  const actionGetListNoSA = useActionApi<ReportSAInput, NoSAData[]>(getListNoSA);

  useEffect(() => {
    if (username) {
      const payload: ReportSAInput = {
        agentCode: username,
        designationCd: designation,
        month: moment().format("MM"),
        year: moment().format("YYYY"),
      };

      actionGetListNoSA({
        body: payload,
        loading: {
          type: "local",
          name: "getListNoSALoading",
        },
      })
        .then(({ data }) => {
          setData(data?.length ? data : null);
        })
        .catch((err) => {
          console.log(err);
          Alert("ERROR", ERROR_API_MESSAGE);
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [username, designation]);

  const dataTable = useMemo(() => {
    return data?.map((d) =>
      config.map((config) => ({
        config: config,
        node: formatValueTableNotSa(d, config),
        originData: get(d, [config.key]),
      }))
    );
  }, [data]);

  return (
    <NotSaTeamWrapper>
      <h6>Danh sách Tư vấn</h6>
      {!Boolean(getListNoSALoading) ? (
        !isNull(data) ? (
          data?.length ? (
            <>
              <Table config={config} data={dataTable} />
              <TableMobile config={config} data={dataTable} />
            </>
          ) : null
        ) : (
          <NoData>Không có dữ liệu</NoData>
        )
      ) : (
        <LoadingSection loading />
      )}
    </NotSaTeamWrapper>
  );
};

export default NotSaTeam;
