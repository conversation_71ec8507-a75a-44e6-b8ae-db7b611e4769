import { EChartsOption } from "echarts";
import dynamic from "next/dynamic";
const ReactECharts = dynamic(() => import("echarts-for-react"), { ssr: false });
import { useMemo } from "react";
import { calculatePercent, calculateTotal } from "services/untils";
import { FlexBox } from "styles";
import { ChartAgentWrapper, ChartWrapper, LabelWrapper, Legend, LegendList, Value } from "./styled";

export interface ChartAgentTotalProps {
  subTitle: string;
  legendList: { label: string; color: string; value: number }[];
  subLegendTitle?: string;
  total: number;
}

const ChartAgentTotal = ({ total, subTitle, subLegendTitle, legendList }: ChartAgentTotalProps) => {
  const colorConfig = useMemo(() => {
    const colorArray = [];
    let cumulativeSum = 0;
    const totalList = calculateTotal(legendList, "value");

    for (let i = 0; i < legendList.length; i++) {
      const { color, value } = legendList[i];
      cumulativeSum += value;
      colorArray.push([calculatePercent(cumulativeSum, totalList) / 100, color] as [number, string]);
    }

    colorArray.splice(legendList.length - 1, 1, [1, legendList[legendList.length - 1].color] as [number, string]);
    return colorArray;
  }, [legendList]);

  const option: EChartsOption = {
    series: [
      {
        type: "gauge",
        radius: "88%",
        axisLine: {
          lineStyle: {
            width: 22,
            color: colorConfig,
          },
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        splitNumber: 8,
        min: 0,
        max: 100,
      },
    ],
  };

  return (
    <ChartAgentWrapper>
      <FlexBox direction="column" alignItem="center">
        <ChartWrapper>
          <ReactECharts option={option} style={{ width: "100%", height: 250 }} />
          <Value className="mt-8">
            <h5>{total}</h5>
            <p
              className="body-5"
              dangerouslySetInnerHTML={{
                __html: subTitle,
              }}
            />
          </Value>
        </ChartWrapper>
        <LegendList>
          {legendList.map((item, index) => (
            <FlexBox alignItem="center" justifyContent="space-between" key={index}>
              <Legend color={item.color}>{item.label}</Legend>
              <h6 className="h8 mb-0">{item.value}</h6>
            </FlexBox>
          ))}
          {subLegendTitle && (
            <LabelWrapper>
              <label className="label-4">{subLegendTitle}</label>
            </LabelWrapper>
          )}
        </LegendList>
      </FlexBox>
    </ChartAgentWrapper>
  );
};

export default ChartAgentTotal;
