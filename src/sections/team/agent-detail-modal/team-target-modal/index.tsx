import { ERROR_API_MESSAGE } from "@constants/message";
import {
  TargetForMemberData,
  TargetForMemberOutput,
  TargetForTeamData,
  TargetForTeamOutput,
  TeamInput,
  TeamSetTargetInput,
} from "@custom-types/team";
import { getTargetIndividualYearly, getTargetTeamYearly, setTargetTeamYearly } from "api/team";
import { Alert } from "components/alert";
import InputNumber from "components/input-number";
import LoadingSection from "components/loading";
import useActionApi from "hooks/use-action-api";
import { useAppSelector } from "hooks/use-redux";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import { setAgentDetailModal, setFlag } from "screens/team/performance/slice";
import BlockTeamTarget from "../components/block-team-target";
import TargetUser from "../components/target-user";
import { TopAgentContentL<PERSON>tWrapper, TopAgentContentRightWrapper } from "../styled";
import CardPersonal from "./card-personal";
import { TargetInputWrapper, WrapperTargetPersonal } from "./styled";
import ConfirmModal from "components/confirm-modal";

export interface ChildrenData {
  label: string;
  value: number;
  primary?: boolean;
  onClick?: (value: any) => void;
}

export interface CardData {
  renderTitle: React.ReactNode;
  listTarget: ChildrenData[];
}

export interface CardPersonalProps {
  name: string;
  urlImage: string;
  list: CardData[];
}

export interface UpdateTarget {
  group: boolean;
  personal: boolean;
}

export interface DataTarget {
  apeCurrent: number;
  fypCurrent: number;
  casesCurrent: number;
  apePast: number;
  fypPast: number;
  casesPast: number;
}

export const renderTextTitle = (text: string, year: string | number) => {
  return (
    <>
      {text}
      <span> {year}</span>
    </>
  );
};

const TeamTargetModal = () => {
  const [openConfirmModal, setOpenConfirmModal] = useState<boolean>(false);
  const [updateTarget, setUpdateTarget] = useState<UpdateTarget>({
    group: true,
    personal: true,
  });
  const [modifiedData, setModifiedData] = useState<TargetForTeamData>();
  const [listTargetForMember, setListTargetForMember] = useState<TargetForMemberData[]>([]);
  const [targetForTeam, setTargetForTeam] = useState<TargetForTeamData>();

  const {
    loading: { getTargetIndividualYearlyLoading, getTargetTeamYearlyLoading },
    user: { username },
  } = useAppSelector((state) => state.rootReducer);

  const { currentAgentOfTeam, flag } = useAppSelector((state) => state.teamPerformanceReducer);

  const dispatch = useDispatch();

  const actionSetTargetTeamYearly = useActionApi<TeamSetTargetInput, any>(setTargetTeamYearly);
  const actionGetTargetIndividualYearly = useActionApi<TeamInput, TargetForMemberOutput>(getTargetIndividualYearly);
  const actionGetTargetTeamYearly = useActionApi<TeamInput, TargetForTeamOutput>(getTargetTeamYearly);

  const dataPersonal = useMemo<CardPersonalProps[]>(
    () =>
      listTargetForMember.map((list) => {
        return {
          name: list.agentName,
          urlImage: `${process.env.basePath}/img/avatar-default.jpg`,
          list: [
            {
              renderTitle: renderTextTitle("Mục tiêu năm", `${list.thisYear}`),
              listTarget: [
                { label: "FYP", value: list.targetFyp },
                { label: "APE", value: list.targetApe },
                { label: "Cases", value: list.targetCases },
              ],
            },
            {
              renderTitle: renderTextTitle("Kết quả đạt được năm", `${list.lastYear}`),
              listTarget: [
                { label: "FYP", value: list.lastYearFYP },
                { label: "APE", value: list.lastYearApe },
                { label: "Cases", value: list.lastYearCases },
              ],
            },
          ],
        };
      }),
    [listTargetForMember]
  );

  useEffect(() => {
    if (username || (username && flag === true)) {
      actionGetTargetTeamYearly({
        body: {
          agentId: username,
        },
        loading: {
          type: "local",
          name: "getTargetTeamYearlyLoading",
        },
      })
        .then(({ data }) => {
          setTargetForTeam(data.data);
        })
        .catch((e) => Alert("ERROR", ERROR_API_MESSAGE));
    }
    dispatch(setFlag(false));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [username, flag]);

  useEffect(() => {
    actionGetTargetIndividualYearly({
      body: {
        agentId: username,
      },
      loading: {
        type: "local",
        name: "getTargetIndividualYearlyLoading",
      },
    })
      .then(({ data }) => {
        setListTargetForMember(data.listData);
      })
      .catch((e) => Alert("ERROR", ERROR_API_MESSAGE));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [username]);

  useEffect(() => {
    setModifiedData(targetForTeam);
  }, [targetForTeam, currentAgentOfTeam]);

  const handleChangeInput = (name: string, value: any) => {
    setModifiedData((pre) => ({
      ...pre,
      [name]: value,
    }));
  };

  const handleUpdateTarget = (name: string, value: boolean) => {
    setUpdateTarget((pre) => ({
      ...pre,
      [name]: false,
    }));

    if (name == "group" && value == true) {
      setOpenConfirmModal(true);
    }

    if (name == "personal") {
      //show modal when click update right
      dispatch(
        setAgentDetailModal({
          type: "edit-personal",
          title: "Chỉnh sửa mục tiêu từng thành viên trong nhóm",
          data: null,
        })
      );
    }
  };

  const handleCancelModal = () => {
    setOpenConfirmModal(false);
    setUpdateTarget((pre) => ({
      ...pre,
      group: true,
    }));
  };

  const handleSubmitContentLeft = useCallback(() => {
    const payload = {
      agentId: username,
      fyp: Number(modifiedData?.targetFyp),
      cases: Number(modifiedData?.targetCases),
      ape: Number(modifiedData?.targetApe),
    };

    if (username) {
      actionSetTargetTeamYearly({
        body: payload,
        loading: {
          type: "global",
          name: "setTargetTeamYearlyLoading",
        },
      })
        .then((data) => {
          if (data.status === 200) {
            setOpenConfirmModal(false);
            setUpdateTarget((pre) => ({
              ...pre,
              group: true,
            }));
            dispatch(setFlag(true));
          }
        })
        .catch((e) => {
          Alert("ERROR", ERROR_API_MESSAGE);
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [username, modifiedData]);

  return (
    <>
      <TopAgentContentLeftWrapper>
        <BlockTeamTarget
          name="group"
          background="#ffffff"
          renderTitle={renderTextTitle("Mục tiêu nhóm", targetForTeam?.thisYear)}
          showButton={true}
          updateTarget={updateTarget}
          onUpdateTarget={handleUpdateTarget}
        >
          {!getTargetTeamYearlyLoading ? (
            <>
              {updateTarget.group ? (
                <TargetUser
                  fyp={targetForTeam?.targetFyp}
                  ape={targetForTeam?.targetApe}
                  cases={targetForTeam?.targetCases}
                />
              ) : (
                <TargetInputWrapper>
                  <InputNumber
                    value={modifiedData?.targetFyp ?? 0}
                    placeholder="FYP"
                    maxLength={19}
                    allowNegative={false}
                    onChange={(value) => handleChangeInput("targetFyp", value)}
                  />
                  <InputNumber
                    value={modifiedData?.targetApe ?? 0}
                    placeholder="APE"
                    maxLength={19}
                    allowNegative={false}
                    onChange={(value) => handleChangeInput("targetApe", value)}
                  />
                  <InputNumber
                    value={modifiedData?.targetCases ?? 0}
                    placeholder="Case"
                    maxLength={19}
                    allowNegative={false}
                    onChange={(value) => handleChangeInput("targetCases", value)}
                  />
                </TargetInputWrapper>
              )}

              <BlockTeamTarget
                renderTitle={renderTextTitle("Kết quả nhóm đạt được năm", targetForTeam?.lastYear)}
                showButton={false}
                subBlock
              >
                <TargetUser
                  subTarget
                  fyp={targetForTeam?.lastYearFYP}
                  ape={targetForTeam?.lastYearApe}
                  cases={targetForTeam?.lastYearCases}
                />
              </BlockTeamTarget>
            </>
          ) : (
            <LoadingSection loading={Boolean(getTargetTeamYearlyLoading)} />
          )}
        </BlockTeamTarget>
      </TopAgentContentLeftWrapper>
      <TopAgentContentRightWrapper>
        <WrapperTargetPersonal>
          <BlockTeamTarget
            name="personal"
            background="#ffffff"
            renderTitle="Mục tiêu từng thành viên trong nhóm"
            showButton={true}
            updateTarget={updateTarget}
            onUpdateTarget={handleUpdateTarget}
          >
            {!getTargetIndividualYearlyLoading ? (
              <CardPersonal data={dataPersonal} />
            ) : (
              <LoadingSection loading={Boolean(getTargetIndividualYearlyLoading)} />
            )}
          </BlockTeamTarget>
        </WrapperTargetPersonal>
      </TopAgentContentRightWrapper>
      <ConfirmModal
        onConfirm={handleSubmitContentLeft}
        onCancel={handleCancelModal}
        show={openConfirmModal}
        title="Lưu thay đổi?"
        content="TVTC có chắc lưu thông tin đã được cập nhật?"
      />
    </>
  );
};

export default TeamTargetModal;
