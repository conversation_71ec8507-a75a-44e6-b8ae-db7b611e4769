import { EChartsOption, GaugeSeriesOption } from "echarts";
import dynamic from "next/dynamic";
const ReactECharts = dynamic(() => import("echarts-for-react"), { ssr: false });
import useWindowResize from "hooks/use-window-resize";
import { useMemo } from "react";
import { formatNumber } from "services/untils";
import { useTheme } from "styled-components";
import { ChartRankProps } from ".";

const ChartRank = ({ data }: { data: ChartRankProps }) => {
  const theme = useTheme();
  const size = useWindowResize();

  const font = (size: number, weight: number) => {
    return {
      fontSize: size,
      fontWeight: weight,
      fontFamily: "FWD",
      color: theme.color.text.body,
    };
  };

  const dataConfig: GaugeSeriesOption["data"] = useMemo(() => {
    return [
      {
        name: `${formatNumber(data.max)} `,
        title: {
          ...font(size.width > 768 ? 31 : 25, 700),
          color: theme.color.status.primary,
          offsetCenter: ["3%", "-10%"],
        },
        detail: {
          ...font(size.width > 768 ? 12 : 10, 500),
          offsetCenter: ["45%", "70%"],
          formatter: "Top FYP",
        },
      },
      {
        title: {
          show: false,
        },
        detail: {
          ...font(size.width > 768 ? 12 : 10, 500),
          formatter: "0 FYP",
          offsetCenter: ["-45%", "70%"],
        },
        pointer: { length: 0, width: 0 },
      },
      {
        name: "TVTC",
        title: {
          ...font(size.width > 768 ? 14 : 12, 500),
          offsetCenter: ["0%", "10%"],
        },
        detail: {
          show: false,
        },
        pointer: { length: 0, width: 0 },
      },
    ];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, size]);

  const listRank: any = useMemo(() => {
    if (!data?.rank?.length) {
      return [];
    }

    return data.rank
      .map((item) => {
        return {
          radius: "75%",
          center: ["50%", "50%"],
          type: "gauge",
          progress: {
            show: false,
          },
          color: ["transparent", "transparent"],
          axisLine: {
            show: false,
          },
          pointer: {
            icon: item.name,
            length: 40,
            width: 40,
            offsetCenter: [0, "-100%"],
          },
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          detail: {
            show: false,
          },
          data: [{ value: item.value }],
        };
      })
      .reverse();
  }, [data]);

  const option: EChartsOption = {
    series: [
      //me
      {
        radius: "85%",
        type: "gauge",
        axisLine: {
          lineStyle: {
            width: 30,
            color: [
              [0.25, "#0097A9"],
              [0.5, "#6ECEB2"],
              [0.75, "#E87722"],
              [1, "#FED141"],
            ],
          },
        },
        pointer: {
          length: 0,
          width: 0,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          fontSize: 12,
          fontFamily: "FWD",
          color: "#ffffff",
          rotate: "tangential",
          distance: 0,

          formatter: function (value) {
            if (value === 12.5) {
              return "Trung bình";
            } else if (value === 37.5) {
              return "Nhóm 25%";
            } else if (value === 62.5) {
              return "Nhóm 50%";
            } else if (value === 87.5) {
              return "Nhóm 75%";
            }
            return "";
          },
        },
        splitNumber: 8,

        min: 0,
        max: 100,
        data: dataConfig,
      },

      ...listRank,
    ],
  };

  return <ReactECharts option={option} notMerge={true} lazyUpdate={true} />;
};

export default ChartRank;
