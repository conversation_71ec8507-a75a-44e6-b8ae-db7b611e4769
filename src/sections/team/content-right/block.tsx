import React from "react";
import { PerformanceRightBlockWrapper } from "./styled";

const PerformanceRightBlock = ({
  title,
  date,
  children,
}: {
  title: string;
  date: string;
  children: React.ReactNode | React.ReactNode[];
}) => {
  return (
    <PerformanceRightBlockWrapper>
      <h6 className="h7">{title}</h6>
      <p className="body-5">{date}</p>

      {children}
    </PerformanceRightBlockWrapper>
  );
};

export default PerformanceRightBlock;
