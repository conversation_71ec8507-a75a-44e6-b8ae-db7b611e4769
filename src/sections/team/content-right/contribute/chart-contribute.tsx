import dynamic from "next/dynamic";
const ReactECharts = dynamic(() => import("echarts-for-react"), { ssr: false });
import useWindowResize from "hooks/use-window-resize";
import { get } from "lodash";
import { formatNumber } from "services/untils";
import { useTheme } from "styled-components";
import { ChartPieContribute } from ".";
import { ChartContributeWrapper, Legend, LegendList } from "./styled";
import { formatValue } from "services/format-value";

const ChartContribute = ({ data, colorByRank }: { data: ChartPieContribute; colorByRank: any }) => {
  const theme = useTheme();
  const size = useWindowResize();

  const font = (size: number, weight: number) => {
    return {
      fontSize: size,
      fontWeight: weight,
      fontFamily: "FWD",
      color: theme.color.text.primary,
    };
  };

  const dataContribute = data.rank.map((item, index) => {
    return {
      itemStyle: {
        color: get(colorByRank, [index + 1], "#E87722"), //#E87722 is default color
      },
      value: item.value,
      name: item.label,
    };
  });

  //EChartsOption
  const option: any = {
    title: {
      textStyle: font(size.width > 768 ? 23 : 20, 700),
      left: "center",
      top: "center",
      text: formatNumber(data.sum),
      subtext: "Tổng số của nhóm FYP (YTD)",
      subtextStyle: {
        fontFamily: "FWD",
        fontSize: size.width > 768 ? 12 : 10,
        align: "center",
        color: "#183028",
      },
    },
    legend: {
      show: false,
    },
    tooltip: {
      trigger: "item",
      textStyle: font(14, 500),
      formatter: (params: any) => {
        const { name, value, marker } = params;

        return `${marker} ${name}: ${formatValue(value, "percent")}`;
      },
    },
    series: [
      {
        type: "pie",
        radius: size.width > 768 ? ["68%", "84%"] : ["62%", "74%"],
        center: ["50%", "50%"],
        hoverAnimation: false,
        label: {
          show: false,
        },
        data: dataContribute,
      },
    ],
  };

  return (
    <ChartContributeWrapper>
      <LegendList>
        {data?.rank?.map((item, index) => (
          <Legend className="label-3" key={index} color={get(colorByRank, [index + 1], "#E87722")}>
            {item.label}: {formatValue(item.value, "percent")}
          </Legend>
        ))}
      </LegendList>
      <ReactECharts option={option} notMerge={true} lazyUpdate={true} />
    </ChartContributeWrapper>
  );
};

export default ChartContribute;
