import { PerformanceDetailMonthly } from "@custom-types/performance";
import { OverviewTeamIfaMonthlyData } from "@custom-types/team";
import CardPerformance, { CardData } from "components/card";
import LoadingSection from "components/loading";
import useIsManager from "hooks/use-is-manager";
import { useAppSelector } from "hooks/use-redux";
import { useMemo } from "react";
import { useDispatch } from "react-redux";
import { setFypLessThan10Mil } from "screens/team/performance/slice";
import { ContentProps } from "../..";
import ChartActiveInTeam from "../../../components/chart-active-in-group";
import { searchByFypLessThan10Mil } from "../../../components/fyp-less-than-10mil";
import { MonthlyWrapper } from "./styled";

const Monthly = ({ dataDetail, businessTeam }: ContentProps) => {
  const { getPerformanceDetailLoading } = useAppSelector((state) => state.rootReducer.loading);

  const isManager = useIsManager();
  const dispatch = useDispatch();

  const dataTeamAboveChart: CardData[][][] = useMemo(() => {
    const dataTeamDetail = dataDetail as OverviewTeamIfaMonthlyData;

    return [
      [
        [
          {
            title: "Đội ngũ kinh doanh",
            children: businessTeam.concat([
              {
                label: "Số Tư vấn phục vụ (SA)",
                value: dataTeamDetail?.totalAgentOnlySA,
              },
              {
                label: "Số TV tuyển mới",
                value: dataTeamDetail?.agentNewJoin,
              },
              {
                label: "Số TV đã chấm dứt hoạt động",
                value: dataTeamDetail?.agentTerminated,
              },
              {
                label: "Tổng số TVTC bao gồm SA",
                value: dataTeamDetail?.totalAgentIncludingSA,
              },
              {
                label: "Tổng số TVTC không bao gồm SA",
                value: dataTeamDetail?.totalAgentNotIncludingSA,
              },
            ]),
          },
        ],
      ],
      [
        [
          {
            title: "Đại lý hoạt động",
            children: [{ label: "IP>= 5 triệu/ tháng", value: dataTeamDetail?.agentActiveIP }],
          },
          {
            title: "Số lượng TV có FYP < 10tr",
            children: [
              {
                label: "Trong 3 tháng",
                value: dataTeamDetail?.fyplessthan10In3Month,
                primary: true,
                onClick: () => handleShowFpyLessThan10Mil("3"),
              },
              {
                label: "Trong 2 tháng",
                value: dataTeamDetail?.fyplessthan10In2Month,
                primary: true,
                onClick: () => handleShowFpyLessThan10Mil("2"),
              },
              {
                label: "Trong tháng hiện tại",
                value: dataTeamDetail?.fyplessthan10Cur,
                primary: true,
                onClick: () => handleShowFpyLessThan10Mil("0"),
              },
            ],
          },
        ],
      ],
    ];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataDetail, businessTeam]);

  const dataTeamBelowChart: CardData[][][] = useMemo(() => {
    const dataTeamDetail = dataDetail as OverviewTeamIfaMonthlyData;

    return [
      [
        [
          {
            title: "HĐ nộp",
            children: [
              { label: "APE", value: dataTeamDetail?.submitApe },
              { label: "IP", value: dataTeamDetail?.submitIP },
              { label: "Cases", value: dataTeamDetail?.submitCases },
            ],
          },
        ],
      ],
      [
        [
          {
            title: "HĐ chờ xử lý",
            children: [
              { label: "Nộp tháng này", value: dataTeamDetail?.pendingCases },
              { label: "APE", value: dataTeamDetail?.pendingApe },
              { label: "IP", value: dataTeamDetail?.pendingIP },
            ],
          },
          {
            title: "",
            children: [
              { label: "Nộp tháng trước", value: dataTeamDetail?.pendingPreMonthCases },
              { label: "APE", value: dataTeamDetail?.pendingPreMonthApe },
              { label: "IP", value: dataTeamDetail?.pendingPreMonthIP },
            ],
          },
        ],
      ],
      [
        [
          {
            title: "HĐ phát hành",
            children: [
              { label: "APE", value: dataTeamDetail?.issueApe },
              { label: "IP", value: dataTeamDetail?.issueIP },
              { label: "Cases", value: dataTeamDetail?.issueCases },
            ],
          },
          {
            title: "HĐ điều chỉnh",
            children: [
              { label: "APE", value: dataTeamDetail?.adjApe },
              { label: "IP", value: dataTeamDetail?.adjIP },
              { label: "Cases", value: dataTeamDetail?.adjCases },
            ],
          },
        ],
      ],
      [
        [
          {
            title: "HĐ hủy",
            children: [
              { label: "APE", value: dataTeamDetail?.cancelApe },
              { label: "IP", value: dataTeamDetail?.cancelIP },
              { label: "Cases", value: dataTeamDetail?.cancelCases },
            ],
          },
          {
            title: "HĐ phát hành thuần",
            children: [
              { label: "APE", value: dataTeamDetail?.netIssueApe },
              { label: "IP", value: dataTeamDetail?.netIssueIP },
              { label: "Cases", value: dataTeamDetail?.netIssueCases },
            ],
          },
        ],
      ],
    ];
  }, [dataDetail]);

  const dataPersonal: CardData[][][] = useMemo(() => {
    const dataPersonalDetail = dataDetail as PerformanceDetailMonthly;

    return [
      [
        [
          {
            title: "HĐ nộp",
            children: [
              { label: "APE", value: dataPersonalDetail?.IndividualMonthlySubmitApe },
              { label: "IP", value: dataPersonalDetail?.IndividualMonthlySubmitIp },
              { label: "Cases", value: dataPersonalDetail?.IndividualMonthlySubmitCaseCount },
            ],
          },
        ],
      ],
      [
        [
          {
            title: "HĐ chờ xử lý",
            children: [
              { label: "Nộp tháng này", value: dataPersonalDetail?.IndividualMonthlyPendingCaseCount },
              { label: "APE", value: dataPersonalDetail?.IndividualMonthlyPendingApe },
              { label: "IP", value: dataPersonalDetail?.IndividualMonthlyPendingIp },
            ],
          },
          {
            title: "",
            children: [
              { label: "Nộp tháng trước", value: dataPersonalDetail?.IndividualMonthlyPendingPrevMonthCaseCount },
              { label: "APE", value: dataPersonalDetail?.IndividualMonthlyPendingPrevMonthApe },
              { label: "IP", value: dataPersonalDetail?.IndividualMonthlyPendingPrevMonthIp },
            ],
          },
        ],
      ],
      [
        [
          {
            title: "HĐ phát hành",
            children: [
              { label: "APE", value: dataPersonalDetail?.IndividualMonthlyIssueApe },
              { label: "IP", value: dataPersonalDetail?.IndividualMonthlyIssueIp },
              { label: "Cases", value: dataPersonalDetail?.IndividualMonthlyIssueCaseCount },
            ],
          },
          {
            title: "HĐ điều chỉnh",
            children: [
              { label: "APE", value: dataPersonalDetail?.IndividualMonthlyAdjustmentApe },
              { label: "IP", value: dataPersonalDetail?.IndividualMonthlyAdjustmentIp },
              { label: "Cases", value: dataPersonalDetail?.IndividualMonthlyAdjustmentCaseCount },
            ],
          },
        ],
      ],
      [
        [
          {
            title: "HĐ hủy",
            children: [
              { label: "APE", value: dataPersonalDetail?.IndividualMonthlyCancelApe },
              { label: "IP", value: dataPersonalDetail?.IndividualMonthlyCancelIp },
              { label: "Cases", value: dataPersonalDetail?.IndividualMonthlyCancelCaseCount },
            ],
          },
          {
            title: "HĐ phát hành thuần",
            children: [
              { label: "APE", value: dataPersonalDetail?.IndividualMonthlyNetIssueApe },
              { label: "IP", value: dataPersonalDetail?.IndividualMonthlyNetIssueIp },
              { label: "Cases", value: dataPersonalDetail?.IndividualMonthlyNetIssueCaseCount },
            ],
          },
        ],
      ],
    ];
  }, [dataDetail]);

  const chartData = useMemo(() => {
    const dataTeamDetail = dataDetail as OverviewTeamIfaMonthlyData;

    return {
      blue: dataTeamDetail?.agentActiveOldInforce,
      orange: dataTeamDetail?.agentActiveNewInforce,
      yellow: dataTeamDetail?.agentNotActive,
      total: dataTeamDetail?.totalAgentActive,
    };
  }, [dataDetail]);

  const handleShowFpyLessThan10Mil = (value: string) => {
    const findValue = searchByFypLessThan10Mil.find((item) => item.value === value);
    dispatch(setFypLessThan10Mil(findValue));
  };

  return !Boolean(getPerformanceDetailLoading) ? (
    <MonthlyWrapper>
      {isManager ? (
        <>
          <CardPerformance data={dataTeamAboveChart} />
          <ChartActiveInTeam data={chartData} />
          <CardPerformance data={dataTeamBelowChart} />
        </>
      ) : (
        <CardPerformance data={dataPersonal} />
      )}
    </MonthlyWrapper>
  ) : (
    <LoadingSection loading />
  );
};

export default Monthly;
