import { ERROR, ERROR_API_MESSAGE } from "@constants/message";
import { OverviewBancaBOTOData, OverviewBancaBOTOOutput, OverViewTeamDetailInput } from "@custom-types/team";
import { getOverviewTeamBanca, getOverviewTeamBancaBOTO } from "api/team";
import { Alert } from "components/alert";
import { formatAgentList } from "components/dropdown-hierarchy";
import useActionApi from "hooks/use-action-api";
import useIsManager from "hooks/use-is-manager";
import { useAppSelector } from "hooks/use-redux";
import { capitalize, get } from "lodash";
import { useEffect, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import { getPerformanceDetailPersonalAction } from "screens/performance/action";
import { setFypForFormSearchAction } from "screens/team/performance/action";
import { addLevelDepth } from "services/untils";
import Daily from "./components/daily";
import Monthly from "./components/monthly";
import Yearly from "./components/yearly";
import { BancaWrapper } from "./styled";

export interface ContentProps {
  dataDetail: any;
  dataBOTO?: OverviewBancaBOTOData[];
  isShowTableBOTO?: boolean;
}

const Banca = () => {
  const [dataTeamOverviewDetail, setDataTeamOverviewDetail] = useState<any>();
  const [dataOverviewBOTO, setDataOverviewBOTO] = useState<OverviewBancaBOTOData[]>([]);

  const { channel } = useAppSelector((state) => state.rootReducer.user);
  const { currentAgentOfTeam, performanceTeamForm } = useAppSelector((state) => state.teamPerformanceReducer);
  const { performanceTeamDate, performanceTeamType, performanceTeamErrorDate } = performanceTeamForm;

  const isManager = useIsManager();
  const dispatch = useDispatch();

  const actionGetOverviewTeamBanca = useActionApi<OverViewTeamDetailInput, any>(getOverviewTeamBanca);
  const actionGetOverviewBOTO = useActionApi<OverViewTeamDetailInput, OverviewBancaBOTOOutput>(
    getOverviewTeamBancaBOTO
  );

  const isShowTableBOTO = ["ASDB", "RSDB", "ACD"].includes(currentAgentOfTeam.designationCd);

  useEffect(() => {
    if (currentAgentOfTeam.agentCode && performanceTeamType) {
      //check format date
      if (
        (performanceTeamDate.length !== 10 && performanceTeamType === "daily") ||
        (performanceTeamDate.length !== 7 && performanceTeamType === "monthly") ||
        (performanceTeamDate.length !== 4 && performanceTeamType === "yearly") ||
        performanceTeamErrorDate
      ) {
        return;
      }

      const splitDate = performanceTeamDate.split("/");

      const payload = {
        agentId: currentAgentOfTeam.agentCode,
        channelCd: channel,
        day: splitDate[splitDate.length - 3],
        month: splitDate[splitDate.length - 2],
        year: splitDate[splitDate.length - 1],
      };

      if (isManager) {
        actionGetOverviewTeamBanca({
          body: {
            payload: payload,
            type:
              performanceTeamType === "monthly"
                ? "mtd"
                : performanceTeamType === "yearly"
                ? "ytd"
                : performanceTeamType,
          },
          loading: { type: "local", name: "getPerformanceDetailLoading" },
        })
          .then(({ data }) => {
            const dataOverview = data?.data as OverviewBancaBOTOData;

            setDataTeamOverviewDetail(dataOverview);
            dispatch(setFypForFormSearchAction({ key: "team", value: dataOverview?.fyp }));
          })
          .catch((error) => {
            console.log("error", error);
            Alert(ERROR, ERROR_API_MESSAGE);
          });
      } else {
        dispatch(
          getPerformanceDetailPersonalAction({
            payload: payload,
            type: performanceTeamType,
            loading: {
              type: "local",
              name: "getPerformanceDetailLoading",
            },
            callback: ({ data }) => {
              setDataTeamOverviewDetail(data);
              dispatch(
                setFypForFormSearchAction({
                  key: "team",
                  value: get(data, `Individual${capitalize(performanceTeamType)}Fyp`, 0),
                })
              );
            },
          })
        );
      }

      if (performanceTeamType !== "daily") {
        actionGetOverviewBOTO({
          body: {
            payload: payload,
            type: performanceTeamType,
          },
          loading: { type: "local", name: "getOverviewBancaBOTOLoading" },
        })
          .then(({ data }) => {
            if (data?.listData?.length) {
              const parentAgentCode = data?.listData[0]?.supervisorId ?? "";
              setDataOverviewBOTO(addLevelDepth(formatAgentList(data.listData, parentAgentCode)));
            }
          })
          .catch((error) => {
            console.log("error", error);
            Alert(ERROR, ERROR_API_MESSAGE);
          });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    currentAgentOfTeam.agentCode,
    performanceTeamDate,
    isManager,
    channel,
    performanceTeamType,
    performanceTeamErrorDate,
  ]);

  const Content = useMemo(() => {
    switch (performanceTeamForm.performanceTeamType) {
      case "daily":
        return Daily;
      case "monthly":
        return Monthly;
      case "yearly":
        return Yearly;
      default:
        return null;
    }
  }, [performanceTeamForm.performanceTeamType]);

  return (
    <BancaWrapper>
      {Content ? (
        <Content dataDetail={dataTeamOverviewDetail} dataBOTO={dataOverviewBOTO} isShowTableBOTO={isShowTableBOTO} />
      ) : null}
    </BancaWrapper>
  );
};

export default Banca;
