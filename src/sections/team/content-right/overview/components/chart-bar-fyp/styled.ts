import styled from "styled-components";
import { device } from "styles/media";

export const ChartBarFYPWrapper = styled.div`
  margin-top: 20px;
  padding: 20px;

  background: #ffffff;
  border: 1px solid ${({ theme }) => theme.color.status.grey};
  border-radius: 12px;
`;

export const ChartWrapper = styled.div`
  width: 100%;
  height: 255px;

  overflow: hidden;

  border-radius: 12px;
  background: #ffffff;

  /* 
  @media ${device.mobile} {
    .echarts-for-react {
      height: 131px !important;
    }
  } */
`;

export const Value = styled.div`
  width: 100%;
  margin-top: -10px;

  display: flex;
  flex-direction: column;
  align-items: center;

  position: relative;

  h6 {
    color: ${({ theme }) => theme.color.status.primary};
  }

  p {
    color: ${({ theme }) => theme.color.status.grey_darker};
  }

  svg {
    position: absolute;
    bottom: calc(100% + 6px);
    left: 50%;
    transform: translateX(-50%);
  }
`;

export const XName = styled.p`
  width: 100%;
  padding-right: 16px;

  font-size: 12px;
  line-height: 150%;
  text-align: right;

  color: ${({ theme }) => theme.color.status.grey_darkest};
`;
