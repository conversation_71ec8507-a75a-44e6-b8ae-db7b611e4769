import { EChartsOption } from "echarts";
import { CallbackDataParams } from "echarts/types/dist/shared";
import dynamic from "next/dynamic";
const ReactECharts = dynamic(() => import("echarts-for-react"), { ssr: false });
import { range } from "lodash";
import { useMemo } from "react";
import { formatNumberToMoneyString } from "services/untils";
import { ChartBarFYPWrapper, ChartWrapper, XName } from "./styled";

export interface ChartDataProps {
  directTeam?: number[];
  allTeam?: number[];
}

export interface ChartBarFYPProps {
  title: string;
  data: ChartDataProps;
  showLabel?: boolean;
  showLegend?: boolean;
  className?: string;
}

const ChartBarFYP = ({ title, data, showLegend, showLabel, className }: ChartBarFYPProps) => {
  const { directTeam, allTeam } = data;

  const option: EChartsOption = useMemo(() => {
    return {
      tooltip: {
        show: false,
      },
      legend: {
        show: showLegend,
        data: ["Nhóm trực tiếp", "Toàn nhóm"],
        left: 20,
        top: 10,
        itemWidth: 14,
        itemHeight: 14,
        itemGap: 20,
        textStyle: {
          fontFamily: "FWD",
          fontSize: 14,
          fontWeight: 400,
        },
      },
      xAxis: {
        axisLabel: {},
        data: range(1, 13),
        axisLine: { onZero: true },
        splitArea: { show: false },
        axisTick: { show: false },
      },
      yAxis: [
        {
          axisLabel: {
            formatter: function (value: number) {
              return value > 10 ? formatNumberToMoneyString(value).result : value.toString();
            },
          },
        },
      ],
      grid: {
        top: !showLegend ? "30px" : " 60px",
        left: "50px",
        right: "16px",
      },
      label: {
        label: {
          formatter: function (value: CallbackDataParams) {
            let data: number = Number(value.data);
            return data > 10 ? formatNumberToMoneyString(data).result : value.toString();
          },
          show: showLabel,
          fontSize: 10,
          position: "top",
          fontFamily: "FWD",
        },
      },
      series: [
        directTeam
          ? {
              name: "Nhóm trực tiếp",
              type: "bar",
              stack: "one",
              color: "#E87722",
              data: directTeam,
              label: {
                formatter: function (value: CallbackDataParams) {
                  let data: number = Number(value.data);
                  return data > 0 ? (data > 10 ? formatNumberToMoneyString(data).result : data.toString()) : "";
                },
                show: showLabel,
                fontSize: 10,
                position: "top",
                fontFamily: "FWD",
              },
            }
          : null,
        allTeam
          ? {
              name: "Toàn nhóm",
              type: "bar",
              stack: "one",
              color: "#FED141",
              data: allTeam,
              label: {
                formatter: function (value: CallbackDataParams) {
                  let data: number = Number(value.data);
                  return data > 0 ? (data > 10 ? formatNumberToMoneyString(data).result : data.toString()) : "";
                },
                show: showLabel,
                fontSize: 10,
                position: "top",
                fontFamily: "FWD",
              },
            }
          : null,
      ],
    };
  }, [allTeam, directTeam, showLabel, showLegend]);

  return (
    <ChartBarFYPWrapper className={className}>
      <h6 className="h7">{title}</h6>
      <ChartWrapper>
        <ReactECharts option={option} notMerge={true} lazyUpdate={true} />
      </ChartWrapper>
      <XName>Tháng</XName>
    </ChartBarFYPWrapper>
  );
};

export default ChartBarFYP;
