import { ERROR, ERROR_API_MESSAGE } from "@constants/message";
import { OverViewTeamDetailInput, OverviewTeamAgencyMonthlyOutput } from "@custom-types/team";
import { getOverviewTeamAgency } from "api/team";
import { Alert } from "components/alert";
import useActionApi from "hooks/use-action-api";
import useIsManager from "hooks/use-is-manager";
import { useAppSelector } from "hooks/use-redux";
import { capitalize, get } from "lodash";
import { useEffect, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import { getPerformanceDetailPersonalAction } from "screens/performance/action";
import { setFypForFormSearchAction } from "screens/team/performance/action";
import Monthly from "./components/monthly";
import Quarterly from "./components/quarterly";
import Yearly from "./components/yearly";
import { AgencyWrapper } from "./styled";

const convertPerformanceType = {
  monthly: "mtd",
  quarterly: "qtd",
  yearly: "ytd",
};

const Agency = () => {
  const {
    user: { channel },
  } = useAppSelector((state) => state.rootReducer);
  const [dataTeamOverviewDetail, setDataTeamOverviewDetail] = useState<any>();

  const { currentAgentOfTeam, performanceTeamForm } = useAppSelector((state) => state.teamPerformanceReducer);
  const { performanceTeamDate, performanceTeamType, performanceTeamErrorDate, performanceTeamQuarterly } =
    performanceTeamForm;

  const isManager = useIsManager();
  const dispatch = useDispatch();

  const actionOverviewTeamAgencyDetail = useActionApi<OverViewTeamDetailInput, OverviewTeamAgencyMonthlyOutput>(
    getOverviewTeamAgency
  );

  useEffect(() => {
    if (currentAgentOfTeam.agentCode && performanceTeamType && !performanceTeamErrorDate) {
      const splitDate = performanceTeamDate.split("/");
      const payload = {
        agentId: currentAgentOfTeam.agentCode,
        channelCd: channel,
        day: splitDate[splitDate.length - 3],
        month: splitDate[splitDate.length - 2],
        year: splitDate[splitDate.length - 1],
        quarterly: performanceTeamQuarterly?.value,
      };

      if (isManager) {
        actionOverviewTeamAgencyDetail({
          loading: {
            type: "local",
            name: "getPerformanceDetailLoading",
          },
          body: {
            payload: payload,
            type: get(convertPerformanceType, [performanceTeamType]),
          },
        })
          .then(({ data }) => {
            setDataTeamOverviewDetail(data.data);
            dispatch(setFypForFormSearchAction({ key: "team", value: data.data?.fyp }));
          })
          .catch((error) => {
            console.log(error);
            Alert(ERROR, ERROR_API_MESSAGE);
          });
      } else {
        dispatch(
          getPerformanceDetailPersonalAction({
            payload: payload,
            type: performanceTeamType,
            loading: {
              type: "local",
              name: "getPerformanceDetailLoading",
            },
            callback: ({ data }) => {
              setDataTeamOverviewDetail(data);
              dispatch(
                setFypForFormSearchAction({
                  key: "team",
                  value: get(data, `Individual${capitalize(performanceTeamType)}Fyp`, 0),
                })
              );
            },
          })
        );
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentAgentOfTeam.agentCode, performanceTeamForm, isManager, channel]);

  const Content = useMemo(() => {
    switch (performanceTeamForm.performanceTeamType) {
      case "monthly":
        return Monthly;
      case "quarterly":
        return Quarterly;
      case "yearly":
        return Yearly;
      default:
        return null;
    }
  }, [performanceTeamForm.performanceTeamType]);

  return <AgencyWrapper>{Content ? <Content dataDetail={dataTeamOverviewDetail} /> : null}</AgencyWrapper>;
};

export default Agency;
