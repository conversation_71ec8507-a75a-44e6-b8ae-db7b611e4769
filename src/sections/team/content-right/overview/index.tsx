import { ERROR, ERROR_API_MESSAGE } from "@constants/message";
import { AgentIdInput } from "@custom-types/home";
import { PerformanceFormSearchValue, PerformanceKPIInput, PerformanceKPIOutput } from "@custom-types/performance";
import { KPIOverviewTeamData, KPIOverviewTeamOutput } from "@custom-types/team";
import { getPerformancePersonal } from "api/performance";
import { getKPIOverviewTeam } from "api/team";
import { Alert } from "components/alert";
import ChartSlide from "components/chart-slide";
import FormSearchTemplate from "components/form-search-template";
import LoadingSection from "components/loading";
import ModalFullPage from "components/modal-full-page";
import useActionApi from "hooks/use-action-api";
import { useAppSelector } from "hooks/use-redux";
import { useEffect, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import { setFypLessThan10Mil, setPerformanceTeamForm } from "screens/team/performance/slice";
import { <PERSON><PERSON><PERSON><PERSON>, RelativeWrapper } from "styles";
import Agency from "./agency";
import Banca from "./banca";
import ChartGaugeFYP, { ChartGaugeFYPData } from "./components/chart-gauge-fyp";
import FYPLessThan10Mil from "./components/fyp-less-than-10mil";
import Ifa from "./ifa";
import { ChartGaugeFYPrapper, NoteWrapper, TeamPerformanceOverviewWrapper } from "./styled";
import FormSearchPerformance from "./components/form-search";

const TeamPerformanceOverview = () => {
  const [dataKPIOverViewTeam, setDataKPIOverviewTeam] = useState<KPIOverviewTeamData>({
    ape: 0,
    fyp: 0,
    targetApe: 0,
    targetFyp: 0,
  });

  const {
    user: { channel },
    loading: { getKPIOverviewTeamLoading, userMeLoading, getHierarchyLoading, getKPILoading },
  } = useAppSelector((state) => state.rootReducer);
  const { fypForFormSearch, fypLessThan10Mil, currentAgentOfTeam, performanceTeamForm, flag } = useAppSelector(
    (state) => state.teamPerformanceReducer
  );
  const { performanceTeamErrorDate, performanceTeamDate, performanceTeamType, performanceTeamQuarterly } =
    performanceTeamForm ?? {};

  const dispatch = useDispatch();

  const actionGetKPIOverviewTeam = useActionApi<AgentIdInput, KPIOverviewTeamOutput>(getKPIOverviewTeam);
  const actionGetKPI = useActionApi<PerformanceKPIInput, PerformanceKPIOutput>(getPerformancePersonal);

  useEffect(() => {
    if (currentAgentOfTeam.agentCode || flag === true) {
      if (currentAgentOfTeam.manager) {
        actionGetKPIOverviewTeam({
          body: { agentId: currentAgentOfTeam.agentCode },
          loading: { type: "local", name: "getKPIOverviewTeamLoading" },
        })
          .then(({ data }) => {
            setDataKPIOverviewTeam(data.data);
          })
          .catch((error) => {
            console.log("error", error);
            Alert(ERROR, ERROR_API_MESSAGE);
          });
      } else {
        actionGetKPI({
          body: {
            agentId: currentAgentOfTeam.agentCode,
            channelCd: channel,
          },
          loading: {
            type: "local",
            name: "getKPILoading",
          },
        })
          .then(({ data }) => {
            setDataKPIOverviewTeam({
              ape: data.apeYearly,
              fyp: data.fypYearly,
              targetApe: data.ytdTargetApe,
              targetFyp: data.ytdTargetFyp,
            });
          })
          .catch((error) => {
            console.log("error", error);
            Alert(ERROR, ERROR_API_MESSAGE);
          });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentAgentOfTeam, flag, channel]);

  const chartOverviewList: ChartGaugeFYPData[] = useMemo(() => {
    return [
      {
        title: "FYP Đạt được",
        value: dataKPIOverViewTeam.fyp,
        max: dataKPIOverViewTeam.targetFyp,
        format: "number",
      },
      {
        title: "APE Đạt được",
        value: dataKPIOverViewTeam.ape,
        max: dataKPIOverViewTeam.targetApe,
        format: "number",
      },
    ];
  }, [dataKPIOverViewTeam]);

  const Content = useMemo(() => {
    switch (channel) {
      case "AGENCY":
        return Agency;
      case "BANCA":
        return Banca;
      case "BROKER":
        return Ifa;
      default:
        return null;
    }
  }, [channel]);

  const handleCloseFypLessTHan10Mil = () => {
    dispatch(setFypLessThan10Mil({ value: "", label: "" }));
  };

  const handleChangeFormSearch = (value: PerformanceFormSearchValue) => {
    dispatch(
      setPerformanceTeamForm({
        performanceTeamType: value.type,
        performanceTeamDate: value.date,
        performanceTeamErrorDate: value.error,
        performanceTeamQuarterly: value.quarterly,
      })
    );
  };

  return (
    <TeamPerformanceOverviewWrapper>
      <RelativeWrapper>
        <FlexBox className="hide-mobile">
          {chartOverviewList.map((item, index) => (
            <ChartGaugeFYP data={item} key={index} />
          ))}
        </FlexBox>
        <ChartGaugeFYPrapper className="hide-desktop">
          <ChartSlide>
            {chartOverviewList.map((item, index) => (
              <ChartGaugeFYP data={item} key={index} />
            ))}
          </ChartSlide>
        </ChartGaugeFYPrapper>
        <LoadingSection
          loading={Boolean(getKPIOverviewTeamLoading || getHierarchyLoading || userMeLoading || getKPILoading)}
          isFullContent
        />
      </RelativeWrapper>
      <NoteWrapper>
        Ghi chú: <br />
        KPI mục tiêu và thực đạt của FYP & APE đang được tính theo YTD
      </NoteWrapper>
      <FormSearchTemplate title="Thông tin">
        <FormSearchPerformance
          fyp={fypForFormSearch.team}
          error={performanceTeamErrorDate}
          tab={performanceTeamType}
          date={performanceTeamDate}
          quarterly={performanceTeamQuarterly}
          currentAgentCode={currentAgentOfTeam.agentCode}
          onChangeFormSearch={handleChangeFormSearch}
        >
          {Content ? <Content /> : null}
        </FormSearchPerformance>
      </FormSearchTemplate>
      {channel !== "BANCA" ? (
        <ModalFullPage
          show={Boolean(fypLessThan10Mil?.value)}
          title="Số lượng tư vấn có doanh số FYP < 10tr"
          onClose={handleCloseFypLessTHan10Mil}
        >
          <FYPLessThan10Mil />
        </ModalFullPage>
      ) : null}
    </TeamPerformanceOverviewWrapper>
  );
};

export default TeamPerformanceOverview;
