export const getEmailBancaTemplate = (bank: string, nameContest: string) => {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<style>
    p{
        margin:0px;
    }

    body{
         font-family: Arial, Helvetica, sans-serif;
    }
</style>
<body>
    <p>Các anh/chị dự án <span style='font-weight:700;'>${bank}</span> thân mến,</p>
    <p>Phòng Thi đua và Chính sách khen thưởng đã đưa lên CUBE kết quả của chương trình sau đây:</p>
    <p style='font-weight:700;'>${nameContest}</p>
    <p>Các anh/chị xin vui lòng đăng nhập CUBE/ Thi đua & Khen thưởng/ Thông tin thi đua để tra cứu chi tiết kết quả thi
        đua của chương trình.</p>
    <br />
    <p>Trân trọng,</p>
    <br />
    <p>Phòng Thi đua và Chính sách khen thưởng,</p>
    <p>Kênh Phân Phối Bảo Hiểm qua Ngân Hàng</p>
    <p>FWD Vietnam Life Insurance Company Limited</p>
    <p>Head Office: 11th Floor, Diamond Plaza Building</p>
    <p>34 Le Duan St., Dist. 1, HCMC, Vietnam </p>
    <p style='font-weight:700;'>www.fwd.com.vn</p>
</body>
</html>
    `;
};

export const getSubjectEmail = (bank: string) => {
  return `Tra cứu kết quả chương trình thi đua trên CUBE _ Dự án ${bank}`;
};
