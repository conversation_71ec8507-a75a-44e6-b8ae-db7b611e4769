import { File } from "@custom-types";
import { CreateFormProps, GetDetailContestContentStackData } from "@custom-types/contest";
import DropDownFieldset from "components/drop-down-fieldset";
import FormSearchTemplate from "components/form-search-template";
import DateInput from "components/input-date";
import InputFieldSet from "components/input-fileldset";
import ModalFullPage from "components/modal-full-page";
import UploadFileView from "components/upload-file-view";
import { useAppSelector } from "hooks/use-redux";
import { isNil } from "lodash";
import { useEffect, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import {
  downloadContestContentStackAction,
  getDetailContestContentStackAction,
} from "screens/thi-dua-va-khen-thuong/action";
import { statusContestList } from "sections/thi-dua-va-khen-thuong/utils";
import { formatValue } from "services/format-value";
import { Col, NoData, Row } from "styles";
import { CreateFormWrapper } from "./styled";

const ViewContestDetailBanca = ({ modal, typeContest, onClose }: CreateFormProps) => {
  const [detailData, setDetailData] = useState<GetDetailContestContentStackData>(null);

  const { show, data: dataFromTable, title } = modal;
  const isMOC = typeContest === "BANCA_MOC";

  const { username, channel } = useAppSelector((state) => state.rootReducer.user);

  const dispatch = useDispatch();

  useEffect(() => {
    if (modal.show) {
      dispatch(
        getDetailContestContentStackAction({
          body: {
            agentId: username,
            contestId: dataFromTable.contestId,
            typeChannel: channel,
            typeContest: typeContest,
          },
          callback: (data) => {
            setDetailData(data);
          },
        })
      );
    }
  }, [modal, username, channel, dataFromTable, typeContest, dispatch]);

  const statusColor = useMemo(() => {
    return statusContestList.find((item) => item.value === detailData?.status);
  }, [detailData]);

  const memoFile: File[] = useMemo(() => {
    if (!isNil(detailData?.fileMemo)) {
      return [
        {
          id: detailData?.fileMemo?.typeId,
          fileName: detailData?.fileMemo?.typeName,
        },
      ];
    } else {
      return [];
    }
  }, [detailData]);

  const resultFile: File[] = useMemo(() => {
    if (detailData?.fileResult?.length) {
      return detailData.fileResult.map((item) => ({
        id: item?.typeId,
        fileName: item?.typeName,
      }));
    } else {
      return [];
    }
  }, [detailData]);

  const handleDownloadFile = (file: File, fileType: "pdf" | "excel") => {
    dispatch(
      downloadContestContentStackAction({
        agentId: username,
        fileId: file.id,
        fileType: fileType,
        typeContest: typeContest,
      })
    );
  };

  const handleClose = () => {
    setDetailData(null);
    onClose();
  };

  return (
    <ModalFullPage show={show} status={statusColor as any} title={title} onClose={handleClose}>
      <CreateFormWrapper>
        <FormSearchTemplate title="Thông tin">
          <Row rowGap={20} rowGapMb={16} spaceBetween={8}>
            <Col md={4}>
              <DropDownFieldset
                required
                placeholder="Ngân hàng"
                value={{
                  value: detailData?.subChannelId,
                  label: detailData?.subChannelName,
                }}
                disabled
              />
            </Col>
            <Col md={4}>
              <DropDownFieldset
                required
                placeholder="Chức danh"
                value={{
                  value: detailData?.designationCode,
                  label: detailData?.designationName,
                }}
                disabled
              />
            </Col>
            <Col md={4}>
              <InputFieldSet
                required
                placeholder={isMOC ? "Tên báo cáo" : "Tên chương trình thi đua"}
                value={detailData?.contestName}
                disabled
              />
            </Col>
          </Row>
          <Row rowGap={20} rowGapMb={16} spaceBetween={8}>
            <Col md={4}>
              <DateInput
                required
                placeholder="Ngày bắt đầu"
                value={formatValue(detailData?.startDate, "date")}
                disabled
              />
            </Col>
            <Col md={4}>
              <DateInput
                required
                placeholder="Ngày kết thúc"
                value={formatValue(detailData?.endDate, "date")}
                disabled
              />
            </Col>
            <Col md={4}>
              <DateInput
                required
                placeholder="Ngày chốt kết quả"
                value={formatValue(detailData?.closeDate, "date")}
                disabled
              />
            </Col>
          </Row>
          <h6 className="h7 mb-20">Thông tin Memo</h6>
          <Row rowGap={20} rowGapMb={16}>
            <Col md={12}>
              <InputFieldSet placeholder="Mã memo" value={detailData?.memoId} required disabled />
            </Col>
          </Row>
          <UploadFileView
            onlyView
            buttonAddTitle="Thêm file memo"
            value={memoFile}
            onDownloadFile={(file) => handleDownloadFile(file, "pdf")}
          />
        </FormSearchTemplate>
        <FormSearchTemplate title={isMOC ? "Kết quả báo cáo" : "Kết quả thi đua"} className="mt-24">
          {resultFile?.length ? (
            <UploadFileView
              onlyView
              buttonAddTitle="Thêm file memo"
              value={resultFile}
              onDownloadFile={(file) => handleDownloadFile(file, "excel")}
            />
          ) : (
            <NoData>Không có dữ liệu</NoData>
          )}
        </FormSearchTemplate>
      </CreateFormWrapper>
    </ModalFullPage>
  );
};

export default ViewContestDetailBanca;
