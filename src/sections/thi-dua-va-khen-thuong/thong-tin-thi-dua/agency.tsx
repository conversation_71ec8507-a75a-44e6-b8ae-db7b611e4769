import { EMPTY_DATA, ERROR, ERROR_API_MESSAGE } from "@constants/message";
import { ValueLabel } from "@custom-types";
import { DataTable, TableConfig } from "@custom-types/config-table";
import {
  ContestListAgencyInput,
  ContestListAgencyOutput,
  GetDesignationListAgencyInput,
  ListDropDownSearchContest,
} from "@custom-types/contest";
import { getContestListAgency, getDesignationListAgency } from "api/contest";
import { Alert } from "components/alert";
import DropDownFieldset from "components/drop-down-fieldset";
import DropdownMultipleFieldSet from "components/drop-down-multiple-fieldset";
import FormSearchTemplate from "components/form-search-template";
import DateInput from "components/input-date";
import InputFieldSet from "components/input-fileldset";
import TableMobileSeverSide from "components/table-mobile/table-mobile-server-side";
import TableServerSide, { TablePaginationProps } from "components/table/table-sever-side";
import useActionApi from "hooks/use-action-api";
import { useAppSelector } from "hooks/use-redux";
import { cloneDeep, get } from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import { setContestTokenAgency } from "screens/thi-dua-va-khen-thuong/slice";
import { ShowCreateFormProps } from "screens/thi-dua-va-khen-thuong/thong-tin-thi-dua";
import StatusOfTableContest from "sections/thi-dua-va-khen-thuong/status-agency";
import { formatValueList } from "sections/thi-dua-va-khen-thuong/utils";
import { formatValueTable } from "services/format-value";
import { formatDateRequest, getDateFrom, getDateTo, handleCheckValidateMonthForm } from "services/untils";
import { PolicyNo, RowItem } from "styles";
import { ContestWrapper } from "./styled";
import CreateFormAgency from "./view-detail-modal/agency";

const config: TableConfig[] = [
  { key: "ContestCode", show: true, type: "string", label: "Mã thi đua" },
  { key: "ContestName", show: true, type: "rich-text", label: "Tên chương trình thi đua" },
  { key: "DesignationCode", show: true, type: "string", label: "Chức danh" },
  { key: "StartDate", show: true, type: "date", label: "Ngày bắt đầu", sort: true },
  { key: "EndDate", show: true, type: "date", label: "Ngày kết thúc", sort: true },
  { key: "ContestFinalResultDate", show: true, type: "date", label: "Ngày chốt kết quả", sort: true },
  { key: "ContestStatus", show: true, type: "string", label: "Tình trạng" },
];

export const statusTableContestAgency: Record<number, ValueLabel> = {
  2: { value: 2, label: "Đang diễn ra" },
  3: { value: 3, label: "Chờ chốt kết quả" },
  4: { value: 4, label: "Kết thúc" },
};

// ContestStatus = 2 = Đang diễn ra
// ContestStatus = 3 = Chờ chốt kết quả
// ContestStatus = 4 = Kết thúc
const contestDropdownList: ValueLabel[] = [
  { value: "ALL", label: "Tất cả" },
  { value: 2, label: "Đang diễn ra" },
  { value: 3, label: "Chờ chốt kết quả" },
  { value: 4, label: "Kết thúc" },
];

const ContestAgency = () => {
  const [formSearch, setFormSearch] = useState<ContestListAgencyInput>({
    contestCode: "",
    designation: [],
    contestName: "",
    curTimeGToken: "",
    pageIndex: 0,
    pageSize: 10,
    tokenInfo: null,
    contestStatus: { value: "ALL", label: "Tất cả" },
    startDate: getDateFrom(),
    endDate: getDateTo(),
  });
  const [error, setError] = useState({
    startDate: null,
    endDate: null,
  });
  const [data, setData] = useState([]);
  const [designationList, setDesignationList] = useState([]);
  const [pagination, setPagination] = useState<TablePaginationProps>({
    currentPage: 0,
    pageSize: 0,
    totalItem: 0,
  });
  const [viewDetailModal, setViewDetailModal] = useState<ShowCreateFormProps<any>>({
    show: false,
    title: "",
    type: "",
    data: null,
  });

  const dispatch = useDispatch();

  const {
    user: { designation, channel },
    loading: { getListContestTableLoading, getSubChannelContestLoading },
  } = useAppSelector((state) => state.rootReducer);

  const actionGetContestList = useActionApi<ContestListAgencyInput, ContestListAgencyOutput>(getContestListAgency);
  const actionGetDesignation = useActionApi<GetDesignationListAgencyInput, ListDropDownSearchContest[]>(
    getDesignationListAgency
  );

  useEffect(() => {
    if (channel && designation) {
      actionGetDesignation({
        loading: {
          type: "local",
          name: "getSubChannelContestLoading",
        },
        body: {
          typeChannel: channel,
        },
      }).then(({ data }) => {
        const index = data.findIndex((item) => item.name.split("/ ").includes(designation));
        const result = index > -1 ? index : data.length;

        setDesignationList(formatValueList(data.slice(0, result + 1)?.reverse()));
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [channel, designation]);

  useEffect(() => {
    if (designation && designationList?.length) {
      const currentOption = designationList.find((item) => item.label.split("/ ").includes(designation));

      handleChangeData("designation", currentOption ? [currentOption] : []);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [designation, designationList]);

  const disableSubmit = useMemo(() => {
    return (
      Boolean(getListContestTableLoading) ||
      Object.values(error).findIndex((value) => value) > -1 ||
      !formSearch.designation.length
    );
  }, [error, getListContestTableLoading, formSearch.designation]);

  const handleChangeData = (name: string, value: any) => {
    setFormSearch((pre: any) => ({
      ...pre,
      [name]: value,
    }));

    if (name === "startDate" || name === "endDate") {
      const newError = handleCheckValidateMonthForm(name, value, formSearch.startDate, 3);
      setError((pre: any) => ({ ...pre, ...newError }));
    }
  };

  const handleBlurData = (name: string, valueDateFrom: string) => {
    const newError = handleCheckValidateMonthForm(name, formSearch.endDate, valueDateFrom, 3);
    setError((pre: any) => ({ ...pre, ...newError }));
  };

  const handleSubmit = useCallback(
    (page: number, pageSize: number) => {
      let payload: ContestListAgencyInput = cloneDeep(formSearch);

      payload.designation = formSearch.designation.map((item: ValueLabel) => item.value).join(",");
      payload.startDate = formatDateRequest(payload.startDate);
      payload.endDate = formatDateRequest(payload.endDate);
      payload.contestStatus = formSearch.contestStatus.value !== "ALL" ? formSearch.contestStatus.value : null;

      payload.pageIndex = page;
      payload.pageSize = pageSize;

      actionGetContestList({
        body: payload,
        loading: {
          type: "local",
          name: "getListContestTableLoading",
        },
      })
        .then(({ data }) => {
          if (!data?.lstContest?.length) {
            Alert("WARNING", EMPTY_DATA);
          }

          setData(data?.lstContest ?? []);
          setPagination({
            currentPage: data.pageIndex + 1,
            pageSize: data.pageSize,
            totalItem: data.totalItems,
          });

          dispatch(
            setContestTokenAgency({
              curTimeGToken: data?.curTimeGToken,
              tokenInfo: data?.tokenInfo,
            })
          );
        })
        .catch((error) => {
          console.log("error", error);
          Alert(ERROR, ERROR_API_MESSAGE);
        });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [designation, formSearch]
  );

  const formatValueTablePolicy = (data: any, config: TableConfig) => {
    switch (config.key) {
      case "ContestName": {
        return (
          <PolicyNo primary onClick={() => handleOpenCreateForm(data, config)}>
            {data[config.key]}
          </PolicyNo>
        );
      }
      case "ContestStatus": {
        const status = statusTableContestAgency?.[data?.[config.key]];
        return <StatusOfTableContest className="sm-mb-4" status={status?.value} label={status?.label} />;
      }
      default:
        return formatValueTable(data, config);
    }
  };

  const handleOpenCreateForm = useCallback((data: any, config: any) => {
    let newData: ShowCreateFormProps<any> = {};

    newData.show = true;
    newData.data = data;
    newData.title = data[config.key];
    setViewDetailModal(newData);
  }, []);

  const dataTable: DataTable[][] = useMemo(() => {
    return data?.map((item) => {
      return config.map((config) => ({
        config: config,
        node: formatValueTablePolicy(item, config),
        originData: get(item, [config.key]),
      }));
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, config]);

  //Only for Agency
  const handleChangePageAgency = (page: number, pageSize: number) => {
    handleSubmit(page - 1, pageSize);
  };

  return (
    <ContestWrapper>
      <FormSearchTemplate
        title="Thông tin"
        onSubmit={() => handleSubmit(0, 10)}
        disableSubmit={disableSubmit || Boolean(getSubChannelContestLoading)}
        loading={Boolean(getListContestTableLoading || getSubChannelContestLoading)}
      >
        <RowItem>
          <InputFieldSet
            placeholder="Mã thi đua"
            value={formSearch?.contestCode}
            onChange={(e) => handleChangeData("contestCode", e.target.value)}
          />
          <InputFieldSet
            placeholder="Tên chương trình thi đua"
            value={formSearch?.contestName}
            onChange={(e) => handleChangeData("contestName", e.target.value)}
          />
          <DropdownMultipleFieldSet
            placeholder="Chức danh"
            value={formSearch?.designation}
            list={designationList}
            loading={Boolean(getSubChannelContestLoading)}
            showSelectAll
            onChange={(value) => handleChangeData("designation", value)}
          />
        </RowItem>
        <RowItem>
          <DropDownFieldset
            placeholder="Tình trạng"
            value={formSearch?.contestStatus}
            list={contestDropdownList}
            onChange={(value) => handleChangeData("contestStatus", value)}
          />
          <DateInput
            placeholder="Từ (Ngày bắt đầu thi đua)"
            value={formSearch?.startDate}
            error={error?.startDate}
            onBlur={(value) => handleBlurData("endDate", value)}
            onChange={(value) => handleChangeData("startDate", value)}
          />
          <DateInput
            placeholder="Đến (Ngày bắt đầu thi đua)"
            value={formSearch?.endDate}
            error={error?.endDate}
            onChange={(value) => handleChangeData("endDate", value)}
          />
        </RowItem>
      </FormSearchTemplate>

      <h6 className="h7 mb-16 mt-24">Danh sách thi đua</h6>
      <TableServerSide
        data={dataTable}
        config={config}
        showConfig={false}
        loading={Boolean(getListContestTableLoading)}
        pagination={pagination}
        onChangePage={handleChangePageAgency}
      />
      <TableMobileSeverSide
        config={config}
        data={dataTable}
        loading={Boolean(getListContestTableLoading)}
        pagination={pagination}
        onChangePage={handleChangePageAgency}
      />

      <CreateFormAgency
        modal={viewDetailModal}
        onClose={() => setViewDetailModal({ show: false, title: null, data: null })}
      />
    </ContestWrapper>
  );
};

export default ContestAgency;
