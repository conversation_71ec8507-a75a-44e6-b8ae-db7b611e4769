import { ERROR, ERROR_API_MESSAGE } from "@constants/message";
import { File } from "@custom-types";
import { ContestAC2023byTypeDetailOutput, GetDetailContestOf2023Input } from "@custom-types/contest";
import { DownloadDocumentInput, DownloadDocumentOutput } from "@custom-types/report-document";
import { getDetailContestAC2023 } from "api/contest";
import { downloadDocumentFile } from "api/report-document";
import { Alert } from "components/alert";
import FormSearchTemplate from "components/form-search-template";
import DateInput from "components/input-date";
import InputFieldSet from "components/input-fileldset";
import ModalFullPage from "components/modal-full-page";
import UploadFileView from "components/upload-file-view";
import useActionApi from "hooks/use-action-api";
import { useAppSelector } from "hooks/use-redux";
import { isNil } from "lodash";
import { useEffect, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import { downloadContestContentStackAction } from "screens/thi-dua-va-khen-thuong/action";
import { CreateFormWrapper } from "sections/thi-dua-va-khen-thuong/thong-tin-thi-dua/view-detail-modal/styled";
import { formatValue } from "services/format-value";
import { downloadFileBase64 } from "services/untils";
import { Col, NoData, Row } from "styles";

export interface DetailContestAC2023Modal {
  show: boolean;
  title: string;
  typeContest: string;
  contestId: string;
}

interface DetailContestACOf2023Props {
  modal: DetailContestAC2023Modal;
  onClose: () => void;
}

const DetailContestACOf2023 = ({ modal, onClose }: DetailContestACOf2023Props) => {
  const [detailData, setDetailData] = useState<ContestAC2023byTypeDetailOutput>(null);

  const { show, title, typeContest, contestId } = modal;
  const { username } = useAppSelector((state) => state.rootReducer.user);

  const dispatch = useDispatch();
  const actionGetDetailContestACOf2023 = useActionApi<GetDetailContestOf2023Input, ContestAC2023byTypeDetailOutput>(
    getDetailContestAC2023
  );
  const actionDownloadDocumentFile = useActionApi<DownloadDocumentInput, DownloadDocumentOutput>(downloadDocumentFile);

  useEffect(() => {
    if (show) {
      actionGetDetailContestACOf2023({
        loading: {
          type: "global",
          name: "getDetailContestLoading",
        },
        body: {
          agentId: username,
          contestId: contestId,
          typeContestId: typeContest,
        },
      })
        .then(({ data }) => {
          setDetailData(data);
        })
        .catch((err) => {
          console.log(err);
          Alert(ERROR, ERROR_API_MESSAGE);
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [contestId, show, typeContest, username]);

  const memoFile: File[] = useMemo(() => {
    if (!isNil(detailData?.memoFileId)) {
      return [
        {
          id: detailData?.memoFileId,
          fileName: detailData?.memoFileName,
        },
      ];
    } else {
      return [];
    }
  }, [detailData]);

  const resultFile: File[] = useMemo(() => {
    if (detailData?.listFileResults?.length) {
      return detailData.listFileResults.map((item) => ({
        id: item?.fileUploadId,
        fileName: item?.fileName,
      }));
    } else {
      return [];
    }
  }, [detailData]);

  const handleDownloadFile = (file: File, fileType: "pdf" | "excel") => {
    if (fileType === "pdf") {
      dispatch(
        downloadContestContentStackAction({
          agentId: username,
          fileId: file.id,
          fileType: "pdf",
        })
      );
    } else {
      actionDownloadDocumentFile({
        loading: {
          type: "global",
          name: "downloadDocumentLoading",
        },
        body: {
          agentId: username,
          fileId: file.id,
        },
      })
        .then(({ data }) => {
          if (data) {
            if (data.fileContent) {
              downloadFileBase64(data.fileContent, data.fileName, data.mimeType);
            } else {
              Alert(ERROR, "Không tìm thấy thông tin");
            }
          }
        })
        .catch((err) => {
          console.log(err);
          Alert(ERROR, ERROR_API_MESSAGE);
        });
    }
  };

  const handleClose = () => {
    setDetailData(null);
    onClose();
  };

  return (
    <ModalFullPage show={show} title={title} onClose={handleClose}>
      <CreateFormWrapper>
        <FormSearchTemplate title="Thông tin">
          <Row rowGap={20} rowGapMb={16} spaceBetween={8}>
            <Col md={4}>
              <InputFieldSet required placeholder="Mã thi đua" value={detailData?.contestTitle} disabled />
            </Col>
            <Col md={4}>
              <InputFieldSet required placeholder="Tên chương trình thi đua" value={detailData?.contestName} disabled />
            </Col>
            <Col md={4}>
              <DateInput
                required
                placeholder="Ngày phát hành"
                value={formatValue(detailData?.issueDate, "date")}
                disabled
              />
            </Col>
          </Row>

          <h6 className="h7 mb-20">Thông tin Memo</h6>
          <Row rowGap={20} rowGapMb={16}>
            <Col md={12}>
              <InputFieldSet placeholder="Mã memo" value={detailData?.memoId} required disabled />
            </Col>
          </Row>
          <UploadFileView
            onlyView
            buttonAddTitle="Thêm file memo"
            value={memoFile}
            onDownloadFile={(file) => handleDownloadFile(file, "pdf")}
          />
        </FormSearchTemplate>
        <FormSearchTemplate title="Tiến độ & Kết quả" className="mt-24">
          {resultFile?.length ? (
            <UploadFileView
              onlyView
              buttonAddTitle="Thêm file memo"
              value={resultFile}
              onDownloadFile={(file) => handleDownloadFile(file, "excel")}
            />
          ) : (
            <NoData>Không có dữ liệu</NoData>
          )}
        </FormSearchTemplate>
      </CreateFormWrapper>
    </ModalFullPage>
  );
};

export default DetailContestACOf2023;
