import { DropdownFormData, RegisterTripData } from "@custom-types/journey";
import DropDownFieldset from "components/drop-down-fieldset";
import DateInput from "components/input-date";
import InputFieldSet from "components/input-fileldset";
import Note from "components/note";
import RadioButton from "components/radio";
import UploadFile from "components/upload-file";
import { convertIdentityType, typeOnlyNumber } from "services/untils";
import { Col, Row } from "styles";
import { formatDropdownForm } from "../../..";
import { LabelTicket, UploadRelationshipWrapper } from "../../../styled";

interface TicketProps {
  ticketDataForm?: RegisterTripData;
  error?: any;
  warning?: any;
  dropdownForm: DropdownFormData;
  onChangeTicket?: (name: string, value: any) => void;
}

const noteList = [
  "Hộ chiếu/ CMND/ CCCD đính kèm đủ 2 mặt (mặt trước và mặt sau).",
  "Hình ảnh dung dượng tối đa là 5MB.",
  "Ảnh chụp trực tiếp từ bản gốc, r<PERSON> n<PERSON>t, không mờ, bị tẩy xóa.",
];

const noteList2 = [
  "Hình ảnh dung dượng tối đa là 5MB.",
  "Ảnh chụp trực tiếp từ bản gốc, rõ nét, không mờ, bị tẩy xóa.",
];

const ConferenceFrom2DaysTicket2 = ({ ticketDataForm, error, warning, dropdownForm, onChangeTicket }: TicketProps) => {
  return (
    <>
      <Row rowGap={16} rowGapMb={16} spaceBetween={8} className="mt-16">
        <Col md={6}>
          <DropDownFieldset
            required
            list={formatDropdownForm(dropdownForm?.FOOD)}
            value={ticketDataForm?.food}
            placeholder="Ăn uống"
            onChange={(value) => onChangeTicket("food", value)}
          />
        </Col>
        <Col md={6}>
          <DropDownFieldset
            required
            list={formatDropdownForm(dropdownForm?.SIZECLOTHES)}
            value={ticketDataForm?.sizeClothes}
            placeholder="Áo thun"
            onChange={(value) => onChangeTicket("sizeClothes", value)}
          />
        </Col>
      </Row>
      <LabelTicket className="h7">Thông tin cá nhân</LabelTicket>
      <Row rowGap={16} rowGapMb={16}>
        <Col md={6}>
          <InputFieldSet
            placeholder="Mã số Tư vấn của người thân (Nếu có)"
            value={ticketDataForm?.relativeAgentId}
            error={error?.relativeAgentId}
            onChange={(e) => onChangeTicket("relativeAgentId", e.target.value)}
          />
        </Col>
        <Col md={6}>
          <InputFieldSet
            required
            placeholder="Tên người thân được mời"
            value={ticketDataForm?.relativeName}
            error={error?.relativeName}
            onChange={(e) => onChangeTicket("relativeName", e.target.value)}
          />
        </Col>
      </Row>
      <Row rowGap={16} rowGapMb={16}>
        <Col md={6}>
          <DropDownFieldset
            required
            placeholder="Mối quan hệ với Tư vấn được mời"
            list={formatDropdownForm(dropdownForm?.RELATIVE_RELATIONSHIP)}
            value={ticketDataForm?.relativeRelationship}
            onChange={(value) => onChangeTicket("relativeRelationship", value)}
          />
        </Col>
        <Col md={6}>
          <DropDownFieldset
            required
            placeholder="Giới tính"
            list={formatDropdownForm(dropdownForm?.GENDER)}
            value={ticketDataForm?.relativeGender}
            onChange={(value) => onChangeTicket("relativeGender", value)}
          />
        </Col>
        <Col md={12}>
          {ticketDataForm?.relativeRelationship?.value === "ELSE" && (
            <div className="mt-16">
              <InputFieldSet
                required
                type="textarea"
                placeholder="Ghi chú mối quan hệ với Tư vấn được mời"
                value={ticketDataForm?.otherRelativeRelationship}
                error={error.otherRelativeRelationship}
                onChange={(e) => onChangeTicket("otherRelativeRelationship", e.target.value)}
              />
            </div>
          )}
        </Col>
      </Row>
      <Row rowGapMb={16} rowGap={16}>
        <Col md={12}>
          <DropDownFieldset
            required
            placeholder="Hộ chiếu/CMND/CCCD"
            list={formatDropdownForm(dropdownForm?.IDENTITYTYPE)}
            value={ticketDataForm?.identityType}
            onChange={(value) => onChangeTicket("identityType", value)}
          />
        </Col>
      </Row>
      {ticketDataForm?.identityType?.value ? (
        <>
          <Row rowGap={16} rowGapMb={16}>
            <Col md={6}>
              <InputFieldSet
                required
                placeholder={
                  ticketDataForm?.identityType?.value
                    ? `Số ${convertIdentityType[ticketDataForm?.identityType?.value] ?? ""}`
                    : "Số Hộ chiếu/CMND/CCCD"
                }
                value={ticketDataForm?.identityId}
                error={error?.identityId}
                onChange={({ target: { value } }) =>
                  onChangeTicket(
                    "identityId",
                    ticketDataForm?.identityType?.value === "HC" ? value : typeOnlyNumber(value)
                  )
                }
              />
            </Col>
            <Col md={6}>
              <DateInput
                required
                placeholder="Ngày cấp"
                value={ticketDataForm?.identityIssueDate}
                error={error?.identityIssueDate}
                onChange={(value) => onChangeTicket("identityIssueDate", value)}
              />
            </Col>
          </Row>
          <Row rowGap={16} rowGapMb={16}>
            <Col md={6}>
              <InputFieldSet
                required
                placeholder="Nơi cấp"
                value={ticketDataForm?.identityIssuePlace}
                error={error?.identityIssuePlace}
                onChange={(e) => onChangeTicket("identityIssuePlace", e.target.value)}
              />
            </Col>
            {ticketDataForm?.identityType?.value !== "CMND" && (
              <Col md={6}>
                <DateInput
                  required
                  placeholder="Ngày hết hạn"
                  value={ticketDataForm?.identityExpiryDate}
                  error={error?.identityExpiryDate}
                  warning={warning?.identityExpiryDate}
                  onChange={(value) => onChangeTicket("identityExpiryDate", value)}
                />
              </Col>
            )}
          </Row>
          <UploadRelationshipWrapper>
            <label className="label-1">
              {ticketDataForm?.identityType?.value
                ? `Hình ảnh ${convertIdentityType[ticketDataForm?.identityType?.value] ?? ""}`
                : "Hình ảnh Hộ chiếu/CMND/CCCD"}
              <span className="color-error">*</span>
            </label>
            <UploadFile
              maxSize={5}
              value={ticketDataForm?.filesTicket}
              accept={[".png", ".jpeg", ".jpg"]}
              multiple
              error={error?.filesTicket}
              onChange={(file) => onChangeTicket("filesTicket", file)}
            />
          </UploadRelationshipWrapper>
          <Note content={noteList} backgroundColor="#EDEFF0" title="Lưu ý:" type="list" className="mt-16" />
        </>
      ) : null}
      <LabelTicket className="h7">Thông tin liên hệ</LabelTicket>
      <Row rowGap={16} rowGapMb={16}>
        <Col md={6}>
          <InputFieldSet
            required
            placeholder="Số điện thoại"
            value={ticketDataForm?.mobileNumber}
            error={error?.mobileNumber}
            onChange={({ target: { value } }) => onChangeTicket("mobileNumber", typeOnlyNumber(value))}
          />
        </Col>
        <Col md={6}>
          <InputFieldSet
            required
            placeholder="Địa chỉ"
            value={ticketDataForm?.address}
            type="string"
            error={error?.address}
            onChange={(e) => onChangeTicket("address", e.target.value)}
          />
        </Col>
      </Row>
      <LabelTicket className="h7">Thông tin liên hệ trong trường hợp khẩn cấp </LabelTicket>
      <Row rowGap={16} rowGapMb={16}>
        <Col md={6}>
          <InputFieldSet
            required
            placeholder="Họ và tên"
            value={ticketDataForm?.contactName}
            error={error?.contactName}
            onChange={(e) => onChangeTicket("contactName", e.target.value)}
          />
        </Col>
        <Col md={6}>
          <DropDownFieldset
            required
            placeholder="Mối quan hệ"
            list={formatDropdownForm(dropdownForm?.RELATIVE_RELATIONSHIP)}
            value={ticketDataForm?.contactRelationship}
            error={error?.contactRelationship}
            onChange={(value) => onChangeTicket("contactRelationship", value)}
          />
        </Col>
      </Row>
      {ticketDataForm?.contactRelationship?.value === "ELSE" && (
        <Row className="mb-20">
          <Col md={12}>
            <InputFieldSet
              placeholder="Ghi chú mối quan hệ"
              type="textarea"
              value={ticketDataForm?.otherRelativeRelationshipUrgent}
              required
              error={error?.otherRelativeRelationshipUrgent}
              onChange={(e) => onChangeTicket("otherRelativeRelationshipUrgent", e.target.value)}
            />
          </Col>
        </Row>
      )}
      <Row rowGap={16} rowGapMb={16}>
        <Col md={6}>
          <InputFieldSet
            required
            placeholder="Số điện thoại"
            value={ticketDataForm?.contactPhone}
            error={error?.contactPhone}
            onChange={({ target: { value } }) => onChangeTicket("contactPhone", typeOnlyNumber(value))}
          />
        </Col>
        <Col md={6}>
          <InputFieldSet
            required
            placeholder="Địa chỉ"
            value={ticketDataForm?.contactAddress}
            type="string"
            error={error?.contactAddress}
            onChange={(e) => onChangeTicket("contactAddress", e.target.value)}
          />
        </Col>
      </Row>
      <LabelTicket className="h7">Phòng ở tại khách sạn</LabelTicket>
      <p className="body-4 mt-16">
        Công ty sẽ xếp phòng ở theo tiêu chuẩn 2 người/phòng theo trình tự. Nếu có yêu cầu ưu tiên xếp chung phòng với
        Đại lý khác có cùng chuyến đi thì điền thông tin bên dưới:
      </p>
      <LabelTicket className="h7 text-medium">Bạn có đồng ý với sự sắp xếp của công ty?</LabelTicket>
      <div>
        <RadioButton
          haveBorder={false}
          selected={ticketDataForm?.roomOnHotel === "Y"}
          label="Có"
          onChange={() => onChangeTicket("roomOnHotel", "Y")}
        />
        <RadioButton
          haveBorder={false}
          selected={ticketDataForm?.roomOnHotel === "N"}
          label="Không"
          onChange={() => onChangeTicket("roomOnHotel", "N")}
        />
      </div>
      {ticketDataForm?.roomOnHotel === "N" && (
        <>
          <LabelTicket className="h7 text-medium">Tôi yêu cầu được sắp xếp ở cùng phòng khách sạn với:</LabelTicket>
          <Row rowGap={16} rowGapMb={16}>
            <Col md={4}>
              <InputFieldSet
                placeholder="Tên TVTC"
                value={ticketDataForm?.roomateAgentName}
                error={error?.roomateAgentName}
                onChange={(e) => onChangeTicket("roomateAgentName", e.target.value)}
              />
            </Col>
            <Col md={4}>
              <InputFieldSet
                placeholder="Mã TVTC"
                value={ticketDataForm?.roomateAgentId}
                error={error?.roomateAgentId}
                onChange={(e) => onChangeTicket("roomateAgentId", e.target.value)}
              />
            </Col>
            <Col md={4}>
              <InputFieldSet
                placeholder="Văn phòng"
                value={ticketDataForm?.roomateOffice}
                error={error?.roomateOffice}
                onChange={(e) => onChangeTicket("roomateOffice", e.target.value)}
              />
            </Col>
          </Row>
          <UploadRelationshipWrapper>
            <label className="label-1">Giấy tờ chứng minh mối quan hệ</label>
            <p className="body-4">
              Lưu ý: Nếu Nam & Nữ đăng ký ở chung phòng vui lòng nộp kèm theo giấy chứng minh là vợ chồng.
            </p>
            <UploadFile
              multiple
              maxSize={5}
              value={ticketDataForm?.filesTicketRelative}
              accept={[".png", ".jpeg", ".jpg"]}
              error={error?.filesTicketRelative}
              onChange={(file) => onChangeTicket("filesTicketRelative", file)}
            />
          </UploadRelationshipWrapper>
          <Note content={noteList2} backgroundColor="#EDEFF0" type="list" title="Lưu ý:" className="mt-16" />
        </>
      )}
    </>
  );
};

export default ConferenceFrom2DaysTicket2;
