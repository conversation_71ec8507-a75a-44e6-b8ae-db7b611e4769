import { ConfirmJourneyDetailData, DropdownJourneyFormDataValue } from "@custom-types/journey";

export const getLabelListJourney = (
  list: DropdownJourneyFormDataValue[],
  key: string,
  data: ConfirmJourneyDetailData
) => {
  if (key === "relativeRelationship" || key === "contactRelationship") {
    if (list?.findIndex((i) => i.valueId === data[key]) === -1) {
      return data[key];
    }
  }
  return list?.find((i) => i.valueId === data[key])?.valueName ?? "";
};
