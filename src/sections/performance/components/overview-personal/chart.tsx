import { ChartDataProps } from "@custom-types/performance";
import { EChartsOption, GaugeSeriesOption } from "echarts";
import dynamic from "next/dynamic";
const EChartsReact = dynamic(() => import("echarts-for-react"), { ssr: false });
import useNumberAnimation from "hooks/use-numnber-animation";
import useWindowResize from "hooks/use-window-resize";
import { useMemo } from "react";
import { calculatePercent, formatNumber, formatNumberToMoneyString } from "services/untils";
import { useTheme } from "styled-components";
import { ChartDetail, ChartWrapper, Description, Detail, Legend, Line, Range } from "./styled";

const ChartGaugeTwoLine = ({ data, length }: { data: ChartDataProps; length: number }) => {
  const theme = useTheme();
  const size = useWindowResize();
  const fypValue = useNumberAnimation(data.fyp);
  const apeValue = useNumberAnimation(data.ape);

  const font = (size: number, weight: number) => {
    return {
      fontSize: size,
      fontWeight: weight,
      fontFamily: "FWD",
      color: theme.color.text.body,
    };
  };

  const dataConfig: GaugeSeriesOption["data"] = useMemo(() => {
    return [
      {
        value: calculatePercent(data.fyp, data.fypMax, 100),
        name: data.title,
        title: {
          ...font(25, 700),
          offsetCenter: ["0%", "-20%"],
        },
      },
      {
        value: 100,
        title: {
          show: false,
        },
        detail: {
          show: false,
        },
      },
      {
        value: calculatePercent(data.ape, data.apeMax, 100),
        name: data.date,
        title: {
          fontSize: 12,
          fontWeight: 400,
          fontFamily: "FWD",
          color: theme.color.text.body,
          offsetCenter: ["0%", "0%"],
        },
      },
    ];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  const option: EChartsOption = useMemo(() => {
    return {
      series: [
        {
          type: "gauge",
          center: ["50%", "50%"],
          radius: size.width >= 768 ? "80%" : "90%",
          min: 0,
          max: 100,
          pointer: {
            show: false,
          },
          progress: {
            show: true,
            overlap: false,
            roundCap: false,
            clip: false,
          },
          color: ["#F47721", "#ffffff", "#F3BB90"],
          axisLine: {
            lineStyle: {
              width: 30,
              color: [[1, theme.color.status.grey_50]],
            },
          },
          splitLine: {
            show: false,
            distance: 0,
            length: 10,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: false,
            distance: 50,
          },
          title: {
            fontSize: 14,
          },
          detail: {
            show: false,
          },
          data: dataConfig,
        },
      ],
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataConfig, data, size?.width]);

  return (
    <ChartWrapper length={length}>
      <EChartsReact option={option} notMerge={true} lazyUpdate={true} />
      <Description>
        <Range>
          <p>0</p>
          <Line>
            <p>APE</p>
          </Line>
          <p>{formatNumberToMoneyString(data.fypMax).result}</p>
        </Range>
        <Range>
          <p>0</p>
          <Line>
            <p>FYP</p>
          </Line>
          <p>{formatNumberToMoneyString(data.apeMax).result}</p>
        </Range>
      </Description>
      <ChartDetail>
        <Detail>
          {/* {data.fyp === data.fypMax ? <Icons icon="crown" /> : null} */}
          <h6>{formatNumber(fypValue)}</h6>
          <Legend className="body-5" color="#F47721">
            FYP (VND)
          </Legend>
        </Detail>
        <Detail>
          {/* {data.ape === data.apeMax ? <Icons icon="crown" /> : null} */}
          <h6>{formatNumber(apeValue)}</h6>
          <Legend className="body-5" color="#F3BB90">
            APE (VND)
          </Legend>
        </Detail>
      </ChartDetail>
    </ChartWrapper>
  );
};

export default ChartGaugeTwoLine;
