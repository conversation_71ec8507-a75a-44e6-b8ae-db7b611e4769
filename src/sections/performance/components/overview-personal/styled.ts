import styled from "styled-components";
import { device } from "styles/media";

export const OverviewPersonalWrapper = styled.div<{ isJustifyCenter?: boolean }>`
  display: flex;
  justify-content: ${({ isJustifyCenter }) => (isJustifyCenter ? "center" : "flex-start")};
  position: relative;
  margin-top: 20px;

  @media ${device.mobile} {
    justify-content: flex-start;
  }
`;

export const ChartWrapper = styled.div<{ length: number }>`
  width: ${({ length }) => `calc(100% / ${length} ) `};
  min-width: 350px;

  display: flex;
  flex-direction: column;
  overflow: hidden;

  .echarts-for-react {
    height: 350px !important;
  }

  @media ${device.mobile} {
    min-width: 100%;
    max-width: 100%;
    justify-content: center;

    .echarts-for-react {
      height: 300px !important;
    }
  }
`;

export const Description = styled.div`
  width: 100%;
  margin-top: -82px;

  display: flex;
  flex-direction: column;
  align-items: center;

  @media ${device.mobile} {
    margin-top: -70px;
  }
`;

export const Range = styled.div`
  width: 100%;
  max-width: 200px;

  display: flex;
  justify-content: space-between;

  position: relative;

  p {
    font-weight: 500;
    font-size: 12px;
    line-height: 125%;

    color: ${({ theme }) => theme.color.status.grey_darker};
  }

  :first-child {
    padding: 0px 20px;
  }

  @media ${device.mobile} {
    max-width: 170px;

    p {
      font-size: 10px;
    }
  }
`;

export const Line = styled.div`
  margin: 0px 5px;
  flex: 1;

  position: relative;
  text-align: center;

  p {
    width: 25px;
    margin: 0px auto;

    background: #ffffff;
  }

  :after {
    content: "";
    position: absolute;
    top: 50%;
    left: 1px;
    z-index: -1;

    width: 100%;
    height: 1px;

    background: ${({ theme }) => theme.color.status.grey};
  }
`;

export const ChartDetail = styled.div`
  margin-top: 24px;

  display: flex;
  justify-content: center;
`;

export const Detail = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;

  position: relative;

  svg {
    position: absolute;
    bottom: calc(100% + 4px);
    left: 50%;
    transform: translate(-50%);
  }

  :first-child {
    margin-right: 20px;
  }

  :last-child {
    margin-left: 20px;
  }

  @media ${device.mobile} {
    h6 {
      font-size: 16px;
    }
  }
`;

export const Legend = styled.p<{ color: string }>`
  margin-top: 4px;
  padding-left: 18px;

  position: relative;

  :before {
    content: "";
    width: 12px;
    height: 12px;

    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);

    background: ${({ color }) => color};
    border-radius: 3px;
  }
`;
