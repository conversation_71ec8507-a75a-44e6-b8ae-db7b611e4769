import { EMPTY_DATA, ERROR, ERROR_API_MESSAGE, WARNING } from "@constants/message";
import { DataTable, TableConfig } from "@custom-types/config-table";
import { ErrorPolicy } from "@custom-types/policy-tracking";
import {
  SOACompensationData,
  SOACompensationInput,
  SOADocumentData,
  SOADocumentInput,
  SOADownloadPDFInput,
} from "@custom-types/soa";
import { compensationDocType, downloadSalaryPDF, getListCompensation } from "api/soa";
import { Alert } from "components/alert";
import FormSearchTemplate from "components/form-search-template";
import Icons from "components/icons";
import MonthInput from "components/input-month";
import Table from "components/table";
import TableMobile from "components/table-mobile";
import useActionApi from "hooks/use-action-api";
import { useAppSelector } from "hooks/use-redux";
import { cloneDeep, get } from "lodash";
import moment from "moment";
import { useCallback, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import { setIncomeSOAForm } from "screens/performance/slice";
import { formatValueTable } from "services/format-value";
import { RowItem } from "styles";
import { ButtonIcon } from "styles/buttons";
import { SalaryPDFWrapper, TitlePDF } from "./styled";

export interface SalaryPDFData {
  agentId: string;
  longDesc: string;
  issueDate: number;
  awplIndex: string;
  agentName: string;
  documentCd: string;
  designationCd: string;
}

const configSalaryPDF: TableConfig[] = [
  { key: "agentId", show: true, type: "string", label: "Mã TVTC", sort: true },
  { key: "designationCd", show: true, type: "string", label: "Chức danh", sort: true },
  { key: "longDesc", show: true, type: "string", label: "Nội dung", sort: true },
  { key: "issueDate", show: true, type: "date", label: "Ngày", sort: true },
  { key: "awplIndex", show: true, type: "string", label: "" },
];

const SalaryPDF = () => {
  const [dataPDF, setDataPDF] = useState<SalaryPDFData[]>([]);
  const [error, setError] = useState<ErrorPolicy>({
    month: null,
  });

  const {
    loading: { getSOALoading },
  } = useAppSelector((state) => state.rootReducer);
  const { username, channel } = useAppSelector((state) => state.rootReducer.user);
  const { incomeSOAForm } = useAppSelector((state) => state.performanceReducer);
  const { month } = incomeSOAForm;

  const dispatch = useDispatch();

  const actionDocType = useActionApi<SOADocumentInput, { data: SOADocumentData }>(compensationDocType);
  const actionListCompensation = useActionApi<SOACompensationInput, { data: SOACompensationData[] }>(
    getListCompensation
  );
  const actionDownloadPDF = useActionApi<SOADownloadPDFInput, Blob>(downloadSalaryPDF);

  const handleDownloadPDF = useCallback(
    (value: string) => {
      actionDownloadPDF({
        body: { agentId: username, awplIndex: value },
        loading: {
          type: "local",
          name: "downloadAwplLoading",
        },
      })
        .then(({ data }) => {
          const url = window.URL.createObjectURL(data);
          var link = document.createElement("a");
          link.href = url;
          link.download = `bang-luong-pdf-${value}.pdf`;
          link.click();
        })
        .catch((error) => {
          if (error.response.status === 404) {
            Alert(WARNING, EMPTY_DATA);
          } else {
            console.log("error", error);
            Alert(ERROR, ERROR_API_MESSAGE);
          }
        });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [username]
  );

  const formatValueTablePDF = (data: SalaryPDFData, config: TableConfig) => {
    switch (config.key) {
      case "awplIndex":
        return (
          <ButtonIcon onClick={() => handleDownloadPDF(data.awplIndex)}>
            <Icons icon="download-icon-border" />
          </ButtonIcon>
        );
      default:
        return formatValueTable(data, config);
    }
  };

  const dataTable: DataTable[][] = useMemo(
    () =>
      dataPDF?.map((d: any) =>
        configSalaryPDF.map((config) => ({
          config: config,
          node: formatValueTablePDF(d, config),
          originData: get(d, [config.key]),
        }))
      ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dataPDF, configSalaryPDF]
  );

  const handleChangeData = useCallback(
    (key: string, value: any) => {
      const incomeSOAFormClone = cloneDeep(incomeSOAForm);

      if (key === "month") {
        incomeSOAFormClone.month = value;

        if (value.length < 7) {
          setError({ month: "Vui lòng nhập đúng định dạng: mm/yyyy" });
        } else {
          if (moment(value, "MM/YYYY").isAfter(moment(), "month")) {
            setError({
              month: "Dữ liệu được cập nhật đến thời điểm hiện tại. Vui lòng không chọn tháng/năm trong tương lai.",
            });
          } else {
            setError({ month: "" });
          }
        }
      }

      dispatch(setIncomeSOAForm(incomeSOAFormClone));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [incomeSOAForm]
  );

  const handleSubmit = useCallback(() => {
    const [month, year] = incomeSOAForm.month.split("/");

    actionDocType({
      body: { agentId: username, channelCd: channel },
      loading: {
        type: "local",
        name: "getSOALoading",
      },
    }).then(({ data }) => {
      actionListCompensation({
        body: {
          agentId: username,
          language: "V",
          listDocType: data?.data,
          month: Number(month),
          year: Number(year),
        },
        loading: {
          type: "local",
          name: "getSOALoading",
        },
      })
        .then(({ data }) => {
          setDataPDF(data?.data);
        })
        .catch((error) => {
          console.log(error);
          Alert(ERROR, ERROR_API_MESSAGE);
        });
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [username, channel, incomeSOAForm.month]);

  return (
    <SalaryPDFWrapper>
      <FormSearchTemplate title="Thông tin" onSubmit={handleSubmit}>
        <RowItem>
          <MonthInput
            placeholder="Tháng"
            value={month}
            error={error.month}
            onChange={(value) => handleChangeData("month", value)}
          />
        </RowItem>
      </FormSearchTemplate>

      <TitlePDF className="h7">Tổng cộng {dataPDF.length} kết quả cho Bảng Kê Thu Nhập</TitlePDF>
      <Table
        data={dataTable}
        config={configSalaryPDF}
        showOrderNo={true}
        showConfig={false}
        showPagination={false}
        loading={getSOALoading}
      />
      <TableMobile config={configSalaryPDF} data={dataTable} />
    </SalaryPDFWrapper>
  );
};

export default SalaryPDF;
