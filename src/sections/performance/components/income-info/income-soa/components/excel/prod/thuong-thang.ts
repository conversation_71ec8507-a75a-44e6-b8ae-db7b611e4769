import { PolicyBonusMonthDetailData, TotalIncomeAgencyData } from "@custom-types/soa";
import ExcelJS from "exceljs";
import { isNil } from "lodash";
import { renderAgentInfo } from "../";
import { ExcelSoaConfig, renderDataTableSOAExcel, renderTwoColsExcelData } from "../utils";

// const monthBonusConfig1 = [
//   { col: "B", key: "totalFYP", type: "string", label: "FYP trong tháng (Triệu đồng)", mergeCell: "B15:B17" },
//   { col: "C", type: "string", label: "Tỷ lệ thưởng", mergeCell: "C15:F15", customCell: "C15" },
//   {
//     key: "",
//     customCell: "C16",
//     col: "C",
//     type: "string",
//     label: "Tỷ lệ duy trì phí năm hai cá nhân (K2) tại cuối tháng",
//     mergeCell: "C16:F16",
//   },
//   {
//     key: "k2Rate75",
//     customCell: "C17",
//     col: "C",
//     type: "string",
//     label: "K2 ≥ 75%",
//   },
//   {
//     key: "k2Rate70",
//     customCell: "D17",
//     col: "D",
//     type: "string",
//     label: "70% ≤ K2 < 75%",
//   },
//   {
//     key: "k2Rate60",
//     customCell: "E17",
//     col: "E",
//     type: "string",
//     label: "60% ≤ K2 < 70%",
//   },
//   {
//     key: "k2Rate50",
//     customCell: "F17",
//     col: "F",
//     type: "string",
//     label: "50% ≤ K2 < 60%",
//   },
// ];

export const monthlyBonusWorksheet = async (
  worksheet: ExcelJS.Worksheet,
  data: PolicyBonusMonthDetailData,
  agentInfo: TotalIncomeAgencyData
) => {
  const monthlyBonusData = data?.monthlyBonus;

  const monthlyBonusDataConfig: ExcelSoaConfig[] = [
    {
      row: "18",
      col: "B",
      title: "1. FYP thực đạt trong tháng xét thưởng",
      valueCol: "H",
      value: monthlyBonusData?.totalFypPs,
      mergeCell: "B,G",
      type: "number",
    },
    {
      row: "19",
      col: "B",
      title: "2. FYC thực đạt trong tháng xét thưởng",
      valueCol: "H",
      value: monthlyBonusData?.totalFycPs,
      mergeCell: "B,G",
      type: "number",
    },
    {
      row: "20",
      col: "B",
      title: "3. Tỷ lệ duy trì phí năm hai (K2) cá nhân",
      valueCol: "H",
      value: monthlyBonusData?.k2Persistently === -1 ? "Chưa xét K2" : monthlyBonusData?.k2Persistently,
      mergeCell: "B,G",
      type:
        monthlyBonusData?.k2Persistently === -1
          ? "string"
          : isNil(monthlyBonusData?.k2Persistently)
          ? "empty"
          : "percent",
    },
    {
      row: "21",
      col: "B",
      title: "4. Tỷ lệ thưởng",
      valueCol: "H",
      value: monthlyBonusData?.bonusRate,
      mergeCell: "B,G",
      type: "percent",
    },
    {
      row: "22",
      col: "B",
      title: "5. Thực nhận tháng này: (*)",
      valueCol: "H",
      value: monthlyBonusData?.mpbFwp,
      mergeCell: "B,G",
      type: "number",
    },
    {
      row: "23",
      col: "B",
      title: "6. Các khoản điều chỉnh (nếu có):",
      valueCol: "H",
      value: monthlyBonusData?.otherAdjustment,
      mergeCell: "B,G",
      type: "number",
    },
  ];

  const monthlyBonusTableConfig: ExcelSoaConfig[] = [
    {
      title: "Chi tiết hợp đồng",
      col: "B",
      mergeCell: `B26:C26`,
      row: `26`,
      key: "",
    },
    { title: "Đại lý khai thác", col: "B", row: `27`, key: "agentCode", type: "string" },
    { title: "Số hợp đồng", col: "C", row: `27`, key: "policynumber", type: "string" },
    {
      title: "Phí bảo hiểm năm đầu",
      col: "D",
      row: `27`,
      key: "fyp",
      type: "number",
      topTotalValue: data?.totalFyp,
    },
    {
      title: "Hoa hồng bảo hiểm năm đầu",
      col: "E",
      row: `27`,
      key: "fyc",
      type: "number",
      topTotalValue: data?.totalFyc,
    },
  ];

  renderAgentInfo(worksheet, agentInfo);

  // renderFollowColsExcel({
  //   worksheet,
  //   data: rewardMonthData,
  //   config: monthBonusConfig1,
  //   startIndex: 18,
  // });
  // worksheet.getCell(
  //   "B23"
  // ).value = `TVTC mới gia nhập sau ngày 10 của tháng, tháng xét thưởng sẽ là tháng tiếp theo tháng gia nhập`;
  // worksheet.getCell(
  //   "B24"
  // ).value = `Đối với TVTC có thời gian làm việc từ 14 tháng trở xuống hoặc chưa có K2 thì sẽ áp dụng K2 là 60%`;

  renderTwoColsExcelData({
    config: monthlyBonusDataConfig,
    startIndex: 18,
    worksheet: worksheet,
  });

  renderDataTableSOAExcel({
    data: data?.monthlyBonusDetail,
    worksheet,
    config: monthlyBonusTableConfig,
    startIndex: 28,
  });
};
