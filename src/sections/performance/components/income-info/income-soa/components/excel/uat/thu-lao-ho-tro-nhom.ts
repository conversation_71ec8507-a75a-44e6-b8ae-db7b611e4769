import { PolicyBonusSupportTeam, TotalIncomeAgencyData } from "@custom-types/soa";
import ExcelJS from "exceljs";
import { sumCalculator } from "services/untils";
import { renderAgentInfo } from "..";

import { bonusSupportTeamData } from "../../policy-reward/policy-reward-modal/agency-uat/policy-bonus-support-team";
import { ExcelSoaConfig, renderDataTableSOAExcel, renderFollowColsExcel, renderTwoColsExcelData } from "../utils";

const table1Config = [
  { col: "B", key: "col1", type: "string", label: "Cấp báo cáo" },
  { col: "C", key: "col2", type: "string", label: "Hệ số nhân thù lao quản lý khai thác mới nhóm trực tiếp" },
];

export const bonusSupportTeamWorksheet = async (
  worksheet: ExcelJS.Worksheet,
  data: PolicyBonusSupportTeam,
  agentInfo: TotalIncomeAgencyData
) => {
  const supportTeamData = data?.indirectORY1;
  const overrideDetailsTable: any = data?.indirectORY1Details || [];

  const supportTeamDetailData: ExcelSoaConfig[] = [
    {
      row: "22",
      col: "B",
      title: "1. Thù lao quản lý khai thác mới nhóm trực tiếp của Cấp quản lý báo cáo cấp 1",
      valueCol: "H",
      value: supportTeamData?.valueTier2,
      mergeCell: "B,G",
      type: "number",
    },
    {
      row: "23",
      col: "B",
      title: "2. Thù lao quản lý khai thác mới nhóm trực tiếp của Cấp quản lý báo cáo cấp 2",
      valueCol: "H",
      value: supportTeamData?.valueTier3,
      mergeCell: "B,G",
      type: "number",
    },
    {
      row: "24",
      col: "B",
      title: "3. Thực nhận tháng này (*)",
      valueCol: "H",
      value: supportTeamData?.incentive_Indirect,
      mergeCell: "B,G",
      type: "number",
    },
    {
      row: "25",
      col: "B",
      title: "4. Các khoản điều chỉnh (nếu có):",
      valueCol: "H",
      value: supportTeamData?.adjustAmount,
      mergeCell: "B,G",
      type: "number",
    },
  ];

  const overrideDetailsConfig: ExcelSoaConfig[] = [
    {
      title: "Chi tiết",
      col: "B",
      mergeCell: `B28:E28`,
      row: `28`,
      key: "",
    },
    { title: "Cấp quản lý", col: "B", row: `29`, key: "agentId", type: "string" },
    { title: "Cấp quản lý báo cáo", col: "C", row: `29`, key: "sourceAgentId", type: "string" },
    { title: "Chức danh Cấp quản lý báo cáo", col: "D", row: `29`, key: "designationCd", type: "string" },
    { title: "Hệ số nhân", col: "E", row: `29`, key: "multiFactor", type: "percent" },
    {
      title: "Thù lao quản lý khai thác mới nhóm trực tiếp của Cấp quản lý báo cáo",
      col: "F",
      row: `29`,
      key: "incentive",
      type: "number",
      topTotalValue: sumCalculator(overrideDetailsTable, "incentive"),
    },
  ];

  renderAgentInfo(worksheet, agentInfo);

  //Chi tiết Thù lao từ dịch vụ quản lý hoạt động khai thác mới
  //table 1
  renderFollowColsExcel({
    data: bonusSupportTeamData,
    config: table1Config,
    startIndex: 16,
    worksheet,
  });

  renderTwoColsExcelData({
    worksheet,
    config: supportTeamDetailData,
    startIndex: 22,
  });

  renderDataTableSOAExcel({
    worksheet,
    config: overrideDetailsConfig,
    data: data?.indirectORY1Details,
    startIndex: 30,
  });
};
