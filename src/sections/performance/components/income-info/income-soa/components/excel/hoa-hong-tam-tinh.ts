import { IncomeProData, TotalIncomeAgencyData } from "@custom-types/soa";
import ExcelJS from "exceljs";
import { renderAgentInfo } from ".";
import { renderDataTableSOAExcel } from "./utils";
import { ExcelConfig } from "@custom-types/excel";

interface CommissionProvisionalData {
  data: {
    provisionalComTotal: number;
    totalProvisionalCom: IncomeProData[];
    lstProvisionalCom: IncomeProData[];
  };
  designationCdWithDate: string;
}

const commissionProExcelConfig: ExcelConfig[] = [
  { title: "HOA HỒNG TẠM TÍNH", col: "B", mergeCell: "B13:M13", row: "13", key: "" },
  { title: "<PERSON><PERSON> hợp đồng", col: "B", row: "14", key: "polnum" },
  { title: "Mã sản phẩm", col: "C", row: "14", key: "productCode" },
  { title: "<PERSON>ên sản phẩm", col: "D", row: "14", key: "productName" },
  { title: "<PERSON><PERSON><PERSON> phát hành lần đầu", col: "E", row: "14", key: "firstIssueDate", type: "date" }, //
  { title: "Ngày phát sinh phí", col: "F", row: "14", key: "incurredDate", type: "date" },
  { title: "Ngày ACK", col: "G", row: "14", key: "ackDate", type: "date" },
  { title: "Thời điểm qua 21 ngày xem xét", col: "H", row: "14", key: "date21", type: "date" },
  { title: "Phí bảo hiểm", col: "I", row: "14", key: "premium", type: "number" },
  { title: "Tỷ lệ hoa hồng (%)", col: "J", row: "14", key: "ratioCom", type: "percent" },
  { title: "Hoa hồng tạm tính (VNĐ)", col: "K", row: "14", key: "commission", type: "number" },
  {
    title: "Thưởng quyền lợi sản phẩm bổ trợ tạm tính (VNĐ)",
    col: "L",
    row: "14",
    key: "bonusBenefit",
    type: "number",
  },
  { title: "Loại hoa hồng", col: "M", row: "14", key: "comType" },
];

export const commissionProvisionalWorksheet = async (
  worksheet: ExcelJS.Worksheet,
  data: CommissionProvisionalData,
  agentInfo: TotalIncomeAgencyData
) => {
  renderAgentInfo(worksheet, agentInfo);
  renderDataTableSOAExcel({
    worksheet,
    data: data?.data?.lstProvisionalCom,
    config: commissionProExcelConfig,
    startIndex: 15,
  });
};
