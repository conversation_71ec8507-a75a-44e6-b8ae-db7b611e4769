import { PolicyBonusLeaderPersisbon, TotalIncomeAgencyData } from "@custom-types/soa";
import ExcelJS from "exceljs";
import { sumCalculator } from "services/untils";
import { renderAgentInfo } from "..";
import { ExcelSoaConfig, renderDataTableSOAExcel, renderTwoColsExcelData, titleStyle } from "../utils";

export const leaderPersisbonWorksheet = async (
  worksheet: ExcelJS.Worksheet,
  data: PolicyBonusLeaderPersisbon,
  agentInfo: TotalIncomeAgencyData
) => {
  const leaderPersisbonData = data?.persistencyLeader;
  const overrideDetailsTable = data?.persistencyLeaderDetails || [];

  const newMiningManagementDetailData: ExcelSoaConfig[] = [
    {
      row: "19",
      col: "B",
      title: "1. Tổng thưởng duy trì hợp đồng bảo hiểm thực nhận của cấp báo cáo liền kề",
      valueCol: "H",
      value: leaderPersisbonData?.persistencyBonus,
      mergeCell: "B,G",
      type: "number",
    },
    {
      row: "20",
      col: "B",
      title: "2. Hệ số nhân (%)",
      valueCol: "H",
      value: leaderPersisbonData?.multiFactor,
      mergeCell: "B,G",
      type: "percent",
    },
    {
      row: "21",
      col: "B",
      title: "3. Thực nhận tháng này (*)",
      valueCol: "H",
      value: leaderPersisbonData?.leaderPersisBonus,
      mergeCell: "B,G",
      type: "number",
    },
    {
      row: "22",
      col: "B",
      title: "4. Các khoản điều chỉnh (nếu có):",
      valueCol: "H",
      value: leaderPersisbonData?.adjustAmount,
      mergeCell: "B,G",
      type: "number",
    },
  ];

  const overrideDetailsConfig: ExcelSoaConfig[] = [
    {
      title: "Chi tiết",
      col: "B",
      mergeCell: `B25:D25`,
      row: `25`,
      key: "",
    },
    { title: "Cấp quản lý", col: "B", row: `26`, key: "agentId", type: "string" },
    { title: "Mã số Cấp báo cáo liền kề", col: "C", row: `26`, key: "sourceAgentId", type: "string" },
    { title: "Chức danh cấp báo cáo liền kề", col: "D", row: `26`, key: "designationCd", type: "string" },
    {
      title: "Thưởng duy trì hợp đồng bảo hiểm thực nhận",
      col: "E",
      row: `26`,
      key: "persistencyBonus",
      topTotalValue: sumCalculator(overrideDetailsTable, "persistencyBonus"),
    },
  ];

  renderAgentInfo(worksheet, agentInfo);

  worksheet.getCell("B15").style = {
    ...titleStyle,
    font: {
      size: 14,
      bold: true,
    },
  };

  worksheet.getCell(
    "B15"
  ).value = `Thù lao hỗ trợ thu phí tái tục năm hai= 50% x [Thưởng duy trì hợp đồng bảo hiểm thực nhận của cấp báo cáo liền kề]`;
  worksheet.mergeCells("B15:E17");

  renderTwoColsExcelData({
    worksheet,
    config: newMiningManagementDetailData,
    startIndex: 19,
  });

  renderDataTableSOAExcel({
    worksheet,
    config: overrideDetailsConfig,
    data: data?.persistencyLeaderDetails,
    startIndex: 27,
  });
};
