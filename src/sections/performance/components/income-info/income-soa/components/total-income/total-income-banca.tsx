import { TotalIncomeBancaData } from "@custom-types/soa";
import Note from "components/note";
import TableCollapse, { HeadTableCollapse, TableCollapseData } from "components/table-collapse";
import TableCollapseMobile from "components/table-collapse-mobile";
import { useAppSelector } from "hooks/use-redux";
import { useMemo } from "react";
import { IncomeChannelBancaWrapper } from "../../styled";
import { isNil } from "lodash";

interface TotalIncomeBancaProps {
  data: TotalIncomeBancaData;
}

const headingTableBANCA: HeadTableCollapse[][] = [
  [
    { label: "Chi tiết các khoản thu nhập trong tháng", colSpan: 2 },
    { label: "Đơn vị: VNĐ", colSpan: 2 },
  ],
];

const salesDetailDataHeading: HeadTableCollapse[][] = [
  [
    { label: "Chi tiết doanh số", colSpan: 2 },
    { label: "Đơn vị: VNĐ", colSpan: 2 },
  ],
];

const noteList = [
  "Thông tin trong bản kê thu nhập này chỉ dành riêng cho TVTC và phải được bảo mật",
  "Trong bất kỳ tình huống nào thông tin cũng không được tiết lộ cho bên thứ ba không phải TVTC trừ trường hợp được sự chấp thuận từ Công ty.",
];

const TotalIncomeBanca = ({ data }: TotalIncomeBancaProps) => {
  const {
    loading: { getSOALoading },
    user: { subchannel },
  } = useAppSelector((state) => state.rootReducer);

  const monthlyIncomeBANCA: TableCollapseData[] = useMemo(() => {
    return [
      {
        label: "A. Tổng thu nhập chịu thuế",
        value: data?.totalTaxableIncome,
        type: "number",
        colSpan: 2,
        children: [
          {
            label: "Trợ cấp cơ bản tháng",
            value: data?.allowFixMonth,
            type: "number",
            colSpan: 2,
          },
          {
            label: "Thưởng tháng theo doanh số năm nhất",
            value: data?.bonusSalesFirstYear,
            type: "number",
            colSpan: 2,
          },
          {
            label: "Thưởng tháng theo doanh số năm hai",
            value: data?.bonusSalesSecondYear,
            type: "number",
            colSpan: 2,
          },
          {
            label: "Hỗ trợ chi phí điện thoại/khám sức khỏe",
            value: data?.healthCareSupport,
            type: "number",
            colSpan: 2,
          },
          {
            label: "Thưởng doanh số quý",
            value: data?.bonusOfQuarterly,
            type: "number",
            colSpan: 2,
          },
          {
            label: "Thưởng doanh số năm",
            value: data?.bonusSalesAnnual,
            type: "number",
            colSpan: 2,
          },
          {
            label: "Thưởng chương trình thi đua",
            value: data?.bonusEmulation,
            type: "number",
            colSpan: 2,
          },
          {
            label: "Các khoản thu nhập và điều chỉnh khác",
            value: data?.othersIncome,
            type: "number",
            colSpan: 2,
          },
        ],
      },
      {
        label: "B. Thuế thu nhập cá nhân",
        value: -Math.abs(data?.taxIncurredInMonth),
        type: "number",
        colSpan: 2,
        children: [
          {
            label: "Thuế phát sinh trong tháng",
            value: -Math.abs(data?.taxIncurredInMonth),
            type: "number",
            colSpan: 2,
          },
        ],
      },
      {
        label: "C. Thu nhập không chịu thuế",
        value: data?.totalNonTaxBlInc,
        type: "number",
        colSpan: 2,
        children: [
          {
            label: "Thu nhập tạm giữ từ tháng trước",
            value: data?.incomeHoldInPreMonth,
            type: "number",
            colSpan: 2,
          },
          {
            label: "Tiền ngân hàng hoàn trả",
            value: data?.incomeFromBank,
            type: "number",
            colSpan: 2,
          },
          {
            label: "Các khoản điều chỉnh khác",
            value: data?.othersAdjust,
            type: "number",
            colSpan: 2,
          },
          {
            label: "Hoàn trả thuế",
            value: data?.taxRefund,
            type: "number",
            colSpan: 2,
          },
        ],
      },
      {
        label: "D. Thu nhập thực lãnh: (A) + (B) + (C)",
        value: data?.incomeActual,
        type: "number",
        colSpan: 2,
      },
    ];
  }, [data]);

  const salesDetailDataBANCA: TableCollapseData[] = useMemo(() => {
    const isAGB = ["AGF", "AGB"].includes(subchannel);

    return [
      {
        label: "Tháng",
        isHide: true,
        type: "empty",
        colSpan: 2,
        children: [
          {
            label: "Tỷ lệ duy trì hợp đồng (K2)",
            value: data?.contractRetentionRate,
            type: isNil(data?.contractRetentionRate) ? "empty" : "percent",
            colSpan: 2,
          },
          {
            label: "Tỷ lệ duy trì FSC/SFSC",
            value: data?.fscRetention,
            type: "percent",
            colSpan: 2,
          },
          {
            label: "Tổng FYP pass ACK 01",
            value: data?.totalAck01,
            type: "number",
            colSpan: 2,
          },
          {
            label: "Tổng FYP pass ACK 02",
            value: data?.totalAck02,
            type: "number",
            colSpan: 2,
          },
          {
            label: "Tổng SYP(doanh thu năm hai)",
            value: data?.totalFypSecondYear,
            type: "number",
            colSpan: 2,
          },
          {
            label: "Tỷ lệ sản phẩm hỗ trợ cá nhân",
            value: data?.personalSupport,
            type: "percent",
            colSpan: 2,
          },
          {
            label: "Trung bình IP của hợp đồng phát hành trong tháng",
            value: data?.ipAverageInMonth,
            type: "number",
            colSpan: 2,
          },
          isAGB
            ? {
                label: "Tỷ lệ hoạt động của PGD/CN (%) nhóm 1",
                value: 0, //hardcode: Telegram 11:00,06/07/2023
                type: "empty",
                colSpan: 2,
              }
            : null,
          isAGB
            ? {
                label: "Tỷ lệ hoạt động của PGD/CN (%) nhóm 2",
                value: 0, //hardcode: Telegram 11:00,06/07/2023
                type: "empty",
                colSpan: 2,
              }
            : null,
          {
            label: "Tỷ lệ hoạt động của PGD/CN (%) pass ACK 01",
            value: data?.perOfOperationAck01,
            type: "percent",
            colSpan: 2,
          },
          {
            label: "Tỷ lệ hoạt động của PGD/CN (%) pass ACK 02",
            value: data?.perOfOperationAck02,
            type: "percent",
            colSpan: 2,
          },
          {
            label: "Số lượng hợp đồng phát hành/Số lượng PGD/CN hoạt động",
            value: data?.numOfActivities,
            type: "number",
            colSpan: 2,
          },
          {
            label: "Tỷ lệ sản phẩm hỗ trợ theo cấp quản lý",
            value: data?.perOfManagerSupport,
            type: "percent",
            colSpan: 2,
          },
        ],
      },
      {
        label: "Quý",
        type: "empty",

        colSpan: 2,
        children: [
          {
            label: "Tỷ lệ duy trì hợp đồng (K2)",
            value: data?.contractRetentionK2,
            type: isNil(data?.contractRetentionK2) ? "empty" : "percent",
            colSpan: 2,
          },
          {
            label: "Trung bình IP của hợp đồng phát hành trong Quý (cá nhân)",
            value: data?.ipAverageInQuarter,
            type: "number",
            colSpan: 2,
          },
          {
            label: "Tổng FYP pass ACK 02",
            value: data?.totalFypAck02,
            type: "number",
            colSpan: 2,
          },
          {
            label: "Tỷ lệ hoạt động của PGD/CN (%)",
            value: data?.numOfActInQuarter,
            type: "percent",
            colSpan: 2,
          },
          {
            label: "Số lượng hợp đồng phát hành/Số lượng PGD/CN hoạt động",
            value: data?.numOfIssuedQuarter,
            type: "number",
            colSpan: 2,
          },
          {
            label: "Tỷ lệ sản phẩm hỗ trợ theo cấp quản lý",
            value: data?.perOfProductManager,
            type: "percent",
            colSpan: 2,
          },
        ],
      },
      {
        label: "Năm",
        type: "empty",
        colSpan: 2,
        children: [
          {
            label: "Tỷ lệ duy trì hợp đồng (K2)",
            value: data?.perOfContractInYear,
            type: isNil(data?.perOfContractInYear) ? "empty" : "percent",
            colSpan: 2,
          },
          {
            label: "Tổng FYP pass ACK 02",
            value: data?.totalAck02InYear,
            type: "number",
            colSpan: 2,
          },
        ],
      },
    ];
  }, [data, subchannel]);

  return (
    <IncomeChannelBancaWrapper>
      <TableCollapse head={headingTableBANCA} body={monthlyIncomeBANCA} loading={getSOALoading} />
      <TableCollapseMobile head={headingTableBANCA} body={monthlyIncomeBANCA} />
      <TableCollapse head={salesDetailDataHeading} body={salesDetailDataBANCA} loading={getSOALoading} />
      <TableCollapseMobile head={salesDetailDataHeading} body={salesDetailDataBANCA} />
      <Note title=" Ghi chú:" type="list" content={noteList} backgroundColor="#EDEFF0" />
    </IncomeChannelBancaWrapper>
  );
};

export default TotalIncomeBanca;
