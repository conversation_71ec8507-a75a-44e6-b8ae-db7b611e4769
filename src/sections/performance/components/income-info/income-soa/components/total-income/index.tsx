import { TotalIncomeAgencyData, TotalIncomeBancaData } from "@custom-types/soa";
import { useAppSelector } from "hooks/use-redux";
import { useMemo } from "react";
import Information, { InformationData } from "./information";
import { InformationWrapper, Label, TitleInfo, TotalIncomeWrapper } from "./styled";
import TotalIncomeAgency from "./total-income-agency";
import TotalIncomeBanca from "./total-income-banca";

interface TotalIncomeDetail {}

const TotalIncome = ({}: TotalIncomeDetail) => {
  const {
    user: { channel },
  } = useAppSelector((state) => state.rootReducer);
  const { totalIncome } = useAppSelector((state) => state.performanceReducer);

  const informationAGENCY: InformationData[][] = useMemo(() => {
    const totalIncomeAgency = totalIncome as TotalIncomeAgencyData;
    return [
      [
        { label: "Tên TVTC", value: totalIncomeAgency?.agentName, type: "string" },
        { label: "Mã TVTC", value: totalIncomeAgency?.agentCode, type: "string" },
        { label: "Ngày gia nhập", value: totalIncomeAgency?.joiningDate, type: "date" },
        { label: "Văn phòng", value: totalIncomeAgency?.officeName, type: "string" },
        { label: "Quản lý trực tiếp", value: totalIncomeAgency?.parentName, type: "string" },
        { label: "Giám đốc Điều Hành, Phát Triển Kinh Doanh", value: totalIncomeAgency?.adName, type: "string" },
      ],
      [
        { label: "Chức danh", value: totalIncomeAgency?.positionName, type: "string" },
        { label: "Tình trạng", value: totalIncomeAgency?.status, type: "status" },
        { label: "Mã số thuế", value: totalIncomeAgency?.taxNumber, type: "string" },
        { label: "Ngân hàng", value: totalIncomeAgency?.bankName, type: "string" },
        { label: "Tài khoản số", value: totalIncomeAgency?.bankAccount, type: "string" },
        { label: "Chi nhánh", value: totalIncomeAgency?.bankBranch, type: "string" },
      ],
    ];
  }, [totalIncome]);

  const informationBANCA: InformationData[][] = useMemo(() => {
    const totalIncomeBanca = totalIncome as TotalIncomeBancaData;
    return [
      [
        { label: "Tên TVTC", value: totalIncomeBanca?.agentName, type: "string" },
        { label: "Mã TVTC", value: totalIncomeBanca?.agentCode, type: "string" },
        { label: "Chức danh", value: totalIncomeBanca?.position, type: "string" },
        { label: "Tình trạng", value: totalIncomeBanca?.status, type: "status" },
      ],
      [
        { label: "Ngân hàng", value: totalIncomeBanca?.bankName, type: "string" },
        { label: "Chi nhánh", value: totalIncomeBanca?.branchName, type: "string" },
        { label: "Tài khoản số", value: totalIncomeBanca?.bankAccount, type: "string" },
      ],
    ];
  }, [totalIncome]);

  const informationData = useMemo(() => {
    if (channel === "AGENCY") {
      return informationAGENCY;
    } else {
      return informationBANCA;
    }
  }, [channel, informationAGENCY, informationBANCA]);

  const Content = useMemo(() => {
    switch (channel) {
      case "AGENCY":
        return TotalIncomeAgency;
      case "BANCA":
        return TotalIncomeBanca;
      default:
        return null;
    }
  }, [channel]);

  return (
    <TotalIncomeWrapper>
      <InformationWrapper>
        <Label className="label-5">Công ty TNHH Bảo hiểm nhân thọ FWD Việt Nam</Label>
        <TitleInfo className="h7">Bảng kê thu nhập</TitleInfo>
        <Information data={informationData} />
      </InformationWrapper>
      {Content ? <Content data={totalIncome} /> : null}
    </TotalIncomeWrapper>
  );
};

export default TotalIncome;
