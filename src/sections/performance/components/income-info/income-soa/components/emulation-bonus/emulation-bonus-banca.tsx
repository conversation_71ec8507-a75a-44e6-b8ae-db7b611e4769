import { BancaEmulationBonusData } from "@custom-types/soa";
import TableSOA, { HeadTableSOA, TableSOAData } from "components/table-soa";
import FuncHeaderSOA from "components/table-soa/header-mobile-soa";
import { RowDataTooltip } from "components/table-soa/header-mobile-soa/styled";
import { useAppSelector } from "hooks/use-redux";
import { get } from "lodash";
import { useMemo } from "react";
import { formatValueTable } from "services/format-value";
import { filterMapping, formatNumber, sumCalculator } from "services/untils";
import { Separate } from "styles";
import { findTitleMobileSOA } from "../../..";
import { IncomeChannelWrapper } from "../../styled";
import { HeadTitle } from "./styled";

interface EmulationBonusBancaProps {}

const EmulationBonusBanca = ({}: EmulationBonusBancaProps) => {
  const {
    loading: { getSOALoading },
  } = useAppSelector((state) => state.rootReducer);
  const { contestBonus } = useAppSelector((state) => state.performanceReducer);

  const configHeadBonus: HeadTableSOA[][] = useMemo(() => {
    const contestBanca = contestBonus as BancaEmulationBonusData;

    return [
      [
        { label: "CÁC KHOẢN THƯỞNG THI ĐUA BẰNG TIỀN", colSpan: 2, isTitleMobile: true, isNotMapping: true },
        {
          key: "amount",
          label: formatNumber(sumCalculator(contestBanca?.lstBonusbyCash, "amount")),
          isNotMapping: true,
          textAlign: "right",
        },
      ],
      [
        { key: "description", label: "Nội dung", colSpan: 2 },
        { key: "amount", label: "Số tiền", type: "number" },
      ],
    ];
  }, [contestBonus]);

  const configHeadNoBonus: HeadTableSOA[][] = useMemo(() => {
    const contestBanca = contestBonus as BancaEmulationBonusData;

    return [
      [
        { label: "CÁC KHOẢN THƯỞNG THI ĐUA KHÔNG BẰNG TIỀN", colSpan: 2, isTitleMobile: true, isNotMapping: true },
        {
          key: "amount",
          label: formatNumber(sumCalculator(contestBanca?.lstBonusNotbyCash, "amount")),
          isNotMapping: true,
          textAlign: "right",
        },
      ],
      [
        { key: "description", label: "Nội dung", colSpan: 2 },
        { key: "amount", label: "Số tiền", type: "number" },
      ],
    ];
  }, [contestBonus]);

  const bodyBonusData: TableSOAData[][] = useMemo(() => {
    const contestBanca = contestBonus as BancaEmulationBonusData;

    return contestBanca?.lstBonusbyCash?.map((data) =>
      filterMapping(configHeadBonus).map((head) => ({
        config: head,
        node: formatValueTable(data, head),
        originData: get(data, [head.key]),
      }))
    );
  }, [contestBonus, configHeadBonus]);

  const bodyNoBonusData: TableSOAData[][] = useMemo(() => {
    const contestBanca = contestBonus as BancaEmulationBonusData;

    return contestBanca?.lstBonusNotbyCash?.map((data) =>
      filterMapping(configHeadNoBonus).map((head) => ({
        config: head,
        node: formatValueTable(data, head),
        originData: get(data, [head.key]),
      }))
    );
  }, [contestBonus, configHeadNoBonus]);

  const tooltipBonusNotByCash = useMemo(() => {
    const tooltipList = configHeadNoBonus[0]
      .filter((filter) => !filter.isTitleMobile)
      .map((item) => ({
        label: configHeadNoBonus[1].find((find) => find.key === item.key)?.label,
        value: item.label,
      }));

    return (
      <FuncHeaderSOA icon="tooltip">
        {tooltipList.map((item, index) => (
          <RowDataTooltip key={index}>
            <label className="label-4">{item.label}</label>
            <h6 className="h7">{item.value}</h6>
          </RowDataTooltip>
        ))}
      </FuncHeaderSOA>
    );
  }, [configHeadNoBonus]);

  const tooltipBonusByCash = useMemo(() => {
    const tooltipList = configHeadBonus[0]
      .filter((filter) => !filter.isTitleMobile)
      .map((item) => ({
        label: configHeadBonus[1].find((find) => find.key === item.key)?.label,
        value: item.label,
      }));

    return (
      <FuncHeaderSOA icon="tooltip">
        {tooltipList.map((item, index) => (
          <RowDataTooltip key={index}>
            <label className="label-4">{item.label}</label>
            <h6 className="h7">{item.value}</h6>
          </RowDataTooltip>
        ))}
      </FuncHeaderSOA>
    );
  }, [configHeadBonus]);

  return (
    <IncomeChannelWrapper>
      <h6>Chi tiết các khoản thưởng thi đua</h6>
      <HeadTitle className="h7">I. Khoản thưởng thi đua và thu nhập khác</HeadTitle>
      <TableSOA
        titleMobile={findTitleMobileSOA(configHeadBonus)}
        head={configHeadBonus}
        data={bodyBonusData}
        renderIconMobile={tooltipBonusByCash}
        loading={getSOALoading}
        isStickyHeader
      />
      <Separate mgTop={20} mgBottom={20} />
      <TableSOA
        titleMobile={findTitleMobileSOA(configHeadNoBonus)}
        head={configHeadNoBonus}
        data={bodyNoBonusData}
        renderIconMobile={tooltipBonusNotByCash}
        loading={getSOALoading}
        isStickyHeader
      />
    </IncomeChannelWrapper>
  );
};

export default EmulationBonusBanca;
