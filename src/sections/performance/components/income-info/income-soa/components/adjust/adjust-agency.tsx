import { IncomeAdjustmentData } from "@custom-types/soa";
import TableSOA, { HeadTableSOA, TableSOAData } from "components/table-soa";
import FuncHeaderSOA from "components/table-soa/header-mobile-soa";
import { RowDataTooltip } from "components/table-soa/header-mobile-soa/styled";
import { useAppSelector } from "hooks/use-redux";
import { get } from "lodash";
import { useMemo } from "react";
import { formatValueTable } from "services/format-value";
import { filterMapping, formatNumber, sumCalculator } from "services/untils";
import { Separate } from "styles";
import { findTitleMobileSOA } from "../../..";
import { SubTitleSOA, TitleSOA } from "../../../styled";
import { IncomeChannelWrapper } from "../../styled";

interface AdjustAgencyProps {}

const AdjustAgency = ({}: AdjustAgencyProps) => {
  const {
    loading: { getSOALoading },
  } = useAppSelector((state) => state.rootReducer);
  const { incomeAdjustment } = useAppSelector((state) => state.performanceReducer);
  const { lstAdjustDetails, lstDeductOthers, lstBankReturns, lstAdjustOthers } =
    (incomeAdjustment as IncomeAdjustmentData) ?? {};

  const adjustDetailHead: HeadTableSOA[][] = useMemo(() => {
    const totalAmount = sumCalculator(lstAdjustDetails, "amount");

    return [
      [
        { label: "Các khoản điều chỉnh", colSpan: 2, isTitleMobile: true, isNotMapping: true },
        { key: "amount", label: formatNumber(totalAmount, true), isNotMapping: true, textAlign: "right" },
      ],
      [
        { key: "description", label: "Nội dung", colSpan: 2 },
        { key: "amount", label: "Số tiền", type: "number" },
      ],
    ];
  }, [lstAdjustDetails]);

  const adjustDetailData: TableSOAData[][] = useMemo(() => {
    return lstAdjustDetails?.map((data) =>
      filterMapping(adjustDetailHead).map((head) => ({
        config: head,
        node: formatValueTable(data, head, true),
        originData: get(data, [head.key]),
      }))
    );
  }, [lstAdjustDetails, adjustDetailHead]);

  const adjustDeductOthersHead: HeadTableSOA[][] = useMemo(() => {
    const totalAmount = sumCalculator(lstDeductOthers, "amount");

    return [
      [
        { label: "Các khoản trừ khác", colSpan: 2, isTitleMobile: true, isNotMapping: true },
        { key: "amount", label: formatNumber(totalAmount), isNotMapping: true, textAlign: "right" },
      ],
      [
        { key: "description", label: "Nội dung", colSpan: 2 },
        { key: "amount", label: "Số tiền", type: "number" },
      ],
    ];
  }, [lstDeductOthers]);

  const adjustDeductOthersData: TableSOAData[][] = useMemo(() => {
    return lstDeductOthers?.map((data) =>
      filterMapping(adjustDeductOthersHead).map((head) => ({
        config: head,
        node: formatValueTable(data, head),
        originData: get(data, [head.key]),
      }))
    );
  }, [lstDeductOthers, adjustDeductOthersHead]);

  const bankReturnHead: HeadTableSOA[][] = useMemo(() => {
    const totalAmount = sumCalculator(lstBankReturns, "amount");

    return [
      [
        { label: "Thu nhập trả về từ ngân hàng", colSpan: 2, isTitleMobile: true, isNotMapping: true },
        { key: "amount", label: formatNumber(totalAmount), isNotMapping: true, textAlign: "right" },
      ],
      [
        { key: "description", label: "Nội dung", colSpan: 2 },
        { key: "amount", label: "Số tiền", type: "number" },
      ],
    ];
  }, [lstBankReturns]);

  const bankReturnData: TableSOAData[][] = useMemo(() => {
    return lstBankReturns?.map((data) =>
      filterMapping(bankReturnHead).map((head) => ({
        config: head,
        node: formatValueTable(data, head),
        originData: get(data, [head.key]),
      }))
    );
  }, [lstBankReturns, bankReturnHead]);

  const adjustOtherHead: HeadTableSOA[][] = useMemo(() => {
    const totalAmount = sumCalculator(lstAdjustOthers, "amount");

    return [
      [
        { label: "Các khoản điều chỉnh khác", colSpan: 2, isTitleMobile: true, isNotMapping: true },
        { key: "amount", label: formatNumber(totalAmount), isNotMapping: true, textAlign: "right" },
      ],
      [
        { key: "description", label: "Nội dung", colSpan: 2 },
        { key: "amount", label: "Số tiền", type: "number" },
      ],
    ];
  }, [lstAdjustOthers]);

  const adjustOtherData: TableSOAData[][] = useMemo(() => {
    return lstAdjustOthers?.map((data) =>
      filterMapping(adjustOtherHead).map((head) => ({
        config: head,
        node: formatValueTable(data, head),
        originData: get(data, [head.key]),
      }))
    );
  }, [lstAdjustOthers, adjustOtherHead]);

  const tooltipAdjustDetail = useMemo(() => {
    const tooltipList = adjustDetailHead[0]
      .filter((filter) => !filter.isTitleMobile)
      .map((item) => ({
        label: adjustDetailHead[1].find((find) => find.key === item.key)?.label,
        value: item.label,
      }));

    return (
      <FuncHeaderSOA icon="tooltip">
        {tooltipList.map((item, index) => (
          <RowDataTooltip key={index}>
            <label className="label-4">{item.label}</label>
            <h6 className="h7">{item.value}</h6>
          </RowDataTooltip>
        ))}
      </FuncHeaderSOA>
    );
  }, [adjustDetailHead]);

  const tooltipDeductOther = useMemo(() => {
    const tooltipList = adjustDeductOthersHead[0]
      .filter((filter) => !filter.isTitleMobile)
      .map((item) => ({
        label: adjustDeductOthersHead[1].find((find) => find.key === item.key)?.label,
        value: item.label,
      }));

    return (
      <FuncHeaderSOA icon="tooltip">
        {tooltipList.map((item, index) => (
          <RowDataTooltip key={index}>
            <label className="label-4">{item.label}</label>
            <h6 className="h7">{item.value}</h6>
          </RowDataTooltip>
        ))}
      </FuncHeaderSOA>
    );
  }, [adjustDeductOthersHead]);

  const tooltipBankReturn = useMemo(() => {
    const tooltipList = bankReturnHead[0]
      .filter((filter) => !filter.isTitleMobile)
      .map((item) => ({
        label: bankReturnHead[1].find((find) => find.key === item.key)?.label,
        value: item.label,
      }));

    return (
      <FuncHeaderSOA icon="tooltip">
        {tooltipList.map((item, index) => (
          <RowDataTooltip key={index}>
            <label className="label-4">{item.label}</label>
            <h6 className="h7">{item.value}</h6>
          </RowDataTooltip>
        ))}
      </FuncHeaderSOA>
    );
  }, [bankReturnHead]);

  const tooltipAdjustOther = useMemo(() => {
    const tooltipList = adjustOtherHead[0]
      .filter((filter) => !filter.isTitleMobile)
      .map((item) => ({
        label: adjustOtherHead[1].find((find) => find.key === item.key)?.label,
        value: item.label,
      }));

    return (
      <FuncHeaderSOA icon="tooltip">
        {tooltipList.map((item, index) => (
          <RowDataTooltip key={index}>
            <label className="label-4">{item.label}</label>
            <h6 className="h7">{item.value}</h6>
          </RowDataTooltip>
        ))}
      </FuncHeaderSOA>
    );
  }, [adjustOtherHead]);

  return (
    <IncomeChannelWrapper>
      <TitleSOA>Chi tiết các khoản điều chỉnh khác</TitleSOA>
      <SubTitleSOA className="h7">I. Khoản thưởng thi đua và thu nhập khác</SubTitleSOA>
      <TableSOA
        titleMobile={findTitleMobileSOA(adjustDetailHead)}
        head={adjustDetailHead}
        data={adjustDetailData}
        renderIconMobile={tooltipAdjustDetail}
        loading={getSOALoading}
        isStickyHeader
      />
      <Separate mgTop={20} mgBottom={20} />
      <SubTitleSOA className="h7">II. Các khoản trừ</SubTitleSOA>
      <TableSOA
        titleMobile={findTitleMobileSOA(adjustDeductOthersHead)}
        head={adjustDeductOthersHead}
        data={adjustDeductOthersData}
        renderIconMobile={tooltipDeductOther}
        loading={getSOALoading}
        isStickyHeader
      />
      <Separate mgTop={20} mgBottom={20} />
      <SubTitleSOA className="h7">III. Thu nhập không chịu thuế</SubTitleSOA>
      <TableSOA
        titleMobile={findTitleMobileSOA(bankReturnHead)}
        head={bankReturnHead}
        data={bankReturnData}
        renderIconMobile={tooltipBankReturn}
        loading={getSOALoading}
      />
      <TableSOA
        titleMobile={findTitleMobileSOA(adjustOtherHead)}
        head={adjustOtherHead}
        data={adjustOtherData}
        renderIconMobile={tooltipAdjustOther}
        loading={getSOALoading}
        isStickyHeader
      />
    </IncomeChannelWrapper>
  );
};

export default AdjustAgency;
