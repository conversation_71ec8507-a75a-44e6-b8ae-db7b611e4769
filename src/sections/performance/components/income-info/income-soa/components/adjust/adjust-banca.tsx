import { BancaAdjustmentBonusData } from "@custom-types/soa";
import TableSOA, { HeadTableSOA, TableSOAData } from "components/table-soa";
import FuncHeaderSOA from "components/table-soa/header-mobile-soa";
import { RowDataTooltip } from "components/table-soa/header-mobile-soa/styled";
import { useAppSelector } from "hooks/use-redux";
import { get } from "lodash";
import { useMemo } from "react";
import { formatValueTable } from "services/format-value";
import { filterMapping, formatNumber, sumCalculator } from "services/untils";
import { findTitleMobileSOA } from "../../..";
import { IncomeChannelWrapper } from "../../styled";
import { Separate } from "styles";
import { HeadTitle } from "../emulation-bonus/styled";

interface AdjustBancaProps {}

const AdjustBanca = ({}: AdjustBancaProps) => {
  const {
    loading: { getSOALoading },
  } = useAppSelector((state) => state.rootReducer);
  const { incomeAdjustment } = useAppSelector((state) => state.performanceReducer);

  const headDetailConfig: HeadTableSOA[][] = useMemo(() => {
    const bancaAdjustment = incomeAdjustment as BancaAdjustmentBonusData;

    return [
      [
        { label: "CÁC KHOẢN ĐIỀU CHỈNH", colSpan: 2, isTitleMobile: true, isNotMapping: true },
        {
          key: "amount",
          label: formatNumber(sumCalculator(bancaAdjustment?.lstBonusAdjustment, "amount")),
          isNotMapping: true,
          textAlign: "right",
        },
      ],
      [
        { key: "description", label: "Nội dung", colSpan: 2 },
        { key: "amount", label: "Số tiền", type: "number" },
      ],
    ];
  }, [incomeAdjustment]);

  const headDeductConfig: HeadTableSOA[][] = useMemo(() => {
    const bancaAdjustment = incomeAdjustment as BancaAdjustmentBonusData;

    return [
      [
        { label: "CÁC KHOẢN TRỪ KHÁC", colSpan: 2, isTitleMobile: true, isNotMapping: true },
        {
          key: "amount",
          label: formatNumber(sumCalculator(bancaAdjustment?.lstDeductOthers, "amount")),
          isNotMapping: true,
          textAlign: "right",
        },
      ],
      [
        { key: "description", label: "Nội dung", colSpan: 2 },
        { key: "amount", label: "Số tiền", type: "number" },
      ],
    ];
  }, [incomeAdjustment]);

  const bodyDetailData: TableSOAData[][] = useMemo(() => {
    const bancaAdjustment = incomeAdjustment as BancaAdjustmentBonusData;

    return bancaAdjustment?.lstBonusAdjustment?.map((data) =>
      filterMapping(headDetailConfig).map((head) => ({
        config: head,
        node: formatValueTable(data, head),
        originData: get(data, [head.key]),
      }))
    );
  }, [incomeAdjustment, headDetailConfig]);

  const bodyDeductData: TableSOAData[][] = useMemo(() => {
    const bancaAdjustment = incomeAdjustment as BancaAdjustmentBonusData;

    return bancaAdjustment?.lstDeductOthers?.map((data) =>
      filterMapping(headDeductConfig).map((head) => ({
        config: head,
        node: formatValueTable(data, head),
        originData: get(data, [head.key]),
      }))
    );
  }, [incomeAdjustment, headDeductConfig]);

  const tooltipAdjustBanca = useMemo(() => {
    const tooltipList = headDetailConfig[0]
      .filter((filter) => !filter.isTitleMobile)
      .map((item) => ({
        label: headDetailConfig[1].find((find) => find.key === item.key)?.label,
        value: item.label,
      }));

    return (
      <FuncHeaderSOA icon="tooltip">
        {tooltipList.map((item, index) => (
          <RowDataTooltip key={index}>
            <label className="label-4">{item.label}</label>
            <h6 className="h7">{item.value}</h6>
          </RowDataTooltip>
        ))}
      </FuncHeaderSOA>
    );
  }, [headDetailConfig]);

  const tooltipDeductBanca = useMemo(() => {
    const tooltipList = headDeductConfig[0]
      .filter((filter) => !filter.isTitleMobile)
      .map((item) => ({
        label: headDeductConfig[1].find((find) => find.key === item.key)?.label,
        value: item.label,
      }));

    return (
      <FuncHeaderSOA icon="tooltip">
        {tooltipList.map((item, index) => (
          <RowDataTooltip key={index}>
            <label className="label-4">{item.label}</label>
            <h6 className="h7">{item.value}</h6>
          </RowDataTooltip>
        ))}
      </FuncHeaderSOA>
    );
  }, [headDeductConfig]);

  return (
    <IncomeChannelWrapper>
      <h6>Chi tiết các khoản điều chỉnh khác</h6>
      <HeadTitle className="h7">I. Khoản thưởng thi đua và thu nhập khác</HeadTitle>
      <TableSOA
        titleMobile={findTitleMobileSOA(headDetailConfig)}
        head={headDetailConfig}
        data={bodyDetailData}
        renderIconMobile={tooltipAdjustBanca}
        loading={getSOALoading}
        isStickyHeader
      />
      <Separate mgTop={20} mgBottom={20} />
      <HeadTitle className="h7">II. Các khoản trừ</HeadTitle>
      <TableSOA
        titleMobile={findTitleMobileSOA(headDeductConfig)}
        head={headDeductConfig}
        data={bodyDeductData}
        renderIconMobile={tooltipDeductBanca}
        loading={getSOALoading}
        isStickyHeader
      />
    </IncomeChannelWrapper>
  );
};

export default AdjustBanca;
