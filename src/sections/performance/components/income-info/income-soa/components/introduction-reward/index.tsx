import { DataTable, TableConfig } from "@custom-types/config-table";
import Table from "components/table";
import TableMobile from "components/table-mobile";
import { THCustom } from "components/table/custom-table/styled";
import { useAppSelector } from "hooks/use-redux";
import { get } from "lodash";
import { useMemo } from "react";
import { TitleSOA } from "sections/performance/components/income-info/styled";
import { formatValueTable } from "services/format-value";
import { formatNumber } from "services/untils";
import { TR } from "styles/table";
import { IntroductionRewardWrapper } from "./styled";

const configIntroduction: TableConfig[] = [
  { key: "policyNumber", show: true, type: "string", label: "<PERSON><PERSON> hợp đồng" },
  { key: "owName", show: true, type: "string", label: "Bên mua BH" },
  { key: "productCode", show: true, type: "string", label: "<PERSON><PERSON> sản phẩm" },
  { key: "productName", show: true, type: "string", label: "Tên sản phẩm" },
  { key: "firstIssueDate", show: true, type: "date", label: "<PERSON><PERSON>y phát hành lần đầu" },
  { key: "payableDate", show: true, type: "date", label: "Ngày phát sinh phí" },
  { key: "ackDate", show: true, type: "date", label: "Ngày xác nhận ACK" },
  { key: "passAckDate", show: true, type: "date", label: "Thời điểm qua 21 ngày xem xét" },
  { key: "premium", show: true, type: "number", label: "Phí BH (VNĐ)", textAlign: "right" },
  { key: "rate", show: true, type: "percent", label: "Tỷ lệ thưởng", textAlign: "right" },
  { key: "referralBonus", show: true, type: "number", label: "Thưởng giới thiệu (VNĐ)", textAlign: "right" },
  { key: "policyYear", show: true, type: "string", label: "Năm hợp đồng" },
];

const IntroductionReward = () => {
  const {
    loading: { getSOALoading },
  } = useAppSelector((state) => state.rootReducer);
  const { lsReferralBonus, totalPremium, totalReferral } = useAppSelector(
    (state) => state.performanceReducer.incomeCommissionReferral
  );

  const dataTable: DataTable[][] = useMemo(
    () =>
      lsReferralBonus?.map((d: any) =>
        configIntroduction.map((config: TableConfig) => ({
          config: config,
          node: formatValueTable(d, config),
          originData: get(d, [config.key]),
        }))
      ) || [],
    [lsReferralBonus]
  );

  const extend = useMemo(() => {
    return (
      <TR>
        <THCustom colSpan={8}>TỔNG (VNĐ)</THCustom>
        <THCustom></THCustom>
        <THCustom textAlign="right">{formatNumber(totalPremium as number)}</THCustom>
        <THCustom></THCustom>
        <THCustom textAlign="right">{formatNumber(totalReferral as number)}</THCustom>
        <THCustom></THCustom>
      </TR>
    );
  }, [totalPremium, totalReferral]);

  return (
    <IntroductionRewardWrapper>
      <TitleSOA>Thưởng giới thiệu sản phẩm qua IFWD</TitleSOA>
      <Table
        data={dataTable}
        config={configIntroduction}
        showOrderNo={true}
        showConfig={false}
        showPagination={true}
        loading={getSOALoading}
        extendHead={extend}
      />
      <TableMobile data={dataTable} config={configIntroduction} showCollapse={false} />
    </IntroductionRewardWrapper>
  );
};

export default IntroductionReward;
