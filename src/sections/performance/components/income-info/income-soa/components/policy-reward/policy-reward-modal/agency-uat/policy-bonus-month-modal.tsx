import { TableNoHeadConfig } from "@custom-types/config-table";
import Icons from "components/icons";
import Modal from "components/modal";
import Note from "components/note";
import TableNoHeader from "components/table-no-header";
import TableSOA, { HeadTableSOA, TableSOAData } from "components/table-soa";
import FuncHeaderSOA from "components/table-soa/header-mobile-soa";
import { RowDataDropdown, RowDataTooltip } from "components/table-soa/header-mobile-soa/styled";
import { TableSOAContainer } from "components/table-soa/styled";
import { useAppSelector } from "hooks/use-redux";
import useWindowResize from "hooks/use-window-resize";
import { get, isNil } from "lodash";
import { useMemo, useState } from "react";
import { findTitleMobileSOA } from "sections/performance/components/income-info";
import { formatValueTable } from "services/format-value";
import { filterMapping, formatNumber } from "services/untils";
import { ModalPolicyProps } from "../..";

const monthRewardHead: HeadTableSOA[][] = [
  [
    {
      key: "totalFYP",
      label: "FYP trong tháng (Triệu đồng)",
      textAlign: "center",
      rowSpan: 3,
    },
    {
      key: "",
      label: "Tỷ lệ thưởng",
      textAlign: "center",
      colSpan: 4,
      isNotMapping: true,
    },
  ],
  [
    {
      key: "",
      label: "Tỷ lệ duy trì phí năm hai cá nhân (K2) tại cuối tháng",
      textAlign: "center",
      colSpan: 4,
      isNotMapping: true,
    },
  ],
  [
    {
      key: "k2Rate75",
      label: "K2 ≥ 75%",
      textAlign: "center",
    },
    {
      key: "k2Rate70",
      label: "70% ≤ K2 < 75%",
      textAlign: "center",
    },
    {
      key: "k2Rate60",
      label: "60% ≤ K2 < 70%",
      textAlign: "center",
    },
    {
      key: "k2Rate50",
      label: "50% ≤ K2 < 60%",
      textAlign: "center",
    },
  ],
];

interface RewardProp {
  key?: string;
  totalFYP: string;
  k2Rate75: string;
  k2Rate70: string;
  k2Rate60: string;
  k2Rate50: string;
}

export const rewardMonthData: RewardProp[] = [
  {
    key: "fyp80",
    totalFYP: "FYP ≥ 80",
    k2Rate75: "40%",
    k2Rate70: "37%",
    k2Rate60: "34%",
    k2Rate50: "18%",
  },
  {
    key: "fyp60",
    totalFYP: "60 ≤ FYP < 80",
    k2Rate75: "37%",
    k2Rate70: "34%",
    k2Rate60: "31%",
    k2Rate50: "16%",
  },
  {
    key: "fyp40",
    totalFYP: "40 ≤ FYP < 60",
    k2Rate75: "34%",
    k2Rate70: "31%",
    k2Rate60: "28%",
    k2Rate50: "15%",
  },
  {
    key: "fyp20",
    totalFYP: "20 ≤ FYP < 40",
    k2Rate75: "20%",
    k2Rate70: "17%",
    k2Rate60: "14%",
    k2Rate50: "8%",
  },
  {
    key: "fyp20Less",
    totalFYP: "FYP < 20",
    k2Rate75: "0%",
    k2Rate70: "0%",
    k2Rate60: "0%",
    k2Rate50: "0%",
  },
];

const MonthBonusModal = ({ show, onClose }: ModalPolicyProps) => {
  const [titleDropdownMobile, setTitleDropdownMobile] = useState(
    rewardMonthData.find((item) => item.key === "fyp80")?.totalFYP
  );

  const { policyBonusMonth } = useAppSelector((state) => state.performanceReducer);
  const { monthlyBonus, monthlyBonusDetail } = policyBonusMonth;

  const { width } = useWindowResize();

  const filterRewardMonthData: RewardProp[] = useMemo(() => {
    if (width <= 768) {
      return rewardMonthData.filter((item) => item.totalFYP === titleDropdownMobile);
    } else {
      return rewardMonthData;
    }
  }, [width, titleDropdownMobile]);

  const monthRewardData: TableSOAData[][] = useMemo(() => {
    return filterRewardMonthData?.map((data) =>
      filterMapping(monthRewardHead).map((head) => ({
        config: head,
        node: formatValueTable(data, head),
        originData: get(data, [head.key]),
      }))
    );
  }, [filterRewardMonthData]);

  const monthRewardListData: TableNoHeadConfig[] = useMemo(() => {
    return [
      {
        label: "1. FYP thực đạt trong tháng xét thưởng",
        value: monthlyBonus?.totalFypPs,
        type: "number",
      },
      {
        label: "2. FYC thực đạt trong tháng xét thưởng",
        value: monthlyBonus?.totalFycPs,
        type: "number",
      },
      {
        label: "3. Tỷ lệ duy trì phí năm hai (K2) cá nhân",
        value: monthlyBonus?.k2Persistently === -1 ? "Chưa xét K2" : monthlyBonus?.k2Persistently,
        type:
          monthlyBonus?.k2Persistently === -1 ? "string" : isNil(monthlyBonus?.k2Persistently) ? "empty" : "percent",
        textAlignValue: "right",
      },
      { label: "4. Tỷ lệ thưởng", value: monthlyBonus?.bonusRate, type: "percent" },
      {
        label: "5. Thực nhận tháng này: (*)",
        value: monthlyBonus?.mpbFwp,
        type: "number",
      },
      {
        label: "6. Các khoản điều chỉnh (nếu có)",
        value: monthlyBonus?.otherAdjustment,
        type: "number",
      },
    ];
  }, [monthlyBonus]);

  const detailContactHead: HeadTableSOA[][] = useMemo(() => {
    return [
      [
        { label: "Chi tiết", colSpan: 3, isTitleMobile: true, isNotMapping: true },
        { key: "fyp", label: formatNumber(policyBonusMonth?.totalFyp), isNotMapping: true, textAlign: "right" },
        { key: "fyc", label: formatNumber(policyBonusMonth?.totalFyc), isNotMapping: true, textAlign: "right" },
      ],
      [
        { key: "agentCode", label: "Đại lý khai thác	", type: "string" },
        { key: "policynumber", label: "Số hợp đồng", type: "string" },
        { key: "owName", label: "Bên mua BH", type: "string" },
        { key: "fyp", label: "Phí bảo hiểm năm nhất", type: "number" },
        { key: "fyc", label: "Hoa hồng năm nhất", type: "number" },
      ],
    ];
  }, [policyBonusMonth]);

  const policyBonusMonthData: TableSOAData[][] = useMemo(() => {
    return monthlyBonusDetail?.map((data) =>
      filterMapping(detailContactHead).map((head) => ({
        config: head,
        node: formatValueTable(data, head),
        originData: get(data, [head.key]),
      }))
    );
  }, [monthlyBonusDetail, detailContactHead]);

  const tooltipDetailContact = useMemo(() => {
    const tooltipList = detailContactHead[0]
      .filter((filter) => !filter.isTitleMobile)
      .map((item) => ({
        label: detailContactHead[1].find((find) => find.key === item.key)?.label,
        value: item.label,
      }));

    return (
      <FuncHeaderSOA icon="tooltip">
        {tooltipList.map((item, index) => (
          <RowDataTooltip key={index}>
            <label className="label-4">{item.label}</label>
            <h6 className="h7">{item.value}</h6>
          </RowDataTooltip>
        ))}
      </FuncHeaderSOA>
    );
  }, [detailContactHead]);

  const dropdownGuild = useMemo(() => {
    return (
      <FuncHeaderSOA icon="icon-up-down" iconFill="white" trigger={titleDropdownMobile}>
        {rewardMonthData.map((item, index) => (
          <RowDataDropdown key={index} onClick={() => handleSelectDropdownGuild(item.totalFYP)}>
            <p className="body-4">{item.totalFYP}</p>
            {titleDropdownMobile === item.totalFYP ? <Icons icon="check-icon" /> : null}
          </RowDataDropdown>
        ))}
      </FuncHeaderSOA>
    );
  }, [titleDropdownMobile]);

  const handleSelectDropdownGuild = (item: string) => {
    setTitleDropdownMobile(item);
  };
  return (
    <Modal title="Chi tiết Thưởng tháng" show={show} size="slg" onClose={onClose}>
      <p className="body-4 mb-24 sm-ml-16">
        Tỷ lệ thưởng áp dụng từ <span className="color-primary">01/04/2024</span>
      </p>
      <TableSOAContainer>
        <TableSOA
          titleMobile={titleDropdownMobile}
          head={monthRewardHead}
          data={monthRewardData}
          renderIconMobile={dropdownGuild}
        />
        <div className="mb-20">
          <Note
            title=" Ghi chú:"
            type="list"
            content={[
              "TVTC mới gia nhập sau ngày 10 của tháng, tháng xét thưởng sẽ là tháng tiếp theo tháng gia nhập.",
              "Đối với TVTC có thời gian làm việc từ 14 tháng trở xuống hoặc chưa có K2 thì sẽ áp dụng K2 là 60%.",
            ]}
            backgroundColor="#EDEFF0"
          />
        </div>
        <TableNoHeader data={monthRewardListData} />
        <TableSOA
          titleMobile={findTitleMobileSOA(detailContactHead)}
          head={detailContactHead}
          data={policyBonusMonthData}
          renderIconMobile={tooltipDetailContact}
        />
      </TableSOAContainer>
    </Modal>
  );
};

export default MonthBonusModal;
