import { PolicyBonusAgency } from "@custom-types/soa";
import TableCollapse, { HeadTableCollapse, TableCollapseData } from "components/table-collapse";
import TableCollapseMobile from "components/table-collapse-mobile";
import { useAppSelector } from "hooks/use-redux";
import { useMemo } from "react";
import { ModalRewardProps } from "../..";
import QMBonus from "./policy-bonus-QM-modal";
import ManagementRewardModal from "./policy-bonus-manager-modal";
import MonthBonusModal from "./policy-bonus-month-modal";
import PersistencyBonusModal from "./policy-bonus-persistency-modal";
import QuarterBonusModal from "./policy-bonus-quarter-modal";
import PolicyBonusSupportTeamModal from "./policy-bonus-support-team";
import PolicyLeaderPersisbonModal from "./policy-leader-persisbon";

const policyBonusHead: HeadTableCollapse[][] = [
  [
    { label: "<PERSON><PERSON><PERSON> thưởng theo ch<PERSON>h sách", colSpan: 2 },
    { label: "Thu thập trong kỳ", colSpan: 2 },
  ],
];

const PolicyRewardAgency = ({ show, rowKey, onViewDetail, onClose }: ModalRewardProps) => {
  const {
    loading: { getSOALoading },
  } = useAppSelector((state) => state.rootReducer);
  const { policyBonus } = useAppSelector((state) => state.performanceReducer);

  const policyBonusData: TableCollapseData[] = useMemo(() => {
    const policyBonusAgency = policyBonus as PolicyBonusAgency;
    return [
      {
        label: "1. Các khoản thưởng dành cho TVTC",
        value: policyBonusAgency?.bonusAgent,
        type: "number",
        colSpan: 2,
        children: [
          {
            rowKey: "month-bonus",
            label: "Thưởng tháng",
            value: policyBonusAgency?.monthBonus,
            type: "number",
            colSpan: 2,
            detail: "Xem chi tiết",
          },
          {
            rowKey: "quarter-bonus",
            label: "Thưởng quý cá nhân",
            value: policyBonusAgency?.bonusQuarter,
            type: "number",
            colSpan: 2,
            detail: "Xem chi tiết",
          },
          // {
          //   label: "Thưởng thành viên CLB 590",
          //   value: policyBonusAgency?.clb590Bonus,
          //   type: "number",
          //   colSpan: 2,
          // },
          {
            rowKey: "persistency-bonus",
            label: "Thưởng duy trì hợp đồng bảo hiểm",
            value: policyBonusAgency?.bonusMaintainPol,
            type: "number",
            colSpan: 2,
            detail: "Xem chi tiết",
          },
        ],
      },
      {
        label: "2. Các khoản thưởng dành cho quản lý TVTC",
        value: policyBonusAgency?.incomeManageAgent,
        type: "number",
        colSpan: 2,
        children: [
          {
            rowKey: "management-bonus",
            label: "Thù lao quản lý hoạt động khai thác mới nhóm trực tiếp",
            value: policyBonusAgency?.bonusNewManage,
            type: "number",
            colSpan: 2,
            detail: "Xem chi tiết",
          },
          {
            rowKey: "bonus-support-team",
            label: "Thù lao hỗ trợ nhóm",
            value: policyBonusAgency?.bonusSupportTeam,
            type: "number",
            colSpan: 2,
            detail: "Xem chi tiết",
          },
          {
            rowKey: "leader-persisbon",
            label: "Thù lao hỗ trợ thu phí tái tục năm hai",
            value: policyBonusAgency?.leaderPersisbon,
            type: "number",
            colSpan: 2,
            detail: "Xem chi tiết",
          },
          // {
          //   rowKey: "dev590-bonus",
          //   label: "Thù lao phát triển thành viên CLB 590",
          //   value: policyBonusAgency?.dev590Bonus,
          //   type: "number",
          //   colSpan: 2,
          //   detail: "Xem chi tiết",
          // },
          {
            rowKey: "qm-bonus",
            label: "Thù lao hàng quý",
            value: policyBonusAgency?.bonusQuarterManager,
            type: "number",
            colSpan: 2,
            detail: "Xem chi tiết",
          },
          {
            label: "Thù lao phát triển quản lý cùng cấp",
            value: policyBonusAgency?.bonusPromotion,
            type: "number",
            colSpan: 2,
          },
          {
            label: "Thù lao phát triển đa thế hệ FWD",
            value: policyBonusAgency?.lifeBenIncome,
            type: "number",
            colSpan: 2,
          },
          {
            label: "Thù lao phát triển quản lý mới FWO",
            value: policyBonusAgency?.bonusNewDevelop,
            type: "number",
            colSpan: 2,
          },
          {
            label: "Quyền lợi chuyển giao đội ngũ",
            value: policyBonusAgency?.bonusTransferFWD,
            type: "number",
            colSpan: 2,
          },
        ],
      },
    ];
  }, [policyBonus]);

  const ModalContent = useMemo(() => {
    switch (rowKey) {
      case "month-bonus":
        return MonthBonusModal; //Thưởng tháng
      case "quarter-bonus":
        return QuarterBonusModal; //Thưởng quý cá nhân
      case "persistency-bonus":
        return PersistencyBonusModal; //Thưởng duy trì hợp đồng bảo hiểm
      case "management-bonus":
        return ManagementRewardModal; //Thù lao quản lý hoạt động khai thác mới
      //Hide in CUBE-485
      // case "dev590-bonus":
      //   return Dev590Bonus; //Thù lao phát triển thành viên CLB 590
      case "qm-bonus":
        return QMBonus; //Thù lao hàng quý
      case "bonus-support-team":
        return PolicyBonusSupportTeamModal; //Thù lao hỗ trợ nhóm
      case "leader-persisbon":
        return PolicyLeaderPersisbonModal; //Thù lao hỗ trợ thu phí tái tục năm hai
      default:
        return null;
    }
  }, [rowKey]);

  return (
    <div>
      <TableCollapse
        head={policyBonusHead}
        body={policyBonusData}
        onViewDetail={onViewDetail}
        loading={getSOALoading}
      />
      <TableCollapseMobile head={policyBonusHead} body={policyBonusData} onViewDetail={onViewDetail} />
      {ModalContent ? <ModalContent show={show} onClose={onClose} /> : null}
    </div>
  );
};

export default PolicyRewardAgency;
