import { ValueLabel } from "@custom-types";
import Icons from "components/icons";
import Modal from "components/modal";
import TableSOANoHeader from "components/table-no-header";
import TableSOA, { HeadTableSOA, TableSOAData } from "components/table-soa";
import FuncHeaderSOA from "components/table-soa/header-mobile-soa";
import { RowDataDropdown, RowDataTooltip } from "components/table-soa/header-mobile-soa/styled";
import { TableSOAContainer } from "components/table-soa/styled";
import { useAppSelector } from "hooks/use-redux";
import useWindowResize from "hooks/use-window-resize";
import { get } from "lodash";
import { useMemo, useState } from "react";
import { findTitleMobileSOA } from "sections/performance/components/income-info";
import { formatValueTable } from "services/format-value";
import { filterMapping, formatNumber, sumCalculator } from "services/untils";
import { ModalPolicyProps } from "../..";

interface TableDataDefault {
  key?: string;
  col1?: string;
  col2?: string;
}

export const table1Config: HeadTableSOA[][] = [
  [
    { key: "col1", label: "Cấp báo cáo" },
    { key: "col2", label: "Hệ số nhân thù lao quản lý khai thác mới nhóm trực tiếp", type: "string" },
  ],
];

export const bonusSupportTeamData: TableDataDefault[] = [
  { key: "row1", col1: "Cấp quản lý báo cáo cấp 1", col2: "50%" },
  { key: "row2", col1: "Cấp quản lý báo cáo cấp 2", col2: "25%" },
];

const PolicyBonusSupportTeamModal = ({ show, onClose }: ModalPolicyProps) => {
  const [titleMobileTable1, setTitleMobileTable1] = useState(bonusSupportTeamData.find((item) => item.key === "row1"));
  const { width } = useWindowResize();
  const { policyBonusSupportTeam } = useAppSelector((state) => state.performanceReducer);

  const detectDataTable1: any[] = useMemo(() => {
    if (width <= 768) {
      //Data for mobile
      return bonusSupportTeamData.filter((item) => item.key === titleMobileTable1?.key);
    } else {
      //Data for desktop
      return bonusSupportTeamData;
    }
  }, [width, titleMobileTable1]);

  const formatTable1Data: TableSOAData[][] = useMemo(() => {
    return detectDataTable1.map((data) =>
      filterMapping(table1Config).map((head) => ({
        config: head,
        node: formatValueTable(data, head),
        originData: get(data, [head.key]),
      }))
    );
  }, [detectDataTable1]);

  const table5Data: ValueLabel[] = useMemo(() => {
    const data = policyBonusSupportTeam?.indirectORY1;
    return [
      {
        label: "1. Thù lao quản lý khai thác mới nhóm trực tiếp của Cấp quản lý báo cáo cấp 1",
        value: data?.valueTier2,
        colSpan: 3,
        type: "number",
      },
      {
        label: "2. Thù lao quản lý khai thác mới nhóm trực tiếp của Cấp quản lý báo cáo cấp 2",
        value: data?.valueTier3,
        colSpan: 3,
        type: "number",
      },
      {
        label: "3. Thực nhận tháng này: (*)",
        value: data?.incentive_Indirect,
        colSpan: 3,
        type: "number",
      },
      {
        label: "4. Các khoản điều chỉnh (nếu có)",
        value: data?.adjustAmount,
        colSpan: 3,
        type: "number",
      },
    ];
  }, [policyBonusSupportTeam]);

  const table6Config: HeadTableSOA[][] = useMemo(() => {
    return [
      [
        {
          label: "CHI TIẾT",
          colSpan: 4,
          isTitleMobile: true,
          isNotMapping: true,
        },
        {
          key: "",
          label: formatNumber(sumCalculator(policyBonusSupportTeam?.indirectORY1Details, "incentive")),
          isNotMapping: true,
          type: "number",
          textAlign: "right",
        },
      ],
      [
        { key: "agentId", label: "Cấp quản lý", rowSpan: 2 },
        { key: "sourceAgentId", label: "Cấp quản lý báo cáo", rowSpan: 2 },
        { key: "designationCd", label: "Chức danh Cấp quản lý báo cáo", rowSpan: 2 },
        { key: "multiFactor", label: "Hệ số nhân", rowSpan: 2, type: "percent" },
        {
          key: "incentive",
          label: "Thù lao quản lý khai thác mới nhóm trực tiếp của Cấp quản lý báo cáo",
          rowSpan: 2,
          type: "number",
        },
      ],
    ];
  }, [policyBonusSupportTeam]);

  const formatTable6Data: TableSOAData[][] = useMemo(() => {
    return policyBonusSupportTeam?.indirectORY1Details.map((data) =>
      filterMapping(table6Config).map((head) => ({
        config: head,
        node: formatValueTable(data, head),
        originData: get(data, [head.key]),
      }))
    );
  }, [table6Config, policyBonusSupportTeam]);

  const table6Tooltip = useMemo(() => {
    const config = [...table6Config[1]];

    const tooltipList = table6Config[0]
      .filter((filter) => !filter.isTitleMobile)
      .map((item) => ({
        label: config.find((find) => find.key === item.key)?.label,
        value: item.label,
      }));

    return (
      <FuncHeaderSOA icon="tooltip">
        {tooltipList.map((item, index) => (
          <RowDataTooltip key={index}>
            <label className="label-4">{item.label}</label>
            <h6 className="h7">{item.value}</h6>
          </RowDataTooltip>
        ))}
      </FuncHeaderSOA>
    );
  }, [table6Config]);

  const dropdownMobileTable1 = useMemo(() => {
    return (
      <FuncHeaderSOA icon="icon-up-down" iconFill="white" trigger={titleMobileTable1}>
        {bonusSupportTeamData.map((item, index) => (
          <RowDataDropdown key={index} onClick={() => setTitleMobileTable1(item)}>
            <p className="body-4">{item.col1}</p>
            {titleMobileTable1?.key === item.key ? <Icons icon="check-icon" /> : null}
          </RowDataDropdown>
        ))}
      </FuncHeaderSOA>
    );
  }, [titleMobileTable1]);

  return (
    <Modal title="Chi tiết Thù lao hỗ trợ nhóm" show={show} size="slg" onClose={onClose}>
      <p className="body-4 mb-24 sm-ml-16">
        Tỷ lệ thưởng áp dụng từ <span className="color-primary">01/04/2024</span>
      </p>
      <TableSOAContainer>
        {table1Config.length > 0 && (
          <TableSOA
            titleMobile={titleMobileTable1?.col1}
            head={table1Config}
            data={formatTable1Data}
            renderIconMobile={dropdownMobileTable1}
          />
        )}

        <TableSOANoHeader data={table5Data} />

        <TableSOA
          titleMobile={findTitleMobileSOA(table6Config)}
          head={table6Config}
          data={formatTable6Data}
          renderIconMobile={table6Tooltip}
        />
      </TableSOAContainer>
    </Modal>
  );
};

export default PolicyBonusSupportTeamModal;
