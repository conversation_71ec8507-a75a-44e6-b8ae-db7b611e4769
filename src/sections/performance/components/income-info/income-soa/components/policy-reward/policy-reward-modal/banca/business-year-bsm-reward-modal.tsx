import Modal from "components/modal";
import TableSOA, { HeadTableSOA, TableSOAData } from "components/table-soa";
import { useAppSelector } from "hooks/use-redux";
import { get } from "lodash";
import { useMemo } from "react";
import { findTitleMobileSOA } from "sections/performance/components/income-info";
import { formatValueTable } from "services/format-value";
import { filterMapping } from "services/untils";
import { ModalPolicyProps } from "../..";
import { TableSOAContainer } from "components/table-soa/styled";

const detailBusinessYearHead: HeadTableSOA[][] = [
  [
    {
      label: "Chi tiết hợp đồng thưởng theo tháng theo doanh số năm nhất / năm hai / quý / năm",
      colSpan: 4,
      isTitleMobile: true,
      isNotMapping: true,
    },
  ],
  [
    { key: "polNum", label: "<PERSON><PERSON> hợp đồng", type: "string" },
    { key: "issuedDate", label: "<PERSON><PERSON><PERSON> phát hành lần đầu", type: "date" },
    { key: "insuranceFee", label: "Phí BH (VNĐ)", type: "number" },
    { key: "ackDate", label: "Ngày xác nhận ACK", type: "date" },
  ],
];

const BusinessYearBSMRewardModal = ({ show, onClose }: ModalPolicyProps) => {
  const { bonusByYear } = useAppSelector((state) => state.performanceReducer);

  const detailBusinessYearData: TableSOAData[][] = useMemo(() => {
    return bonusByYear?.listData?.map((data) =>
      filterMapping(detailBusinessYearHead).map((head) => ({
        config: head,
        node: formatValueTable(data, head),
        originData: get(data, [head.key]),
      }))
    );
  }, [bonusByYear?.listData]);

  return (
    <Modal title="Chi tiết Thưởng kinh doanh năm" show={show} size="slg" onClose={onClose}>
      <TableSOAContainer>
        <TableSOA
          titleMobile={findTitleMobileSOA(detailBusinessYearHead)}
          head={detailBusinessYearHead}
          data={detailBusinessYearData}
        />
      </TableSOAContainer>
    </Modal>
  );
};

export default BusinessYearBSMRewardModal;
