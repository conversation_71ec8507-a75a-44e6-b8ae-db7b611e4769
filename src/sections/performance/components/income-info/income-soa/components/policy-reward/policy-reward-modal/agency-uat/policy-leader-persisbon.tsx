import { ValueLabel } from "@custom-types";
import Modal from "components/modal";
import TableSOANoHeader from "components/table-no-header";
import TableSOA, { HeadTableSOA, TableSOAData } from "components/table-soa";
import FuncHeaderSOA from "components/table-soa/header-mobile-soa";
import { RowDataTooltip } from "components/table-soa/header-mobile-soa/styled";
import { TableSOAContainer } from "components/table-soa/styled";
import { get } from "lodash";
import { useMemo } from "react";
import { findTitleMobileSOA } from "sections/performance/components/income-info";
import { formatValueTable } from "services/format-value";
import { filterMapping, formatNumber, sumCalculator } from "services/untils";
import { ModalPolicyProps } from "../..";
import { NotedCalculate } from "../../styled";
import { useAppSelector } from "hooks/use-redux";

const PolicyLeaderPersisbonModal = ({ show, onClose }: ModalPolicyProps) => {
  const { policyBonusLeaderPersisbon } = useAppSelector((state) => state.performanceReducer);

  const table5Data: ValueLabel[] = useMemo(() => {
    const data = policyBonusLeaderPersisbon?.persistencyLeader;
    return [
      {
        label: "1. Tổng thưởng duy trì hợp đồng bảo hiểm thực nhận của cấp báo cáo liền kề",
        value: data?.persistencyBonus,
        colSpan: 3,
        type: "number",
      },
      {
        label: "2. Hệ số nhân (%)",
        value: data?.multiFactor,
        colSpan: 3,
        type: "percent",
      },
      {
        label: "3. Thực nhận tháng này: (*)",
        value: data?.leaderPersisBonus,
        colSpan: 3,
        type: "number",
      },
      {
        label: "4. Các khoản điều chỉnh (nếu có)",
        value: data?.adjustAmount,
        colSpan: 3,
        type: "number",
      },
    ];
  }, [policyBonusLeaderPersisbon]);

  const table6Config: HeadTableSOA[][] = useMemo(() => {
    return [
      [
        {
          label: "CHI TIẾT",
          colSpan: 3,
          isTitleMobile: true,
          isNotMapping: true,
        },
        {
          key: "",
          label: formatNumber(sumCalculator(policyBonusLeaderPersisbon?.persistencyLeaderDetails, "persistencyBonus")),
          isNotMapping: true,
          type: "number",
          textAlign: "right",
        },
      ],
      [
        { key: "agentId", label: "Cấp quản lý", rowSpan: 2 },
        { key: "sourceAgentId", label: "Mã số Cấp báo cáo liền kề", rowSpan: 2 },
        { key: "designationCd", label: "Chức danh cấp báo cáo liền kề", rowSpan: 2 },
        { key: "persistencyBonus", label: "Thưởng duy trì hợp đồng bảo hiểm thực nhận", rowSpan: 2, type: "number" },
      ],
    ];
  }, [policyBonusLeaderPersisbon]);

  const formatTable6Data: TableSOAData[][] = useMemo(() => {
    return policyBonusLeaderPersisbon?.persistencyLeaderDetails.map((data) =>
      filterMapping(table6Config).map((head) => ({
        config: head,
        node: formatValueTable(data, head),
        originData: get(data, [head.key]),
      }))
    );
  }, [table6Config, policyBonusLeaderPersisbon]);

  const table6Tooltip = useMemo(() => {
    const config = [...table6Config[1]];

    const tooltipList = table6Config[0]
      .filter((filter) => !filter.isTitleMobile)
      .map((item) => ({
        label: config.find((find) => find.key === item.key)?.label,
        value: item.label,
      }));

    return (
      <FuncHeaderSOA icon="tooltip">
        {tooltipList.map((item, index) => (
          <RowDataTooltip key={index}>
            <label className="label-4">{item.label}</label>
            <h6 className="h7">{item.value}</h6>
          </RowDataTooltip>
        ))}
      </FuncHeaderSOA>
    );
  }, [table6Config]);

  return (
    <Modal title="Thù lao hỗ trợ thu phí tái tục năm hai" show={show} size="slg" onClose={onClose}>
      <TableSOAContainer>
        <NotedCalculate>
          <p className="body-2 color-primary">
            Thù lao hỗ trợ thu phí tái tục năm hai= 50% x [Thưởng duy trì <br className="hide-mobile" /> hợp đồng bảo
            hiểm thực nhận của cấp báo cáo liền kề]
          </p>
        </NotedCalculate>
        <TableSOANoHeader data={table5Data} />

        <TableSOA
          titleMobile={findTitleMobileSOA(table6Config)}
          head={table6Config}
          data={formatTable6Data}
          renderIconMobile={table6Tooltip}
        />
      </TableSOAContainer>
    </Modal>
  );
};

export default PolicyLeaderPersisbonModal;
