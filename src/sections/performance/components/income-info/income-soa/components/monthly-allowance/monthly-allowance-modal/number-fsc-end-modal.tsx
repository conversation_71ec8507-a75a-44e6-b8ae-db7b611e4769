import Modal from "components/modal";
import TableSOA, { HeadTableSOA, TableSOAData } from "components/table-soa";
import { useAppSelector } from "hooks/use-redux";
import { get } from "lodash";
import { useMemo } from "react";
import { findTitleMobileSOA } from "sections/performance/components/income-info";
import { formatValueTable } from "services/format-value";
import { filterMapping } from "services/untils";
import { ModalMonthlyProps } from "..";
import { TableSOAContainer } from "components/table-soa/styled";

const listPGNEndHead: HeadTableSOA[][] = [
  [{ label: "Danh sách FSC", colSpan: 3, isTitleMobile: true, isNotMapping: true }],
  [
    { key: "fscCode", label: "Mã FSC" },
    { key: "fscName", label: "Tên FSC", colSpan: 2 },
  ],
];

const NumberFSCEndModal = ({ show, onClose }: ModalMonthlyProps) => {
  const { listData } = useAppSelector((state) => state.performanceReducer.bancaAllowanceFSC);

  const listPGNEndData: TableSOAData[][] = useMemo(() => {
    return listData?.map((data) =>
      filterMapping(listPGNEndHead).map((head) => ({
        config: head,
        node: formatValueTable(data, head),
        originData: get(data, [head.key]),
      }))
    );
  }, [listData]);

  return (
    <Modal title="Chi tiết Số lượng FSC cuối kỳ" show={show} size="lg" onClose={onClose}>
      <TableSOAContainer>
        <TableSOA titleMobile={findTitleMobileSOA(listPGNEndHead)} head={listPGNEndHead} data={listPGNEndData} />
      </TableSOAContainer>
    </Modal>
  );
};

export default NumberFSCEndModal;
