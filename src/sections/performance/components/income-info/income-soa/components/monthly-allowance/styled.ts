import styled from "styled-components";
import { FlexBox } from "styles";
import { device } from "styles/media";

export const MonthlyAllowanceWrapper = styled.div``;

export const MonthlyFlex = styled(FlexBox)`
  background: ${({ theme }) => theme.color.status.yellow_20};
  color: ${({ theme }) => theme.color.status.primary};

  border-radius: 4px;

  padding: 22.5px;
  margin-bottom: 20px;

  @media ${device.mobile} {
    padding: 6px 16px;

    h6 {
      font-size: 16px;
    }
  }
`;
export const MonthlyItem = styled.div`
  display: flex;

  &:nth-child(2) {
    align-items: center;
    width: 100%;
    margin-left: 16px;
    justify-content: space-between;
  }

  @media ${device.mobile} {
    &:nth-child(2) {
      align-items: flex-start;
      flex-direction: column;
    }
  }
`;

export const MonthlyAllowanceModal = styled.div``;
