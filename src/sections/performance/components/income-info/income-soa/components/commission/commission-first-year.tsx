import TableSOA, { HeadTableSOA, TableSOAData } from "components/table-soa";
import FuncHeaderSOA from "components/table-soa/header-mobile-soa";
import { RowDataTooltip } from "components/table-soa/header-mobile-soa/styled";
import { useAppSelector } from "hooks/use-redux";
import { get } from "lodash";
import { useMemo } from "react";
import { formatValueTable } from "services/format-value";
import { filterMapping, formatNumber } from "services/untils";
import { findTitleMobileSOA } from "../../..";

interface CommissionFirstYearProps {}

const CommissionFirstYear = ({}: CommissionFirstYearProps) => {
  const {
    loading: { getSOALoading },
  } = useAppSelector((state) => state.rootReducer);
  const { commissionFirstYear } = useAppSelector((state) => state.performanceReducer);

  const firstCommissionHead: HeadTableSOA[][] = useMemo(() => {
    const { totalFirstYearCom } = commissionFirstYear;

    return [
      [
        { label: "Hoa hồng năm nhất", colSpan: 3, isTitleMobile: true, isNotMapping: true },
        { key: "premium", label: formatNumber(totalFirstYearCom?.premium), isNotMapping: true, textAlign: "right" },
        {
          key: "commission",
          label: formatNumber(totalFirstYearCom?.commission),
          isNotMapping: true,
          textAlign: "right",
        },
        {
          key: "bonusBenefit",
          label: formatNumber(totalFirstYearCom?.bonusBenefit),
          isNotMapping: true,
          textAlign: "right",
        },
        { key: "totalPay", label: formatNumber(totalFirstYearCom?.totalPay), isNotMapping: true, textAlign: "right" },
      ],
      [
        { key: "polnum", label: "Số hợp đồng", type: "string" },
        { key: "owName", label: "Bên mua BH", type: "string" },
        { key: "incurredDate", label: "Ngày phát sinh phí", type: "date" },
        { key: "premium", label: "Phí BH (VNĐ)", type: "number" },
        { key: "commission", label: "Hoa hồng thanh toán (VNĐ)", type: "number" },
        { key: "bonusBenefit", label: "Thưởng quyền lợi sản phẩm bổ trợ (VNĐ)", type: "number" },
        { key: "totalPay", label: "Tổng thanh toán  (VNĐ)", type: "number" },
      ],
    ];
  }, [commissionFirstYear]);

  const firstYearDataTable: TableSOAData[][] = useMemo(() => {
    return commissionFirstYear?.lstComFirstYear?.map((data) =>
      filterMapping(firstCommissionHead).map((head) => ({
        config: head,
        node: formatValueTable(data, head),
        originData: get(data, [head.key]),
      }))
    );
  }, [commissionFirstYear, firstCommissionHead]);

  const secondCommissionHead: HeadTableSOA[][] = useMemo(() => {
    const { totalRemainCom } = commissionFirstYear;

    return [
      [
        { label: "Hoa hồng tái tục", colSpan: 3, isTitleMobile: true, isNotMapping: true },
        { key: "premium", label: formatNumber(totalRemainCom?.premium), isNotMapping: true, textAlign: "right" },
        {
          key: "commission",
          label: formatNumber(totalRemainCom?.commission),
          isNotMapping: true,
          textAlign: "right",
        },
        {
          key: "bonusBenefit",
          label: formatNumber(totalRemainCom?.bonusBenefit),
          isNotMapping: true,
          textAlign: "right",
        },
        { key: "totalPay", label: formatNumber(totalRemainCom?.totalPay), isNotMapping: true, textAlign: "right" },
      ],
      [
        { key: "polnum", label: "Số hợp đồng", type: "string" },
        { key: "owName", label: "Bên mua BH", type: "string" },
        { key: "incurredDate", label: "Ngày phát sinh phí", type: "date" },
        { key: "premium", label: "Phí BH (VNĐ)", type: "number" },
        { key: "commission", label: "Hoa hồng thanh toán (VNĐ)", type: "number" },
        { key: "bonusBenefit", label: "Thưởng quyền lợi sản phẩm bổ trợ (VNĐ)", type: "number" },
        { key: "totalPay", label: "Tổng thanh toán (VNĐ)", type: "number" },
      ],
    ];
  }, [commissionFirstYear]);

  const secondCommissionDataTable: TableSOAData[][] = useMemo(() => {
    return commissionFirstYear?.lstRemainCom?.map((data) =>
      filterMapping(secondCommissionHead).map((head) => ({
        config: head,
        node: formatValueTable(data, head),
        originData: get(data, [head.key]),
      }))
    );
  }, [commissionFirstYear?.lstRemainCom, secondCommissionHead]);

  const tooltipFirst = useMemo(() => {
    const tooltipList = firstCommissionHead[0]
      .filter((filter) => !filter.isTitleMobile)
      .map((item) => ({
        label: firstCommissionHead[1].find((find) => find.key === item.key)?.label,
        value: item.label,
      }));

    return (
      <FuncHeaderSOA icon="tooltip">
        {tooltipList.map((item, index) => (
          <RowDataTooltip key={index}>
            <label className="label-4">{item.label}</label>
            <h6 className="h7">{item.value}</h6>
          </RowDataTooltip>
        ))}
      </FuncHeaderSOA>
    );
  }, [firstCommissionHead]);

  const tooltipSecond = useMemo(() => {
    const tooltipList = secondCommissionHead[0]
      .filter((filter) => !filter.isTitleMobile)
      .map((item) => ({
        label: secondCommissionHead[1].find((find) => find.key === item.key)?.label,
        value: item.label,
      }));

    return (
      <FuncHeaderSOA icon="tooltip">
        {tooltipList.map((item, index) => (
          <RowDataTooltip key={index}>
            <label className="label-4">{item.label}</label>
            <h6 className="h7">{item.value}</h6>
          </RowDataTooltip>
        ))}
      </FuncHeaderSOA>
    );
  }, [secondCommissionHead]);

  return (
    <>
      <TableSOA
        titleMobile={findTitleMobileSOA(firstCommissionHead)}
        head={firstCommissionHead}
        data={firstYearDataTable}
        renderIconMobile={tooltipFirst}
        loading={getSOALoading}
        isStickyHeader
      />
      <TableSOA
        titleMobile={findTitleMobileSOA(secondCommissionHead)}
        head={secondCommissionHead}
        data={secondCommissionDataTable}
        renderIconMobile={tooltipSecond}
        loading={getSOALoading}
        isStickyHeader
      />
    </>
  );
};

export default CommissionFirstYear;
