import { useCallback, useMemo, useState } from "react";
import { Spacing } from "styles";
import { ButtonPrimary, ButtonSecondary } from "styles/buttons";
import CommissionDetail from "./commission-detail";
import CommissionFirstYear from "./commission-first-year";
import { ButtonWrapper, CommissionWrapper } from "./styled";
import { useDispatch } from "react-redux";
import { getSOAAction } from "screens/performance/action";
import { useAppSelector } from "hooks/use-redux";

type CommissionType = "commission-tab" | "commission-detail";

const Commission = () => {
  const [commissionType, setCommissionType] = useState<CommissionType>("commission-tab");

  const { username } = useAppSelector((state) => state.rootReducer.user);
  const { period } = useAppSelector((state) => state.performanceReducer.incomeSOAForm);

  const dispatch = useDispatch();

  const handleChangeCommissionType = useCallback(
    (type: CommissionType) => {
      setCommissionType(type);

      dispatch(
        getSOAAction({
          payload: {
            agentId: username,
            paymentDt: period.value,
          },
          type: type,
        })
      );
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [username, period]
  );

  const Content = useMemo(() => {
    switch (commissionType) {
      case "commission-tab":
        return CommissionFirstYear;
      case "commission-detail":
        return CommissionDetail;
      default:
        return null;
    }
  }, [commissionType]);

  return (
    <CommissionWrapper>
      {Content ? <Content /> : null}
      <ButtonWrapper>
        {commissionType === "commission-detail" ? (
          <ButtonSecondary long onClick={() => handleChangeCommissionType("commission-tab")}>
            Quay lại
          </ButtonSecondary>
        ) : null}
        <Spacing />
        {commissionType === "commission-tab" ? (
          <ButtonPrimary long onClick={() => handleChangeCommissionType("commission-detail")}>
            Chi tiết hoa hồng
          </ButtonPrimary>
        ) : null}
      </ButtonWrapper>
    </CommissionWrapper>
  );
};

export default Commission;
