import { ValueLabel } from "@custom-types";
import { DataTable, TableConfig } from "@custom-types/config-table";
import { ErrorPolicy } from "@custom-types/policy-tracking";
import { CommissionProvisionalData, CommissionProvisionalInput, CommissionProvisionalOutput } from "@custom-types/soa";
import { getCommissionProvisional } from "api/soa";
import Block from "components/block";
import { BlockContentWrapper } from "components/block/styled";
import DropDownFieldset from "components/drop-down-fieldset";
import FormSearchTemplate from "components/form-search-template";
import DateInput from "components/input-date";
import InputFieldSet from "components/input-fileldset";
import Table from "components/table";
import TableMobile from "components/table-mobile";
import { THCustom } from "components/table/custom-table/styled";
import useActionApi from "hooks/use-action-api";
import { useAppSelector } from "hooks/use-redux";
import { cloneDeep, get } from "lodash";
import { useCallback, useMemo, useState } from "react";
import { formatValueTable } from "services/format-value";
import { formatDateRequest, formatNumber, getDateFrom, getDateTo, handleCheckValidateMonthForm } from "services/untils";
import { RowItem } from "styles";
import { TR } from "styles/table";

export interface CommissionData {
  policyNo: string;
  fullnameOW: string;
  prdtCode: string;
  prdtDesc: string;
  issueFirstDate: string;
  dateOfChange: string;
  reviewOf21days: string;
  ackDate: string;
  totalPremium: number;
  commissionRate: string;
  temporaryCommission: number;
  prdtReward: number;
  commissionType: string;
}

const dateTypeList: ValueLabel[] = [
  { value: "FIRSTISSUE", label: "Ngày phát hành lần đầu" },
  { value: "INCURRED", label: "Ngày phát sinh phí" },
  { value: "ACK", label: "Ngày xác nhận ACK" },
];

const configCommission: TableConfig[] = [
  { key: "polnum", show: true, type: "string", label: "Số hợp đồng", sort: true },
  { key: "owName", show: true, type: "string", label: "Bên mua BH", sort: true },
  { key: "productCode", show: true, type: "string", label: "Mã sản phẩm", sort: true },
  { key: "productName", show: true, type: "string", label: "Tên sản phẩm", sort: true },
  { key: "firstIssueDate", show: true, type: "date", label: "Ngày phát hành lần đầu", sort: true },
  { key: "incurredDate", show: true, type: "date", label: "Ngày phát sinh phí", sort: true },
  { key: "ackDate", show: true, type: "date", label: "Ngày xác nhận ACK", sort: true },
  { key: "date21", show: true, type: "date", label: "Thời điểm qua 21 ngày xem xét", sort: true },
  { key: "premium", show: true, type: "number", label: "Phí BH (VNĐ)", sort: true, textAlign: "right" },
  { key: "ratioCom", show: true, type: "percent", label: "Tỉ lệ hoa hồng (%)", sort: true, textAlign: "right" },
  { key: "commission", show: true, type: "number", label: "Hoa hồng tạm tính (VNĐ)", sort: true, textAlign: "right" },
  {
    key: "bonusBenefit",
    show: true,
    type: "number",
    label: "Thưởng quyền lợi sản phẩm bổ trợ tạm tính (VNĐ)",
    sort: true,
    textAlign: "right",
  },
  { key: "comType", show: true, type: "string", label: "Loại hoa hồng", sort: true },
];

const Commission = () => {
  const [formSearch, setFormSearch] = useState<CommissionProvisionalInput>({
    agentId: "",
    dateType: dateTypeList.find((item) => item.value === "INCURRED"),
    owName: "",
    policyNo: "",
    fromDate: getDateFrom(),
    toDate: getDateTo(),
  });
  const [error, setError] = useState<ErrorPolicy>({
    fromDate: null,
    toDate: null,
  });
  const [dataCommission, setDataCommission] = useState<CommissionProvisionalData>({
    provisionalComTotal: 0,
    totalProvisionalCom: null,
    lstProvisionalCom: [],
  });

  const { username } = useAppSelector((state) => state.rootReducer.user);
  const { getCommissionProvisionalLoading } = useAppSelector((state) => state.rootReducer.loading);

  const actionGetCommissionProvisional = useActionApi<CommissionProvisionalInput, CommissionProvisionalOutput>(
    getCommissionProvisional
  );

  const handleSubmit = useCallback(() => {
    let payload = cloneDeep(formSearch);

    payload.agentId = username;
    payload.dateType = payload.dateType.value;
    payload.fromDate = formatDateRequest(payload.fromDate);
    payload.toDate = formatDateRequest(payload.toDate);

    actionGetCommissionProvisional({
      body: payload,
      loading: { type: "local", name: "getCommissionProvisionalLoading" },
    }).then(({ data }) => {
      setDataCommission(data.data);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formSearch, username]);

  const dataTable: DataTable[][] = useMemo(
    () =>
      dataCommission?.lstProvisionalCom?.map((d) =>
        configCommission.map((config: TableConfig) => ({
          config: config,
          node: formatValueTable(d, config),
          originData: get(d, [config.key]),
        }))
      ) ?? [],
    [dataCommission?.lstProvisionalCom]
  );

  const disableSubmit = useMemo(() => {
    return Object.values(error).findIndex((value) => value) > -1;
  }, [error]);

  const extend = useMemo(() => {
    const total = dataCommission?.totalProvisionalCom;
    return (
      <TR>
        <THCustom colSpan={9}>TỔNG (VNĐ)</THCustom>
        <THCustom textAlign="right">{formatNumber(total?.premium)}</THCustom>
        <THCustom></THCustom>
        <THCustom textAlign="right">{formatNumber(total?.commission)}</THCustom>
        <THCustom textAlign="right">{formatNumber(total?.bonusBenefit)}</THCustom>
        <THCustom></THCustom>
      </TR>
    );
  }, [dataCommission]);

  const handleChangeData = (name: string, value: any) => {
    setFormSearch((pre) => ({
      ...pre,
      [name]: value,
    }));

    if (name === "fromDate" || name === "toDate") {
      const newError = handleCheckValidateMonthForm(name, value, formSearch.fromDate);
      setError((pre: any) => ({ ...pre, ...newError }));
    }
  };

  const onBlurData = (name: string, valueDateFrom: string) => {
    const newError = handleCheckValidateMonthForm(name, formSearch.toDate, valueDateFrom);
    setError((pre: any) => ({ ...pre, ...newError }));
  };

  return (
    <Block type="collapse" title="Hoa hồng tạm tính - Chi tiết">
      <BlockContentWrapper>
        <FormSearchTemplate
          title="Thông tin"
          onSubmit={handleSubmit}
          disableSubmit={disableSubmit || Boolean(getCommissionProvisionalLoading)}
        >
          <RowItem>
            <InputFieldSet
              name="fullnameLF"
              placeholder="Bên mua BH"
              value={formSearch.owName}
              onChange={(e) => handleChangeData("fullnameLF", e.target.value)}
            />
            <InputFieldSet
              name="policyNo"
              placeholder="Số hợp đồng"
              value={formSearch.policyNo}
              onChange={(e) => handleChangeData("policyNo", e.target.value)}
            />
          </RowItem>
          <RowItem>
            <DropDownFieldset
              placeholder="Tìm kiếm theo"
              value={formSearch.dateType}
              list={dateTypeList}
              onChange={(value) => handleChangeData("dateType", value)}
            />
            <DateInput
              placeholder="Từ ngày"
              value={formSearch.fromDate}
              error={error.fromDate}
              onBlur={(value) => onBlurData("toDate", value)}
              onChange={(value) => handleChangeData("fromDate", value)}
            />
            <DateInput
              placeholder="Đến ngày"
              value={formSearch.toDate}
              error={error.toDate}
              onChange={(value) => handleChangeData("toDate", value)}
            />
          </RowItem>
        </FormSearchTemplate>
      </BlockContentWrapper>
      <BlockContentWrapper>
        <Table
          data={dataTable}
          config={configCommission}
          showOrderNo={true}
          showConfig={false}
          showPagination={true}
          isStickyHeader
          extendHead={extend}
          loading={Boolean(getCommissionProvisionalLoading)}
        />
        <TableMobile data={dataTable} config={configCommission} />
      </BlockContentWrapper>
    </Block>
  );
};

export default Commission;
