import { DataTable, TableConfig } from "@custom-types/config-table";
import {
  DesignationLevelOutput,
  LineChartData,
  PerformanceFormSearchValue,
  SalesByBranchData,
} from "@custom-types/performance";
import Block from "components/block";
import { BlockContentWrapper } from "components/block/styled";
import CardPerformance from "components/card";
import LoadingSection from "components/loading";
import Table from "components/table";
import { useAppSelector } from "hooks/use-redux";
import { useMemo } from "react";
import { useDispatch } from "react-redux";
import { setPerformancePersonalForm } from "screens/performance/slice";
import FormSearchPerformance from "sections/team/content-right/overview/components/form-search";
import OnlineBanaProductContent, {
  DataBancaOnline,
} from "sections/team/content-right/overview/components/online-content-banca";
import { formatValueTable } from "services/format-value";
import { calculateTotal, formatNumber, lineChartByYearNumber } from "services/untils";
import LineChart from "../sales-personal/line-chart";
import { ExtendForBancaWrapper } from "./styled";

interface OverviewDetailProps {
  tab?: string;
  data: any;
  fyp: number;
  designationLevel?: DesignationLevelOutput;
  dataChartBanca?: LineChartData[];
  dataTableBanca?: SalesByBranchData[];
  dataOnline?: DataBancaOnline;
}

const OverviewDetail = ({ data, fyp, dataChartBanca, dataTableBanca, dataOnline }: OverviewDetailProps) => {
  const {
    user: { username, channel, designation },
    loading: { getPerformanceDetailLoading, getFYPbyMonthLoading, getSalesByBranchLoading },
  } = useAppSelector((state) => state.rootReducer);
  const {
    performancePersonalType,
    performancePersonalDate,
    performancePersonalErrorDate,
    performancePersonalQuarterly,
  } = useAppSelector((state) => state.performanceReducer.performancePersonalForm);

  const dispatch = useDispatch();

  const config: TableConfig[] = useMemo(() => {
    return [
      { key: "branchCode", label: "Mã PGD", type: "string", show: true },
      { key: "branchName", label: "Tên PGD", type: "string", show: true },
      { key: "ape", label: "APE", type: "number", show: true },
      { key: "caseCount", label: "Cases count", type: "number", show: true },
      { key: "caseSize", label: "Cases size", type: "number", show: true },
      { key: "leadCount", label: "Lead nhận được", type: "number", show: true },
      { key: "fyp", label: `FYP: ${formatNumber(calculateTotal(dataTableBanca, "fyp"))}`, type: "number", show: true },
    ];
  }, [dataTableBanca]);

  const dataTable: DataTable[][] = useMemo(
    () =>
      dataTableBanca?.map((d: any) =>
        config.map((config) => ({
          config: config,
          node: formatValueTable(d, config),
          originData: d[config.key],
        }))
      ),
    [config, dataTableBanca]
  );

  const handleChangeFormSearch = (value: PerformanceFormSearchValue) => {
    dispatch(
      setPerformancePersonalForm({
        performancePersonalType: value.type,
        performancePersonalDate: value.date,
        performancePersonalErrorDate: value.error,
        performancePersonalQuarterly: value.quarterly,
      })
    );
  };

  return (
    <Block type="collapse" title="TQKD - Chi tiết" defaultOpen>
      <BlockContentWrapper>
        <FormSearchPerformance
          fyp={fyp}
          error={performancePersonalErrorDate}
          tab={performancePersonalType}
          date={performancePersonalDate}
          quarterly={performancePersonalQuarterly}
          currentAgentCode={username}
          onChangeFormSearch={handleChangeFormSearch}
        >
          {!Boolean(getPerformanceDetailLoading || getFYPbyMonthLoading || getSalesByBranchLoading) ? (
            <>
              <CardPerformance data={data} />
              {channel === "BANCA" ? <OnlineBanaProductContent data={dataOnline} fyp={fyp} /> : null}
              {channel === "BANCA" ? (
                performancePersonalType !== "yearly" ? (
                  designation === "BSM" || designation === "SBSM" ? (
                    <ExtendForBancaWrapper>
                      <h6 className="h7">Doanh số tính theo CN/PGD</h6>
                      <Table config={config} data={dataTable} showConfig={false} />
                    </ExtendForBancaWrapper>
                  ) : null
                ) : (
                  <ExtendForBancaWrapper>
                    <h6 className="h7">Doanh số phát hành mỗi tháng trong năm (FYP)</h6>
                    <LineChart
                      data={dataChartBanca}
                      xAxisData={lineChartByYearNumber}
                      showLegend
                      showSymbol
                      showTooltip
                      rightOffset={20}
                    />
                  </ExtendForBancaWrapper>
                )
              ) : null}
            </>
          ) : (
            <LoadingSection loading />
          )}
        </FormSearchPerformance>
      </BlockContentWrapper>
    </Block>
  );
};

export default OverviewDetail;
