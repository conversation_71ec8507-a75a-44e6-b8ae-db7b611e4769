import { AchievementOutput, MDRTType } from "@custom-types/performance";
import CircularProgress from "components/circular-progress";
import Icons from "components/icons";
import ModalFullPage from "components/modal-full-page";
import { get } from "lodash";
import { useMemo } from "react";
import { formatNumber } from "services/untils";
import { FlexBox } from "styles";
import ProgressAchievement from "./components/progress-achievement";
import {
  Achievement,
  AchievementModalWrapper,
  Content,
  ContentLeft,
  ContentRight,
  KPIItem,
  KPIWrapper,
  TargetItem,
  TargetWrapper,
  Title,
} from "./styled";

export interface DataAchievement {
  value: number;
  title: string;
  percent?: number;
  imageUrl?: string;
}

export interface DataAchievementAcquired {
  imageUrl: string;
  nextLevel: string;
  compete: number;
  fypTarget?: number;
  fypAchieve: number;
  fypSupple: number;
}

interface AchievementModalProps {
  show: boolean;
  status?: MDRTType;
  data: AchievementOutput;
  onClose: () => void;
}

const AchievementModal = ({ show, status, data, onClose }: AchievementModalProps) => {
  const { mdrt, elite } = data ?? {};
  const currentLevel = status.toLowerCase();

  const title = useMemo(() => {
    if (get(mdrt, [currentLevel]) === 1) {
      return currentLevel === "mdrt" ? "COT" : "TOT";
    } else {
      if (currentLevel === "tot") {
        return "TOT";
      } else {
        return mdrt?.shortfallToNextTier ?? "";
      }
    }
  }, [mdrt, currentLevel]);

  const dataAchievementAcquired: DataAchievementAcquired = useMemo(() => {
    return status === "Elite"
      ? {
          imageUrl: `${process.env.basePath}/img/achievement/elite.jpg`,
          nextLevel: "ELITE",
          compete: Math.round(elite?.elitePercent * 100),
          fypSupple: elite?.eliteMissingPart,
          fypAchieve: elite?.fyp,
        }
      : {
          imageUrl: `${process.env.basePath}/img/achievement/${title.toLowerCase()}.jpg`,
          nextLevel: title,
          compete: Math.round(get(mdrt, [currentLevel]) * 100),
          fypTarget: get(mdrt, `${currentLevel}level`),
          fypAchieve: mdrt?.fyp,
          fypSupple: get(mdrt, `shortfallTo${currentLevel.toUpperCase()}`),
        };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, status, currentLevel, title]);

  const dataKpi: DataAchievement[] = useMemo(() => {
    return [
      {
        value: mdrt?.fyp,
        title: "FYP đã đạt được",
        percent: Math.round(get(mdrt, [currentLevel]) * 100),
      },
      {
        value: get(mdrt, `${currentLevel}level`),
        title: "FYP chỉ tiêu",
        percent: 100,
      },
      {
        value: get(mdrt, `shortfallTo${currentLevel.toUpperCase()}`),
        title: "FYP cần bổ sung",
        percent: Math.round(100 - get(mdrt, [currentLevel]) * 100),
      },
    ];
  }, [mdrt, currentLevel]);

  const dataTarget: DataAchievement[] = useMemo(() => {
    return status !== "Elite"
      ? [
          { value: mdrt?.totlevel, title: "TOT" },
          { value: mdrt?.cotlevel, title: "COT" },
          { value: mdrt?.mdrtlevel, title: "MDRT" },
        ]
      : [{ value: elite?.eliteFypTarget, title: "Elite" }];
  }, [mdrt, elite, status]);

  if (!show) {
    return null;
  }

  return (
    <ModalFullPage show={show} title="Thành tích" onClose={onClose}>
      <AchievementModalWrapper>
        <Achievement>
          <ContentLeft>
            <Content percent={dataAchievementAcquired.compete}>
              <FlexBox justifyContent="space-between">
                <FlexBox>
                  <img src={dataAchievementAcquired.imageUrl} alt="" />
                  <div>
                    <p className="body-2">
                      {currentLevel === "tot"
                        ? "Chúc mừng TVTC đã đạt được thành tích cao nhất"
                        : "Cấp kế tiếp của TVTC là:"}
                    </p>
                    <h4>{dataAchievementAcquired.nextLevel}</h4>
                  </div>
                </FlexBox>
                {dataAchievementAcquired.compete === 100 ? (
                  <Icons icon="two-people-flag-success" />
                ) : (
                  <Icons icon="two-people-flag-compete" />
                )}
              </FlexBox>
              <Title>Thành tựu</Title>
              <ProgressAchievement status={status} data={dataAchievementAcquired} />
            </Content>
          </ContentLeft>
          <ContentRight>
            {status !== "Elite" ? (
              <KPIWrapper>
                <h4 className="h7">KPI đạt được</h4>
                {dataKpi.map((data, key) => (
                  <KPIItem key={key}>
                    <FlexBox alignItem="center">
                      <CircularProgress value={data.percent} />
                      <p className="body-5">{data.title}</p>
                    </FlexBox>
                    <h5 className="h7">{formatNumber(data.value)}</h5>
                  </KPIItem>
                ))}
              </KPIWrapper>
            ) : null}
            <TargetWrapper>
              {status !== "Elite" ? (
                <h5 className="h7">Chỉ tiêu dành cho MDRT</h5>
              ) : (
                <h5 className="h7">Chỉ tiêu ELITE</h5>
              )}
              {dataTarget.map((data, key) => (
                <TargetItem key={key}>
                  <img src={`${process.env.basePath}/img/achievement/${data.title.toLowerCase()}.jpg`} alt="" />
                  <div>
                    {status !== "Elite" ? <p className="body-5">{data.title}</p> : null}
                    <h6 className="h7">
                      FYP <span>{formatNumber(data.value)}</span>
                    </h6>
                  </div>
                </TargetItem>
              ))}
            </TargetWrapper>
          </ContentRight>
        </Achievement>
      </AchievementModalWrapper>
    </ModalFullPage>
  );
};

export default AchievementModal;
