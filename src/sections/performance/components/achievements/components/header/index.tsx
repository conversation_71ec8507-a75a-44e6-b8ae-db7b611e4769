import Icons from "components/icons";

import React from "react";
import { ButtonIcon } from "styles/buttons";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, LeftHeader, RightHeader } from "./styled";

interface HeaderProps {
  onClose: () => void;
}

const Header = ({ onClose }: HeaderProps) => {
  return (
    <HeaderWrapper>
      <LeftHeader>
        <ButtonIcon onClick={onClose}>
          <Icons icon="arrow-left-in-header" />
        </ButtonIcon>
        <h6>Thành tích</h6>
      </LeftHeader>
      <RightHeader>
        <Icons icon="menu-home" fill="black" />
        <h6 className="hide-mobile">Home</h6>
      </RightHeader>
    </HeaderWrapper>
  );
};

export default Header;
