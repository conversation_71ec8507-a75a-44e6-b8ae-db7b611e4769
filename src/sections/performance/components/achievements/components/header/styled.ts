import styled from "styled-components";

export const HeaderWrapper = styled.div`
  width: 100%;
  padding: 15px 18px;

  position: absolute;
  top: 0;
  left: 0;
  z-index: 1000;

  display: flex;
  justify-content: space-between;
  align-items: center;

  background: #ffffff;
  border-bottom: 1px solid ${({ theme }) => theme.color.status.grey};
`;

export const LeftHeader = styled.div`
  display: flex;

  h6 {
    margin-left: 22px;
  }
`;

export const RightHeader = styled.div`
  display: flex;

  h6 {
    margin-left: 5px;
  }
`;
