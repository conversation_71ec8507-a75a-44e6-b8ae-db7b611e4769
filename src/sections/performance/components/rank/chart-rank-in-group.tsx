import Icons from "components/icons";
import { EChartsOption, GaugeSeriesOption } from "echarts";
import dynamic from "next/dynamic";
const EChartsReact = dynamic(() => import("echarts-for-react"), { ssr: false });
import useWindowResize from "hooks/use-window-resize";
import { useMemo } from "react";
import { formatNumber, formatNumberToMoneyString } from "services/untils";
import { useTheme } from "styled-components";
import { FlexBox } from "styles";
import { ChartGroup } from ".";
import { ICON_CROWN } from "./constants";
import { ChartDetail, ChartWrapper, Detail, Legend } from "./styled";

interface ChartRankInGroup {
  data: ChartGroup;
}

const ChartRankInGroup = ({ data }: ChartRankInGroup) => {
  const theme = useTheme();
  const size = useWindowResize();

  const font = (size: number, weight: number) => {
    return {
      fontSize: size,
      fontWeight: weight,
      fontFamily: "FWD",
      color: theme.color.text.body,
    };
  };

  const dataMeConfig: GaugeSeriesOption["data"] = useMemo(() => {
    return [
      {
        value: data.value,
        name: `${formatNumber(data.currentIdx)}`,
        title: {
          ...font(size.height > 768 ? 31 : 25, 700),
          color: theme.color.status.primary,
          offsetCenter: ["-23%", "-25%"],
        },
        detail: {
          ...font(12, 500),
          offsetCenter: ["33%", "75%"],
          formatter: "Top FYP",
        },
      },
      {
        name: "Xếp hạng trong nhóm",
        title: {
          ...font(size.height > 768 ? 14 : 12, 500),
          offsetCenter: ["0%", "0%"],
        },
        detail: {
          ...font(size.height > 768 ? 12 : 10, 400),
          offsetCenter: ["0%", "20%"],
          formatter: data.date ?? " ",
        },
        pointer: { length: 0, width: 0 },
      },
    ];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, size.height]);

  const dataTopFypConfig: GaugeSeriesOption["data"] = useMemo(() => {
    return [
      {
        value: 100,
        name: `/${formatNumber(data.max)}`,
        title: {
          ...font(size.height > 768 ? 16 : 14, 400),
          offsetCenter: ["20%", "-20%"],
        },
        detail: {
          ...font(12, 500),
          offsetCenter: ["-30%", "75%"],
          formatter: `${formatNumberToMoneyString(data.mineFyp, "vi").result} FYP`,
        },
      },
    ];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, size.height]);

  const option: EChartsOption = {
    series: [
      {
        center: ["50%", "50%"],
        type: "gauge",
        progress: {
          show: true,
          overlap: false,
          roundCap: false,
          clip: false,
        },
        color: ["transparent", "transparent"],
        axisLine: {
          lineStyle: {
            width: 10,
            color: [[1, theme.color.status.grey_50]],
          },
        },
        //Crown Pointer
        pointer: {
          icon: ICON_CROWN,
          length: 40,
          width: 40,
          offsetCenter: [0, "-80%"],
          itemStyle: {
            color: "auto",
          },
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        title: {
          fontSize: 14,
        },
        detail: {
          show: true,
          width: "100%",
          ...font(12, 400),
          color: theme.color.status.grey_darkest,
        },
        data: dataTopFypConfig,
      },
      {
        center: ["50%", "50%"],
        type: "gauge",
        progress: {
          show: true,
          overlap: false,
          roundCap: false,
          clip: false,
        },
        color: ["transparent"],
        axisLine: {
          show: false,
        },
        //Me Pointer
        pointer: {
          icon: data?.icon,
          length: 40,
          width: 40,
          offsetCenter: [0, "-80%"],
          itemStyle: {
            color: "auto",
          },
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        title: {
          fontSize: 14,
        },
        detail: {
          show: true,
          width: "100%",
          ...font(12, 400),
          color: theme.color.status.grey_darkest,
        },
        data: dataMeConfig,
      },
    ],
  };
  return (
    <ChartWrapper>
      <EChartsReact option={option} notMerge={true} lazyUpdate={true} />
      <ChartDetail>
        <FlexBox alignItem="center">
          <Detail>
            <Icons icon="me-icon" />
            <Legend className="body-4" color="#806a5b">
              FYP của tôi
            </Legend>
          </Detail>
          <h6>{formatNumber(data.mineFyp)}</h6>
        </FlexBox>
        <FlexBox alignItem="center">
          <Detail>
            <Icons icon="crown-chart-icon" />
            <Legend className="body-4" color="#F3BB90">
              FYP đứng đầu
            </Legend>
          </Detail>
          <h6>
            <span>{formatNumber(data.topFyp)}</span>
          </h6>
        </FlexBox>
      </ChartDetail>
    </ChartWrapper>
  );
};

export default ChartRankInGroup;
