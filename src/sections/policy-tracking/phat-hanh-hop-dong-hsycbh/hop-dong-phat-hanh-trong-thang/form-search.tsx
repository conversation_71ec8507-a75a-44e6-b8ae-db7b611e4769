import { FormSearchProps, PolicyReleasedInMonthInput } from "@custom-types/policy-tracking";
import MonthInput from "components/input-month";
import FormSearchTemplate from "components/form-search-template";
import { RowItem } from "styles";
import { useAppSelector } from "hooks/use-redux";

const FormSearchReleaseInMonth = ({
  showHierarchy,
  disableSubmit,
  error,
  formSearch,
  onChangeData,
  onSubmit,
}: FormSearchProps<PolicyReleasedInMonthInput>) => {
  const { getPolicyTrackingLoading } = useAppSelector((state) => state.rootReducer.loading);

  return (
    <FormSearchTemplate
      showHierarchy={showHierarchy}
      title="Thông tin"
      onSubmit={onSubmit}
      disableSubmit={disableSubmit}
      loading={Boolean(getPolicyTrackingLoading)}
    >
      <RowItem>
        <MonthInput
          placeholder="Tháng"
          value={formSearch.month}
          error={error.month}
          onChange={(value) => onChangeData("month", value)}
        />
      </RowItem>
    </FormSearchTemplate>
  );
};

export default FormSearchReleaseInMonth;
