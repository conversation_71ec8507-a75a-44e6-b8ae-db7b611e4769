import { FormSearchProps, PolicyInput } from "@custom-types/policy-tracking";
import DropDownFieldset from "components/drop-down-fieldset";
import DateInput from "components/input-date";
import FormSearchTemplate from "components/form-search-template";
import { RowItem } from "styles";
import { dateTypePendingList } from "screens/policy-tracking/constants";
import { useAppSelector } from "hooks/use-redux";

const FormSearchPending = ({
  showHierarchy,
  disableSubmit,
  error,
  formSearch,
  onBlurData,
  onChangeData,
  onSubmit,
}: FormSearchProps<PolicyInput>) => {
  const { getPolicyTrackingLoading } = useAppSelector((state) => state.rootReducer.loading);

  return (
    <FormSearchTemplate
      showHierarchy={showHierarchy}
      title="Thông tin"
      disableSubmit={disableSubmit}
      onSubmit={onSubmit}
      loading={Boolean(getPolicyTrackingLoading)}
    >
      <RowItem>
        <DropDownFieldset
          list={dateTypePendingList}
          placeholder="Tì<PERSON> kiếm theo"
          value={formSearch.dateType}
          onChange={(value) => onChangeData("dateType", value as any)}
        />
        <DateInput
          error={error.fromDate}
          placeholder="Từ ngày"
          value={formSearch.fromDate}
          enableCurrentMonth
          onBlur={(value) => onBlurData("toDate", value)}
          onChange={(value) => onChangeData("fromDate", value)}
        />
        <DateInput
          error={error.toDate}
          placeholder="Đến ngày"
          value={formSearch.toDate}
          enableCurrentMonth
          onChange={(value) => onChangeData("toDate", value)}
        />
      </RowItem>
    </FormSearchTemplate>
  );
};

export default FormSearchPending;
