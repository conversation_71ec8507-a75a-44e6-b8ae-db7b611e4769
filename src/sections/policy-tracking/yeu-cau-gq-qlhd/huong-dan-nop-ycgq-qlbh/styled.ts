import styled from "styled-components";
import { device } from "styles/media";

export const SubmitGuideWrapper = styled.div`
  width: 100%;
  padding: 12px 24px;

  border-radius: 24px;
  background: #ffffff;

  @media ${device.mobile} {
    padding: 16px;

    grid-template-columns: repeat(1, 1fr);
    gap: 16px;
    border-radius: 12px;
  }
`;

export const SubmitGuildList = styled.div`
  margin-left: -12px;
  margin-right: -12px;

  display: flex;
  flex-wrap: wrap;

  @media ${device.mobile} {
    margin: 0px;
  }
`;

export const SubmitGuideItem = styled.div`
  width: 250px;
  padding: 16px 12px;
  margin: 12px;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  position: relative;
  background: #ffffff;

  h6 {
    text-align: center;
    margin-top: 10px;
  }

  :after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;

    border: 2px solid ${({ theme }) => theme.color.status.grey};
    border-radius: 12px;

    transition: all 0.3s ease-in-out;
    pointer-events: none;
    user-select: none;
  }

  :hover {
    :after {
      border-color: ${({ theme }) => theme.color.status.primary};
    }
  }

  @media ${device.mobile} {
    width: 100%;
    margin: 12px 0px;
  }
`;

export const IconWrapper = styled.div`
  width: 100%;
  position: relative;

  display: flex;
  justify-content: center;

  a {
    position: absolute;
    top: 50%;
    right: 0px;

    transform: translateY(-50%);
  }
`;

export const ImageWrapper = styled.div``;
