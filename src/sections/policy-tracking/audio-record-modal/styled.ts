import { BlockWrapper } from "components/block/styled";
import { ModalFooter } from "components/modal/styled";
import styled from "styled-components";
import { ButtonStyle } from "styles/buttons";
import { device } from "styles/media";

export const AudioRecordModalWrapper = styled.div`
  .${BlockWrapper.styledComponentId} {
    margin-top: 0px;

    border: 1px solid ${({ theme }) => theme.color.status.grey};

    h6 {
      color: ${({ theme }) => theme.color.status.primary};
    }
  }

  @media ${device.mobile} {
    padding: 0px 16px;

    .${ModalFooter.styledComponentId} {
      @media ${device.mobile} {
        button {
          width: 100%;
          max-width: 100%;
        }
      }
    }
  }
`;

export const UploadedWrapper = styled.div`
  margin-bottom: 32px;

  @media ${device.mobile} {
    margin-bottom: 24px;
  }
`;

export const DocumentUploadedWrapper = styled.div`
  width: 100%;
  margin-top: 18px;

  display: flex;
  flex-wrap: wrap;

  p {
    width: 50%;
    padding-left: 18px;

    position: relative;

    color: ${({ theme }) => theme.color.status.grey_darkest};

    :after {
      content: "";
      width: 7px;
      height: 7px;

      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);

      background: ${({ theme }) => theme.color.status.grey_darkest};
      border-radius: 50%;
    }
  }

  @media ${device.mobile} {
    p {
      width: 100%;
    }
  }
`;

export const UploadWrapper = styled.div`
  h6 {
    margin-bottom: 10px;
    color: ${({ theme }) => theme.color.status.primary};
  }

  ul {
    margin-bottom: 20px;
    color: ${({ theme }) => theme.color.status.grey_darkest};

    p,
    li {
      font-weight: 500;
      font-size: 14px;
      line-height: 125%;
    }

    p {
      margin-left: -16px;
      margin-bottom: 4px;
    }
  }

  @media ${device.mobile} {
    > p {
      margin-bottom: 10px;
    }
  }
`;

export const UploadHead = styled.div`
  width: 100%;
  padding: 20px;
  padding-bottom: 10px;

  position: relative;
  z-index: 1;

  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.1);
`;

export const ButtonUploadMobile = styled.div`
  ${ButtonStyle}
  padding:0px 8px;

  color: ${({ theme }) => theme.color.status.primary};
  background: #ffffff;
`;
