import { WARNING } from "@constants/message";
import { File } from "@custom-types";
import { UploadPendingAudioInput } from "@custom-types/policy-tracking";
import { uploadPolicyPendingAudio } from "api/policy-tracking";
import { Alert } from "components/alert";
import Modal from "components/modal";
import { ModalFooter } from "components/modal/styled";
import UploadBlock from "components/upload-block";
import UploadProgress from "components/upload-progress";
import useActionApi from "hooks/use-action-api";
import { useAppSelector } from "hooks/use-redux";
import { useCallback, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import { setAudioRecord } from "screens/policy-tracking/slice";
import { sizeFile } from "services/untils";
import { ButtonPrimary } from "styles/buttons";
import { AudioRecordModalWrapper, UploadWrapper } from "./styled";

const AudioRecordModal = () => {
  const [fileList, setFileList] = useState<File[]>([]);
  const [error, setError] = useState("");
  const [showUploadProgress, setShowUploadProgress] = useState(false);
  const [uploadProgressStatus, setUploadProgressStatus] = useState({});

  const { username } = useAppSelector((state) => state.rootReducer.user);
  const { audioRecord } = useAppSelector((state) => state.policyTrackingReducer);
  const { data: dataDetail } = audioRecord;

  const dispatch = useDispatch();

  const actionUploadPolicyPendingAudio = useActionApi<UploadPendingAudioInput, number[]>(uploadPolicyPendingAudio);

  const disabled = useMemo(() => {
    return !fileList.length || fileList.findIndex((item) => item.status === "FAIL") > -1;
  }, [fileList]);

  //clear all state
  const handleClose = () => {
    setFileList([]);
    setError("");
    setUploadProgressStatus([]);
    setShowUploadProgress(false);
    dispatch(
      setAudioRecord({
        show: false,
        data: null,
      })
    );
  };

  const handleChangeFile = (document: File[]) => {
    setFileList(document);
  };

  const changeUploadProgressStatus = (key: any, value: "SUCCESS" | "FAIL" | "LOADING") => {
    setUploadProgressStatus((pre) => ({
      ...pre,
      [key]: value,
    }));
  };

  const handleSubmit = useCallback(
    (documentList: File[]) => {
      const totalSize = documentList.map((item) => item.size);

      if (sizeFile(totalSize) > 25) {
        Alert(WARNING, "Tổng dung lượng đã vượt quá 25MB");
        return;
      }

      setShowUploadProgress(true);
      dispatch(
        setAudioRecord({
          show: false,
          data: dataDetail, // only close modal, no clear data
        })
      );

      for (let i = 0; i < documentList.length; i++) {
        const file = documentList[i];

        const payload: UploadPendingAudioInput = {
          agentId: username,
          audioRecordFile: {
            category: "NBU-NAUR-01",
            codeLas: "N90",
            documentCd: "NBU-NAUR-01",
            documentClass: "NBUGroup2",
            documentId: 0,
            fileContent: file.fileContent,
            fileId: 0,
            fileName: file.fileName,
            fileType: file.mime,
            longDesc: "Audio",
            subCategory: "Audio Record",
          },
          policyNo: dataDetail.policyNo,
        };

        changeUploadProgressStatus(file.id, "LOADING");
        actionUploadPolicyPendingAudio({
          body: payload,
          loading: {
            type: "local",
            name: "uploadProgressLoading",
          },
        })
          .then(({ data }) => {
            if (data.length) {
              changeUploadProgressStatus(file.id, "SUCCESS");
            } else {
              changeUploadProgressStatus(file.id, "FAIL");
            }
          })
          .catch((error) => {
            console.log("error", error);
            changeUploadProgressStatus(file.id, "FAIL");
          });
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dataDetail, username]
  );

  return (
    <>
      <Modal
        show={audioRecord.show}
        title="Thông tin yêu cầu bổ sung"
        size="lg"
        backgroundMobile="#ffffff"
        borderHeader
        onClose={handleClose}
        isFooterSticky
      >
        <AudioRecordModalWrapper>
          <UploadWrapper>
            <h6 className="h7">TVTC vui lòng bổ sung bản ghi âm</h6>
            <ul>
              <p>Lưu ý:</p>
              <li>Chứng từ tải lên dùng định dạng âm thanh (M4A, MP3, WAV )</li>
              <li>
                Chứng từ tải lên có dung lượng tối đa 5MB/file và tổng dung lượng được phép tải lên {"< 25MB"}. (*) Tài
                liệu bắt buộc
              </li>
            </ul>
            <UploadBlock
              title="Bản ghi âm"
              require
              error={error}
              value={fileList}
              maxSize={5}
              acceptAll="audio/*"
              onChange={handleChangeFile}
            />
          </UploadWrapper>
          <ModalFooter>
            <ButtonPrimary long onClick={() => handleSubmit(fileList)} disabled={disabled}>
              Chấp nhận
            </ButtonPrimary>
          </ModalFooter>
        </AudioRecordModalWrapper>
      </Modal>
      <UploadProgress
        show={showUploadProgress}
        title="Gửi yêu cầu"
        data={fileList}
        dataStatus={uploadProgressStatus}
        onReSubmit={handleSubmit}
        onClose={handleClose}
      />
    </>
  );
};

export default AudioRecordModal;
