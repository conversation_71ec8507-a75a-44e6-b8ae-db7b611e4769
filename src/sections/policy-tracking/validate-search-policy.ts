import moment from "moment";
import { validatePhone } from "services/validate";

const messageErrorDay = "Vui lòng nhập đúng định dạng: dd/mm/yyyy";
const messageErrorSearch = "<PERSON>ui lòng nhập thời gian tìm kiếm không quá 3 tháng";
const messageCompare = "Vui lòng chọn đến ngày không được bé hơn từ ngày";
// const messageFuture = "Vui lòng không chọn ngày tháng trong tương lai";

const fieldNameDate = ["dateFrom", "dateTo", "from", "to", "tranDateFrom", "tranDateTo", "fromDate", "toDate"];

//Only for POLICY-TRACKING - Pls don't use it for another purpose
export const handleValidatePolicyForm = (name: string, value: string, from: string) => {
  const newError: any = {};

  if (name === "phoneNumber") {
    if (value.length) {
      if (!validatePhone(value)) {
        newError[name] = "Vui lòng nhập đúng định dạng";
      } else {
        newError[name] = null;
      }
    } else {
      newError[name] = null;
    }
  }

  if (fieldNameDate.includes(name)) {
    if (value.length < 10) {
      newError[name] = messageErrorDay;
    } else {
      if (name.toLowerCase().includes("from")) {
        /** hide this rule from Phuc's request */
        // if (moment(value, "DD/MM/YYYY").isAfter(moment().startOf("d"))) {
        //   newError[name] = messageFuture;
        // } else {
        //   newError[name] = null;
        // }
        newError[name] = null;
      } else if (name.toLowerCase().includes("to")) {
        const start = moment(from, "DD/MM/YYYY", true).subtract(3, "month").subtract(1, "day");
        const end = moment(from, "DD/MM/YYYY", true).add(3, "month").add(1, "day");
        const to = moment(value, "DD/MM/YYYY", true);

        if (to.isBetween(start, end)) {
          newError[name] = null;
        } else {
          newError[name] = messageErrorSearch;
        }

        if (to.isBefore(moment(from, "DD/MM/YYYY"))) {
          newError[name] = messageCompare;
        }
      }
    }
  }
  return newError;
};
