import { DataTableProps } from "@custom-types/policy-tracking";
import TableMobile from "components/table-mobile";
import Table from "components/table";
import { PolicyTableWrapper } from "screens/policy-tracking/styled";
import { useAppSelector } from "hooks/use-redux";

const PolicyTable = ({ config, data }: DataTableProps) => {
  const { getPolicyTrackingLoading } = useAppSelector((state) => state.rootReducer.loading);

  return (
    <PolicyTableWrapper>
      <Table config={config} data={data} loading={getPolicyTrackingLoading} />
      <TableMobile config={config} data={data} />
    </PolicyTableWrapper>
  );
};

export default PolicyTable;
