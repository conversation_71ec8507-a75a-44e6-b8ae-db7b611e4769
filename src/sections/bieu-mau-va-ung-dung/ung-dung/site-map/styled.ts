import styled, { css } from "styled-components";
import { device } from "styles/media";

export const LineCo = styled.div`
  ::before {
    content: "";
    position: absolute;
    top: 86px;
    left: 50.16%;

    border: 1px solid #e87722;
    border-top: 1px solid transparent;
    border-right: 1px solid transparent;

    width: 24px;
    height: 36px;

    overflow: hidden;
    z-index: 999;
    border-radius: 0 0px 0 18px;
  }

  ::after {
    content: "";
    position: absolute;
    top: 86px;
    right: 49.84%;

    margin-left: 1px;
    margin-right: -1px;
    border-radius: 0 0 18px 0;

    border: 1px solid #e87722;
    border-top: 1px solid transparent;
    border-left: 1px solid transparent;

    width: 24px;
    height: 36px;
    z-index: 999;
  }
`;

export const DotLine = styled.div<{ lvl: number }>`
  content: "";
  position: absolute;

  width: 6px;
  height: 6px;

  border-radius: 50%;
  background-color: #e87722;

  ${({ lvl }) =>
    lvl === 0
      ? css`
          left: 50.095%;
          top: 84px;
          /* transform: translateX(65%); */
          z-index: 9999;
        `
      : css`
          left: 50%;
          top: 62px;
          transform: translateX(-45.5%);
        `}
`;

export const DotLine2 = styled.div`
  position: absolute;
  top: 25px;
  transform: translateY(-100%);
  left: -42px;

  width: 38px;
  height: 120px;

  border: 1px solid #e87722;
  border-top: 0px;
  border-right: 0px;
  border-bottom-left-radius: 8px;

  &:first-child {
    height: 50px;
  }

  &::after {
    content: "";
    width: 6px;
    height: 6px;
    position: absolute;
    top: 49.9px;
    right: -1px;
    transform: translateY(-50%);

    background-color: #e87722;
    border-radius: 50%;
  }
`;

export const Container = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 780px;

  margin: auto;
  margin-top: 24px;

  @media ${device.mobile} {
    height: 580px;
  }

  @media screen and (max-width: 480px) {
    height: 520px;
  }
`;

export const TreeWrapper = styled.div`
  text-align: center;
  width: 100%;
  height: auto;
  overflow: hidden;
`;

export const TreeList = styled.ul<{ lvl: number }>`
  padding-top: 41px;
  position: relative;
  transition: 0.5s;
  width: 100%;
  display: flex;
  padding-right: 80px;
  ${({ lvl }) =>
    lvl === 2
      ? css`
          flex-direction: column;
          justify-content: flex-start;

          padding-top: 40px;
          margin-left: -120px;
        `
      : lvl == 1
      ? css`
          flex-direction: column;
          justify-content: flex-end;

          padding-right: 0px;
          width: 250px;
          margin-left: auto;
          padding-top: 0px;
        `
      : css`
          ::before {
            content: "";
            position: absolute;
            top: 7px;
            left: 50.13%;
            width: 23px;
            height: 36px;
            z-index: 99;
            background-color: #fff;
          }

          ::after {
            content: "";
            position: absolute;
            top: 7px;
            right: 49.87%;
            width: 15.4px;
            height: 36px;
            z-index: 99;
            background-color: #fff;
          }
        `}
`;

export const TreeLink = styled.div<{ lvl: number }>`
  display: inline-grid;
  align-items: center;
  justify-content: center;

  text-decoration: none;
  transition: 0.5s;
  max-width: ${({ lvl }) => (lvl === 0 ? "240px " : lvl === 1 ? "180px" : "")};
  min-width: ${({ lvl }) => (lvl === 0 ? "240px " : lvl === 1 ? "180px" : "")};

  position: relative;

  font-weight: 500;
  border-radius: 16px;
  padding: 12px;
  line-height: 18px;
  /* &:hover {
    background: #c8e4f8;
    border: 1px solid #94a0b4;
  }
  cursor: pointer;

  &:hover + ul li::after,
  &:hover + ul li::before,
  &:hover + ul::before,
  &:hover + ul ul::before {
    border-color: #94a0b4;
    transition: 0.5s;
  } */

  ${({ lvl }) =>
    lvl === 0 || lvl === 1
      ? css`
          background-color: #e87722;
          color: #fff;
          text-transform: uppercase;
          padding: 2px;
          height: ${lvl === 0 ? "80px " : "60px"};
          span {
            font-size: ${lvl === 0 ? "20px " : "16px"};
          }
          line-height: 22px;
        `
      : lvl === 2
      ? css`
          border: 2px solid #e87722;
          color: #e87722;
          height: 50px;
          padding: 0px;
          max-width: 160px;
          min-width: 160px;
          font-weight: 400;
          white-space: normal;
          span {
            font-size: 14px;
          }
        `
      : lvl === 3
      ? css`
          border: 2px solid #03824f;
          color: #03824f;
          padding: 2px;
          min-height: 50px;
          font-weight: 400;
          max-width: 160px;
          min-width: 160px;
          white-space: normal;
          span {
            font-size: 14px;
          }
        `
      : css`
          background-color: #e87722;
        `}
`;

const VerticalLine = css<{ total: number }>`
  &::before,
  &::after {
    content: "";
    position: absolute;
    top: 0;
    border-top: 1px solid #e87722;
    width: 100%;
    height: 68px;
  }

  &::after {
    right: auto;
    left: 50%;
    border-left: 1px solid #e87722;
    border-radius: 24px 0 0 0;
    width: 51%;
  }

  &:nth-child(n + ${({ total }) => total})::after {
    left: auto;
    right: 49.86%;
    border-right: 1px solid #e87722;
    border-radius: 0 24px 0 0;
    width: 51%;
    border-left: 0px;
  }

  &:nth-child(${({ total }) => total - 1})::after {
    right: auto;
    left: 50%;
    border-left: 1px solid #e87722;
    border-radius: 22px 0 0 0;
    width: 51%;
  }

  &:first-child::after {
    width: 100%;
  }

  &:only-child {
    padding-top: 0;

    &::after {
      display: block;
      top: -10px;
      height: 20px;
    }

    &::before {
      display: none;
    }
  }

  &:first-child::before,
  &:last-child::after {
    border: 0 none;
    background: unset;
    display: none;
  }

  &:last-child::before {
    border-right: 1px solid #e87722;
    border-radius: 0 24px 0 0;
    width: 22.6%;
  }

  &:last-child::after {
    width: 70%;
  }

  &:first-child::after {
    border-radius: 24px 0 0 0;
  }
`;

const HorizontalLine = css`
  &::after {
    content: "";
    position: absolute;
    top: 4px;
    left: -40px;
    border-left: 1px solid #e87722;
  }

  &::after {
    width: 36px;
    height: 100%;
  }

  &:last-child::after {
    height: 4%;
  }

  &:first-child::before {
    content: "";
    position: absolute;
    top: 4px;
    left: -42.5px;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #e87722;
  }
`;

export const TreeNodeItem = styled.li<{ lvl: number; total: number }>`
  display: inline-table;
  text-align: center;
  list-style-type: none;
  white-space: nowrap;
  table-layout: fixed;

  position: relative;
  transition: 0.5s;

  min-width: 400px;
  padding-top: 70px;
  height: fit-content;
  ${({ lvl }) =>
    lvl === 2
      ? css`
          display: flex;
          gap: 40px;
          min-width: fit-content;
          padding-top: 30px;
        `
      : lvl === 3
      ? css`
          display: flex;
          padding: 20px 0px 0px 0px;
          min-width: fit-content;

          &:first-child {
            padding-top: 30px;
            margin-top: 15px;
          }
        `
      : null}

  ${({ lvl }) => (lvl === 0 || lvl === 1 ? VerticalLine : HorizontalLine)}
`;

export const TreeNodeSpan = styled.span`
  font-size: 12px;
  user-select: none;
  word-break: break-word;
  width: 100%;
  white-space: normal;
`;

export const ListTreeWrapper = styled.div`
  position: relative;
`;
