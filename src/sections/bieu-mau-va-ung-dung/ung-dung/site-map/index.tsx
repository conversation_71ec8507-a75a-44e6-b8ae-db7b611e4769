import ZoomAndDrag from "components/zoom-drag";
import { useAppSelector } from "hooks/use-redux";
import { useMemo } from "react";
import TreeNode from "./node";
import { Container, TreeWrapper } from "./styled";

const SiteMap = () => {
  const {
    user: { channel },
    isLeader,
  } = useAppSelector((state) => state.rootReducer);

  const data = useMemo(() => {
    const isAgency = channel === "AGENCY";
    const isIFA = channel === "BROKER";
    const isBANCA = channel === "BANCA";
    const isLeaderYes = isLeader === "Y";
    const isDO = channel === "DO";
    const isAgencyorBanca = ["AGENCY", "BANCA"].includes(channel);

    return {
      show: true,
      name: "<PERSON><PERSON><PERSON> nhậ<PERSON>",
      children: [
        {
          show: true,
          name: "Trang chủ",
          key: "home-page",
          children: [
            {
              show: true,
              name: "<PERSON><PERSON>ng cụ hỗ trợ tư vấn",
              children: [
                { show: true, name: "<PERSON><PERSON><PERSON><PERSON> dụng" },
                { show: true, name: "<PERSON><PERSON> mới" },
                { show: true, name: "<PERSON><PERSON> thiếp điện tử" },
                { show: true, name: "<PERSON><PERSON>ng ký giọng nói mẫu" },
              ],
            },
            {
              show: true,
              name: "Hiển thị banner",
              children: [
                { show: true, name: "Thông b<PERSON>o" },
                { show: isAgency, name: "Chương tr<PERSON>nh thi đua" },
              ],
            },
            {
              show: !isLeaderYes,
              name: "Doanh số phát hành thuần",
              children: [
                { show: true, name: "MTD" },
                { show: true, name: "YTD" },
              ],
            },
            {
              show: !isLeaderYes,
              name: "Phát hành hợp đồng (HSYCBH)",
              children: [
                { show: true, name: "HĐ phát hành trong tháng" },
                { show: true, name: "Đang thẩm định" },
                { show: true, name: "Đang chờ bổ sung thông tin" },
              ],
            },
            {
              show: isLeaderYes,
              name: "Doanh số phát hành thuần - Nhóm",
              children: [
                { show: true, name: "MTD" },
                { show: true, name: "YTD" },
              ],
            },
            {
              show: isLeaderYes,
              name: "Phát hành hợp đồng (HSYCBH) - Nhóm",
              children: [
                { show: true, name: "HĐ phát hành trong tháng" },
                { show: true, name: "Đang thẩm định" },
                { show: true, name: "Đang chờ bổ sung thông tin" },
              ],
            },
            {
              show: true,
              name: "Hợp đồng thuộc chương trình (Campaign)",
              children: [
                { show: true, name: "Khôi phục hiệu lực HĐ" },
                { show: true, name: "Nhắc phí HĐ sắp mất hiệu lực 7 ngày tới" },
                { show: true, name: "Nhắc phí chủ động" },
                { show: isBANCA, name: "VCB - Nhắc phí K2 hợp đồng đến hạn tháng 6-10/2023" },
                { show: isBANCA, name: "VCB K2 – Lucky Draw Campaign" },
                { show: isBANCA, name: "CSHĐ Sắp đến hạn thu phí" },
                { show: isBANCA, name: "CSHĐ Trong thời gian ân hạn" },
                { show: isBANCA, name: "Sống đầy cùng hè rực rỡ" },
                { show: isBANCA, name: "CSHĐ phát hành trong 3 tháng đầu tiên" },
                { show: isBANCA, name: "CSHĐ đang GĐ tháng thứ 4 đến tháng thứ 6" },
                { show: isBANCA, name: "CSHĐ đang GĐ tháng thứ 7 đến tháng thứ 9" },
                { show: isBANCA, name: "CSHĐ đến hạn thu phí  năm 3 và trong thời gian ân hạn" },
                { show: isBANCA, name: "CSHĐ hợp đồng hiệu lực năm 3" },
              ],
            },
            {
              show: true,
              name: "Thông báo",
              children: [
                { show: true, name: "Thông báo từ Bộ phận/ Phòng ban" },
                { show: true, name: "Thông báo từ FWD" },
              ],
            },
            {
              show: true,
              name: "Nhiệm vụ hôm nay	",
              children: [
                { show: true, name: "Hợp đồng đến hạn đóng phí" },
                { show: true, name: "Hợp đồng quá hạn đóng phí" },
                { show: true, name: "Sinh nhật" },
                { show: true, name: "Đăng ký giọng nói mẫu" },
              ],
            },
            {
              show: true,
              name: "Trang cá nhân",
              children: [
                { show: true, name: "Hồ sơ cá nhân" },
                { show: isAgency, name: "Cập nhật thông tin cá nhân (eRequest)" },
                { show: isAgencyorBanca, name: "Cập nhật thông tin COI" },
                { show: isAgencyorBanca, name: "Thông tin cá nhân điện tử (eProfile)" },
                { show: true, name: "Thư điện tử" },
                { show: true, name: "Quản lý mục tiêu cá nhân" },
                { show: isBANCA, name: "Hỏi Đáp" },
                { show: true, name: "Đổi mật khẩu" },
                { show: true, name: "Đăng xuất" },
              ],
            },
          ],
        },
        { show: isDO, name: "KH tiềm năng", key: "leads" },
        { show: isAgencyorBanca && isLeaderYes, name: "Duyệt ứng viên", key: "leads" },

        {
          show: true,
          name: "Thông tin hợp đồng",
          key: "policy-info",
          children: [
            {
              show: true,
              name: "Phát hành hợp đồng (HSYCBH)",
              children: [
                { show: true, name: "Khách hàng xác nhận TT" },
                { show: true, name: "Đang thẩm định" },
                { show: true, name: "Đang chờ bổ sung TT" },
                { show: true, name: "Đang chờ bản ghi âm" },
                { show: true, name: "HĐ phát hành trong tháng" },
                { show: true, name: "Không được phát hành" },
                { show: true, name: "DS phòng khám" },
              ],
            },
            {
              show: true,
              name: "Quản lý hợp đồng",
              children: [
                { show: true, name: "HĐ đến hạn thu phí" },
                { show: true, name: "HĐ mất hiệu lực" },
                { show: true, name: "Thông tin HĐ" },
                { show: true, name: "Phí BH đã nộp" },
                { show: true, name: "Các thay đổi của HĐ	" },
                { show: true, name: "Thông tin giao nhận bộ HĐBH" },
              ],
            },
            {
              show: true,
              name: "Giải quyết quyền lợi bảo hiểm",
              children: [
                { show: true, name: "Thông tin HS GQ QLBH" },
                { show: true, name: "Hướng dẫn nộp YC GQ QLBH" },
                { show: true, name: "Liên hệ" },
              ],
            },
            {
              show: true,
              name: "Hợp đồng thuộc chương trình (Campaign)",
              children: [
                { show: true, name: "DS hợp đồng" },
                { show: true, name: "Thêm mới lịch sử hoạt động" },
                { show: true, name: "Theo dõi lịch sử hoạt động" },
              ],
            },
          ],
        },
        {
          show: true,
          name: "Kết quả kinh doanh",
          key: "performance",
          children: [
            {
              show: true,
              name: "TQKD cá nhân",
              children: [
                { show: isBANCA, name: "DAILY" },
                { show: true, name: "MTD" },
                { show: isAgency, name: "QTD" },
                { show: true, name: "YTD" },
              ],
            },
            {
              show: true,
              name: "TQKD - Chi tiết",
            },
            {
              show: isAgency,
              name: "Hoa hồng tạm tính - Chi tiết",
            },
            {
              show: true,
              name: "Thông tin thu nhập",
              children: [
                { show: true, name: "Bảng kê thu nhập SOA" },
                { show: true, name: "Tải bảng lương pdf" },
              ],
            },
            {
              show: true,
              name: "Xếp hạng",
              children: [
                { show: true, name: "Xếp hạng trong nhóm" },
                { show: true, name: "Xếp hạng trong FWD" },
              ],
            },
            {
              show: isAgency,
              name: "Thành tích",
              children: [
                { show: true, name: "MDRT" },
                { show: true, name: "Elite Agent" },
              ],
            },
          ],
        },
        {
          show: isAgency,
          name: "Bảng phí dịch vụ",
          children: [
            {
              show: true,
              name: "Bảng kê phí dịch vụ",
              children: [
                { show: true, name: "Bảng phí dịch vụ" },
                { show: true, name: "Tải bảng phí pdf" },
              ],
            },
          ],
        },
        {
          show: true,
          name: "K2 và Báo cáo",
          children: [
            {
              show: true,
              name: "Tỷ lệ duy trì K2",
              children: [
                { show: isAgencyorBanca, name: "Cá nhân" },
                { show: isAgencyorBanca, name: "Nhóm" },
                { show: isIFA, name: "Agent" },
                { show: isIFA, name: "Employee" },
                { show: isBANCA, name: "Chi nhánh/PGD" },
              ],
            },
            {
              show: isIFA,
              name: "Tỷ lệ duy trì K2 chuyển giao",
              children: [
                { show: true, name: "Agent" },
                { show: true, name: "Employee" },
              ],
            },
            {
              show: isAgency,
              name: "Doanh số",
              children: [
                { show: true, name: "Cá nhân" },
                { show: true, name: "Nhóm" },
              ],
            },
            {
              show: isIFA,
              name: "Chi tiết giao dịch phí HĐBH",
            },
            {
              show: isIFA,
              name: "Hợp đồng hủy trong tháng",
            },
            {
              show: isIFA,
              name: "Hợp đồng nộp",
            },
            {
              show: isIFA,
              name: "Kết quả KD tổng",
            },
            {
              show: isIFA,
              name: "Kết quả KD chi tiết",
            },
            {
              show: isIFA,
              name: "HĐ đến hạn thu phí",
            },
            {
              show: isIFA,
              name: "HĐ quá hạn thu phí",
            },
            {
              show: isIFA,
              name: "Thông tin ACK hợp đồng",
            },
            {
              show: isIFA,
              name: "ROP",
            },
          ],
        },
        {
          show: isLeaderYes,
          name: "Quản lý nhóm",
          children: [
            {
              show: true,
              name: "KQKD Nhóm - Chi tiết",
              children: [
                { show: true, name: "Tổng quan" },
                { show: true, name: "Đóng góp" },
                { show: true, name: "Xếp hạng" },
              ],
            },

            {
              show: true,
              name: "Thông tin hợp đồng",
              children: [
                { show: true, name: "Phát hành hợp đồng (HSYCBH)" },
                { show: true, name: "Quản lý hợp đồng" },
                { show: true, name: "Giải quyết quyền lợi bảo hiểm" },
                { show: true, name: "Hợp đồng thuộc chương trình (Campaign)" },
              ],
            },
            {
              show: isAgency,
              name: "Đội ngũ kinh doanh",
              children: [
                { show: true, name: "DS tư vấn phục vụ (SA)" },
                { show: true, name: "DS tư vấn (không bao gồm SA)" },
              ],
            },
            {
              show: true,
              name: "Quản lý mục tiêu nhóm",
              children: [
                { show: true, name: "Mục tiêu nhóm theo năm" },
                { show: true, name: "Mục tiêu từng thành viên trong nhóm" },
              ],
            },
          ],
        },
        {
          show: true,
          name: "Thi đua và khen thưởng",
          children: [
            {
              show: true,
              name: "Thông tin thi đua",
            },
            {
              show: isBANCA,
              name: "Thông tin báo cáo",
            },
            {
              show: isAgency,
              name: "Thông tin tham chiếu",
            },
            {
              show: isIFA,
              name: "Chính sách thù lao",
            },
          ],
        },
        {
          show: isAgency,
          name: "DS chuyến đi",
          children: [
            {
              show: true,
              name: "Xác nhận chuyến đi",
            },
            {
              show: true,
              name: "Lịch sử xác nhận chuyến đi",
            },
          ],
        },
        {
          show: true,
          name: "Biểu mẫu và ứng dụng",
          children: [
            {
              show: true,
              name: "Biểu mẫu",
            },

            {
              show: true,
              name: "Ứng dụng",
              children: [
                {
                  show: true,
                  name: "Tải về các ứng dụng và cập nhật (Trợ thủ đắc lực IRIS)",
                },
                {
                  show: true,
                  name: "Một hệ thống phục vụ tất cả các kênh bán hàng (Công cụ hỗ trợ tư vấn/ Tư Vấn Tài Chính/ Cấp Quản lý)",
                },
                {
                  show: true,
                  name: "Sơ đồ website (Sitemap)",
                },
              ],
            },
            {
              show: true,
              name: "Link iFWD",
            },
          ],
        },
        {
          show: true,
          name: "Cài đặt",
          children: [
            { show: true, name: "Đổi mật khẩu" },
            { show: true, name: "Đăng xuất" },
          ],
        },
      ],
    };
  }, [channel, isLeader]);

  return (
    <Container>
      <ZoomAndDrag maxScale={1.3}>
        <TreeWrapper>
          <TreeNode node={data} lvl={0} total={data?.children?.filter((e) => e.show).length} />
        </TreeWrapper>
      </ZoomAndDrag>
    </Container>
  );
};

export default SiteMap;
