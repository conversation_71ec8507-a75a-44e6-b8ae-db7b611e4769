import { ValueLabel } from "@custom-types";
import { DataTable, TableConfig } from "@custom-types/config-table";
import {
  DocumentItemData,
  GetDropdownDepartmentOutput,
  GetInfoDocumentInput,
  GetListDocumentData,
  GetListDocumentOutput,
} from "@custom-types/report-document";
import { getDropdownDepartment, getListDocumentTable } from "api/report-document";
import DropDownFieldset from "components/drop-down-fieldset";
import Table from "components/table";
import TableMobile from "components/table-mobile";
import useActionApi from "hooks/use-action-api";
import { useAppSelector } from "hooks/use-redux";
import useWindowResize from "hooks/use-window-resize";
import moment from "moment";
import { useEffect, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import { downloadDocumentAction } from "screens/bieu-mau-va-ung-dung/bieu-mau/action";
import { formatValueTable } from "services/format-value";
import { FlexBox, NoData } from "styles";
import { TD, TR } from "styles/table";
import { ContentWrapper, FormSectionWrapper, TitleDocument } from "./styled";
import useSearch from "hooks/use-search";

interface FormDocumentSectionProps {
  searchValue: string;
  onClearSearch: (value: string) => void;
}

const config: TableConfig[] = [
  { label: "Tên biểu mẫu", key: "title", show: true },
  { label: "Ngày hiệu lực", key: "effectiveDate", show: true, type: "date", sort: true },
];

const FormDocumentSection = ({ searchValue, onClearSearch }: FormDocumentSectionProps) => {
  const [dataTable, setDataTable] = useState<GetListDocumentData>(null);
  const [dropdown, setDropdown] = useState<ValueLabel[]>([]);
  const [department, setDepartment] = useState(null);

  const {
    user: { channel, username },
    loading: { getDepartmentDropdownLoading, getListDocumentLoading },
  } = useAppSelector((state) => state.rootReducer);

  const dispatch = useDispatch();
  const { width } = useWindowResize();
  const data = useSearch({
    keySearch: "title",
    searchType: "includes",
    searchValue: searchValue,
    data: dataTable?.[department?.value],
  });

  const actionGetDepartment = useActionApi<GetInfoDocumentInput, GetDropdownDepartmentOutput>(getDropdownDepartment);
  const actionGetTableDocument = useActionApi<GetInfoDocumentInput, GetListDocumentOutput>(getListDocumentTable);

  useEffect(() => {
    if (username) {
      actionGetDepartment({
        body: {
          agentId: username,
          channelCd: channel,
        },
        loading: {
          name: "getDepartmentDropdownLoading",
          type: "local",
        },
      })
        .then(({ data }) => {
          setDropdown(
            data?.listData
              ?.map((item) => ({
                label: item.departmentName,
                value: item.departmentId,
              }))
              ?.sort((a, b) => a.label.localeCompare(b.label)) //sort list phòng ban - Chị Phúc's request 3/11 Tele
          );

          //default P. Thẩm định,chị Phúc's Request Tele 2/11
          const defaultValue = data?.listData?.find((i) => i.departmentId === "blt9a22da11d1885300");
          setDepartment({
            label: defaultValue?.departmentName,
            value: defaultValue?.departmentId,
          });
        })
        .catch((err) => {
          console.log(err);
          // Alert("ERROR", ERROR_API_MESSAGE);
        });

      actionGetTableDocument({
        body: {
          agentId: username,
          channelCd: channel,
        },
        loading: {
          name: "getListDocumentLoading",
          type: "local",
        },
      })
        .then(({ data }) => {
          setDataTable(data?.data);
        })
        .catch((err) => {
          console.log(err);
          // Alert("ERROR", ERROR_API_MESSAGE);
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [username, channel]);

  //Nếu có link thì redirect, còn ko có link là document => call API downloadFile
  const formatValueDocument = (data: DocumentItemData, config: TableConfig) => {
    switch (config.key) {
      case "title": {
        return (
          <TitleDocument className="color-primary pointer" onClick={() => handleClick(data)}>
            {data[config.key].toUpperCase()}
          </TitleDocument>
        );
      }
      default:
        return formatValueTable(data, config);
    }
  };

  const formatDataTable: DataTable[][] = useMemo(
    () =>
      //sort ngày theo thứ tự giảm dần -chị Phúc's Request Tele 2/11
      data
        ?.sort((a, b) => moment(b.effectiveDate).valueOf() - moment(a.effectiveDate).valueOf())
        .map((d: any) =>
          config.map((config) => ({
            config: config,
            node: formatValueDocument(d, config),
            originData: d[config.key],
          }))
        ) ?? [],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [data]
  );

  const handleClick = (data: DocumentItemData) => {
    dispatch(
      downloadDocumentAction({
        fileId: data?.fileId,
        link: data?.link,
      })
    );
  };

  const customEmptyData = useMemo(() => {
    return width > 768 ? (
      <TR>
        <TD colSpan={3}>
          <NoData>Không có dữ liệu</NoData>
        </TD>
      </TR>
    ) : (
      <FlexBox>
        <NoData>Không có dữ liệu</NoData>
      </FlexBox>
    );
  }, [width]);

  return (
    <FormSectionWrapper>
      <ContentWrapper>
        <DropDownFieldset
          list={dropdown}
          value={department}
          loading={Boolean(getDepartmentDropdownLoading)}
          placeholder="Phòng ban"
          onChange={(value) => {
            setDepartment(value);
            onClearSearch("");
          }}
        />
        <div className="mt-20">
          <Table
            data={formatDataTable}
            config={config}
            showConfig={false}
            loading={Boolean(getListDocumentLoading)}
            customEmptyData={customEmptyData}
          />
          <TableMobile
            data={formatDataTable}
            config={config}
            loading={Boolean(getListDocumentLoading)}
            customEmptyData={customEmptyData}
          />
        </div>
      </ContentWrapper>
    </FormSectionWrapper>
  );
};

export default FormDocumentSection;
