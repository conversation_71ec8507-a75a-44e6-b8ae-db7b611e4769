import { ValueLabel } from "@custom-types";
import { DataTable, TableConfig, TableNodeBody, TableNodeConfig } from "@custom-types/config-table";
import { K2Input, SummaryData } from "@custom-types/k2";
import Icons from "components/icons";
import Table from "components/table";
import TableMobile from "components/table-mobile";
import TableNode from "components/table-node";
import { useAppSelector } from "hooks/use-redux";
import { useCallback, useMemo } from "react";
import { useDispatch } from "react-redux";
import { setExpectModalK2 } from "screens/k2-va-bao-cao/k2/slice";
import { formatValue, formatValueTable } from "services/format-value";
import { checkIsAD } from "services/untils";
import { PolicyNo } from "styles";
import { ButtonSecondary } from "styles/buttons";
import { DetailContent, DetailCurrentAgent, DetailHeader, K2DetailWrapper, Name } from "./styled";

interface K2DetailProps {
  onDownloadDetail: (value: K2Input) => void;
}

const K2Detail = ({ onDownloadDetail }: K2DetailProps) => {
  const {
    detailK2Modal: { currentAgent, dataTable, headConfigTable, bodyConfigTable },
    isBotoK2,
  } = useAppSelector((state) => state.k2Reducer);

  let isAdmin = checkIsAD(currentAgent?.agentId);

  const dispatch = useDispatch();

  const {
    user: { channel },
  } = useAppSelector((state) => state.rootReducer);

  const detailCurrentData: ValueLabel[] = useMemo(() => {
    return [
      { label: "Phí năm 2 đã thu", value: currentAgent?.ap2, type: "number" },
      { label: "Phí năm 2 phải thu", value: currentAgent?.ep2, type: "number" },
      { label: "K2 thực đạt", value: currentAgent?.k2Ratio, type: "percent" },
    ];
  }, [currentAgent]);

  const configTableNode: TableNodeConfig = useMemo(() => {
    return {
      header: headConfigTable,
      body: bodyConfigTable as TableNodeBody[],
    };
  }, [headConfigTable, bodyConfigTable]);

  const handleViewK2Expect = (value: SummaryData) => {
    dispatch(
      setExpectModalK2({
        show: true,
        data: value,
        title: currentAgent.type === "DT" ? "Dự đoán tỷ lệ K2 - Cá nhân" : "Dự đoán tỷ lệ K2 - Nhóm",
      })
    );
  };

  const formatValueTableDetail = useCallback(
    (payload: any, config: TableConfig) => {
      switch (config.key) {
        case "rate":
          return channel !== "BANCA" ? (
            <PolicyNo primary onClick={() => handleViewK2Expect(payload)}>
              {formatValue(payload[config.key], config.type)}
            </PolicyNo>
          ) : (
            formatValue(payload[config.key], config.type)
          );

        default:
          return formatValueTable(payload, config);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [bodyConfigTable, dataTable, channel]
  );

  const formatDataTable: DataTable[][] = useMemo(
    () =>
      dataTable?.map((d: any) =>
        bodyConfigTable.map((config) => ({
          config: config,
          node: formatValueTableDetail(d, config),
          originData: d[config.key],
        }))
      ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [bodyConfigTable, dataTable]
  );

  //title Agency fix
  const titleDetailAgency = currentAgent?.agentId + "-" + currentAgent?.designationCd + "-" + currentAgent?.agentName;
  return (
    <K2DetailWrapper>
      <DetailContent>
        <DetailHeader>
          <Name>
            {isBotoK2 ? (
              <Icons icon="group-boto" />
            ) : currentAgent.type === "WT" ? (
              <Icons icon="group-agency" />
            ) : channel === "BROKER" ? (
              <Icons icon="people" />
            ) : (
              <Icons icon="people" />
            )}
            <h6>
              {isBotoK2
                ? `${currentAgent?.toCode} - ${currentAgent?.toName}`
                : channel === "AGENCY"
                ? titleDetailAgency
                : currentAgent?.agentInfo}
            </h6>
          </Name>
          <ButtonSecondary size="tiny" withIcon onClick={() => onDownloadDetail(currentAgent)}>
            <Icons icon="download-icon" /> Tải chi tiết hợp đồng
          </ButtonSecondary>
        </DetailHeader>
        <DetailCurrentAgent>
          {detailCurrentData.map((item, index) => (
            <p className="body-5" key={index}>
              {item.label}: <span className="h8 color-primary">{formatValue(item.value, item.type)}</span>
            </p>
          ))}
        </DetailCurrentAgent>
        {channel === "AGENCY" && isAdmin ? (
          <TableNode config={configTableNode} data={dataTable} isPrimaryTitleMobile stickyIndex={4} />
        ) : (
          <>
            <Table config={bodyConfigTable} data={formatDataTable} />
            <TableMobile config={bodyConfigTable} data={formatDataTable} />
          </>
        )}
      </DetailContent>
    </K2DetailWrapper>
  );
};

export default K2Detail;
