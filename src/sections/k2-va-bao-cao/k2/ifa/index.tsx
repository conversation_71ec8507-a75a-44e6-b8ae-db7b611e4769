import { TableConfig, TableNodeBody, TableNodeHeader } from "@custom-types/config-table";
import {
  DownloadSummaryOutput,
  K2DetailOutput,
  K2Input,
  SummaryChartOutput,
  SummaryData,
  SummaryOutput,
} from "@custom-types/k2";
import { LineChartProps } from "@custom-types/performance";
import { getDownloadDetailIfa, getDownloadSummaryIfa, getK2DetailIfa, getSummaryChartIfa, getSummaryIfa } from "api/k2";
import { Alert } from "components/alert";
import useActionApi from "hooks/use-action-api";
import { useAppSelector } from "hooks/use-redux";
import { cloneDeep, toNumber } from "lodash";
import { useCallback, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import { formatDataSummaryK2 } from "screens/k2-va-bao-cao/k2";
import { setDetailModalK2, setExpectModalK2 } from "screens/k2-va-bao-cao/k2/slice";
import { addLevelDepth, convertToValueLabel, getDateFrom } from "services/untils";
import { ERROR, ERROR_API_MESSAGE } from "../../../../@constants/message";
import { exportDetailExcel, exportReportExcel } from "../excel-template";
import FormSearchK2 from "../form-search";
import Summary from "../summary";
import { IfaK2Wrapper } from "./styled";

const tableDetailBody: TableConfig[] = [
  { key: "servicingId", label: "Mã TVTC", type: "string", show: true },
  { key: "servicingName", label: "Tên TVTC", type: "string", show: true },
  { key: "policyNumber", label: "Số hợp đồng", type: "string", show: true },
  { key: "productCode", label: "Mã sản phẩm", type: "string", show: true },
  { key: "poName", label: "Bên mua BH", type: "string", show: true },
  { key: "productName", label: "Tên sản phẩm", type: "string", show: true },
  { key: "policyStatus", label: "Tình trạng HĐ", type: "string", show: true },
  { key: "rcd", label: "Ngày hiệu lực HĐ", type: "date", show: true },
  { key: "ap2", label: "Phí năm 2 đã thu (AP2)", type: "number", show: true, sort: true },
  { key: "ep2", label: "Phí năm 2 phải thu (EP2)", type: "number", show: true, sort: true },
  { key: "k2Ratio", label: "K2 thực đạt", type: "percent", sort: true, show: true },
  { key: "partnerCode", label: "Mã đối tác", type: "string", show: false, sort: true },
  { key: "partnerName", label: "Tên đối tác", type: "string", show: false, sort: true },
  { key: "servicingStatus", label: "Tình trạng TVTC", type: "string", show: false },
];

const IfaK2 = () => {
  const [formSearch, setFormSearch] = useState({
    month: convertToValueLabel(getDateFrom("MM")),
    year: convertToValueLabel(getDateFrom("YYYY")),
  });
  const [showSummary, setShowSummary] = useState(false);
  const [dataChart, setDataChart] = useState<LineChartProps>({
    xAxis: [],
    data: [],
  });
  const [dataSummary, setDataSummary] = useState<SummaryData[]>([]);
  const currentAgentData: SummaryData = dataSummary[0];

  const {
    user: { username, channel, designation, subchannel },
  } = useAppSelector((state) => state.rootReducer);

  const dispatch = useDispatch();

  const actionGetSummary = useActionApi<K2Input, SummaryOutput[]>(getSummaryIfa);
  const actionGetSummaryChart = useActionApi<K2Input, SummaryChartOutput>(getSummaryChartIfa);
  const actionGetK2Detail = useActionApi<K2Input, K2DetailOutput>(getK2DetailIfa);
  //download
  const actionGetDownloadSummaryIfa = useActionApi<K2Input, DownloadSummaryOutput>(getDownloadSummaryIfa);
  const actionGetDownloadDetailIfa = useActionApi<K2Input, DownloadSummaryOutput>(getDownloadDetailIfa);

  const payload = useMemo(() => {
    return {
      agentCode: username,
      channelCd: channel,
      designationCd: designation,
      month: formSearch.month.value,
      year: formSearch.year.value,
    };
  }, [username, channel, designation, formSearch]);

  const handleChangeData = (key: string, value: string) => {
    setFormSearch((pre) => ({
      ...pre,
      [key]: value,
    }));
  };

  const handleSubmit = useCallback(() => {
    setShowSummary(true);

    actionGetSummaryChart({
      body: payload,
      loading: {
        type: "local",
        name: "getSummaryChartLoading",
      },
    })
      .then(({ data }) => {
        setDataChart({
          xAxis: ["", ...data.listK2IfaSummaryChart.map((item) => item.mmyyyy), ""],
          data: [
            {
              color: "#FED141",
              data: data?.listK2IfaSummaryChart?.map((item) => toNumber(item.k2Ratio)),
            },
          ],
        });
      })
      .catch((error) => {
        console.log(error);
        Alert(ERROR, ERROR_API_MESSAGE);
      });

    actionGetSummary({
      body: payload,
      loading: {
        type: "local",
        name: "getSummaryLoading",
      },
    })
      .then(({ data }) => {
        if (data[0] === null) {
          setDataSummary([]);
        } else {
          setDataSummary(formatDataSummaryK2(addLevelDepth(data)));
        }
      })
      .catch((error) => {
        console.log(error);
        Alert(ERROR, ERROR_API_MESSAGE);
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [payload]);

  const handleViewDetail = useCallback(
    (value: SummaryData) => {
      const payloadDetail = cloneDeep(payload);
      payloadDetail.agentCode = value.agentId;
      payloadDetail.designationCd = value.designationCd;

      actionGetK2Detail({
        body: payloadDetail,
        loading: {
          type: "global",
          name: "getK2DetailLoading",
        },
      })
        .then(({ data }) => {
          dispatch(
            setDetailModalK2({
              show: "detail",
              title: "Báo cáo tỷ lệ K2 theo từng hợp đồng",
              currentAgent: value,
              bodyConfigTable: tableDetailBody,
              dataTable: data?.listK2IfaDetail,
            })
          );
        })
        .catch((error) => {
          console.log("error", error);
        });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [payload, tableDetailBody]
  );

  const handleDownloadReport = useCallback(() => {
    actionGetDownloadSummaryIfa({
      body: payload,
      loading: {
        type: "global",
        name: "getDownloadReportK2Loading",
      },
    })
      .then(({ data }) => {
        exportReportExcel({
          channel,
          data,
          currentAgent: dataSummary[0],
        });
      })
      .catch((error) => {
        console.log("error", error);
        Alert(ERROR, ERROR_API_MESSAGE);
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [payload, channel, dataSummary]);

  const handleDownloadDetail = useCallback(
    (value: K2Input) => {
      const payloadDownloadDetail = cloneDeep(payload);
      payloadDownloadDetail.agentCode = value.agentId;
      payloadDownloadDetail.designationCd = value.designationCd;

      actionGetDownloadDetailIfa({
        body: payloadDownloadDetail,
        loading: {
          type: "global",
          name: "getDownloadDetailK2Loading",
        },
      })
        .then(({ data }) => {
          exportDetailExcel({
            channel,
            data,
            currentAgent: dataSummary[0],
          });
        })
        .catch((error) => {
          console.log("error", error);
          Alert(ERROR, ERROR_API_MESSAGE);
        });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [payload, channel, dataSummary]
  );

  const handleViewK2Expect = useCallback(
    (value: SummaryData) => {
      dispatch(
        setExpectModalK2({
          show: true,
          data: value,
          title: `Dự đoán tỷ lệ K2 - ${subchannel === "FWM" ? "FWM" : "Đối tác"}`,
        })
      );
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [subchannel]
  );

  const tableSummaryHeader: TableNodeHeader[][] = useMemo(() => {
    return [
      [
        { label: "Đối tác", type: "string", rowSpan: 2, isSticky: true },
        { label: `${currentAgentData?.ap2}`, type: "number", isSticky: true },
        { label: `${currentAgentData?.ep2}`, type: "number", isSticky: true },
        { label: `${currentAgentData?.k2Ratio}`, type: "percent", isSticky: true },
        { label: "Phí năm 2 cần thu thêm để đạt K2 mong đợi", type: "string", colSpan: 8 },
      ],
      [
        { label: "Phí năm 2 đã thu (AP2)", type: "string", isSticky: true },
        { label: "Phí năm 2 phải thu (EP2)", type: "string", isSticky: true },
        { label: "K2", type: "string", isSticky: true },
        { label: "50%", type: "string" },
        { label: "55%", type: "string" },
        { label: "60%", type: "string" },
        { label: "65%", type: "string" },
        { label: "70%", type: "string" },
        { label: "75%", type: "string" },
        { label: "80%", type: "string" },
        { label: "85%", type: "string" },
      ],
    ];
  }, [currentAgentData]);

  const tableSummaryBody: TableNodeBody[] = [
    {
      label: "Đối tác",
      key: "agentInfo",
      type: "string",
      isNode: true,
      primary: true,
      isSticky: true,
      onClick: (payload) => handleViewDetail(payload),
    },
    { label: "Phí năm 2 phải thu (AP2)", key: "ap2", type: "number", isSticky: true },
    { label: "Phí năm 2 phải thu (EP2)", key: "ep2", type: "number", isSticky: true },
    {
      label: "K2 thực đạt",
      key: "k2Ratio",
      type: "percent",
      isSticky: true,
      primary: true,
      isSpacing: true,
      onClick: (payload) => handleViewK2Expect(payload),
    },
    { label: "Phí năm 2 cần thu thêm để đạt K2 mong đợi", key: "", type: "string", isTitle: true },
    { label: "50%", key: "k2_50", type: "number" },
    { label: "55%", key: "k2_55", type: "number" },
    { label: "60%", key: "k2_60", type: "number" },
    { label: "65%", key: "k2_65", type: "number" },
    { label: "70%", key: "k2_70", type: "number" },
    { label: "75%", key: "k2_75", type: "number" },
    { label: "80%", key: "k2_80", type: "number" },
    { label: "85%", key: "k2_85", type: "number" },
  ];

  return (
    <IfaK2Wrapper>
      <FormSearchK2
        formSearch={formSearch}
        currentK2Agent={dataSummary[0]}
        onChange={handleChangeData}
        onSubmit={handleSubmit}
      />
      {showSummary ? (
        <Summary
          tableHeader={tableSummaryHeader}
          tableBody={tableSummaryBody}
          dataChart={dataChart}
          dataSummary={dataSummary}
          onDownloadReport={handleDownloadReport}
          onDownloadDetail={handleDownloadDetail}
        />
      ) : null}
    </IfaK2Wrapper>
  );
};

export default IfaK2;
