import { ExcelBricsConfig } from ".";

export const submission_report_config: ExcelBricsConfig[] = [
  { col: "A", row: "6", title: "STT", key: "", type: "order" },
  { col: "B", row: "6", title: "<PERSON><PERSON><PERSON> tác", key: "partner", type: "string" },
  { col: "C", row: "6", title: "<PERSON>ã quản lý trực tiếp", key: "supervisorCode", type: "string" },
  { col: "D", row: "6", title: "Tên quản lý trực tiếp", key: "supervisorName", type: "string" },
  { col: "E", row: "6", title: "<PERSON><PERSON><PERSON> danh quản lý trực tiếp", key: "supervisorDesignation", type: "string" },
  { col: "F", row: "6", title: "Mã TVTC", key: "agentCode", type: "string" },
  { col: "G", row: "6", title: "Tên TVTC", key: "agent<PERSON>ame", type: "string" },
  { col: "H", row: "6", title: "<PERSON><PERSON><PERSON> danh", key: "designation", type: "string" },
  { col: "I", row: "6", title: "Tình trạng", key: "agentStatus", type: "string" },
  { col: "J", row: "6", title: "Ngày gia nhập", key: "agentJoinedDate", type: "format-date" },
  { col: "K", row: "6", title: "Số hợp đồng", key: "policyNumber", type: "string" },
  { col: "L", row: "6", title: "Mã sản phẩm", key: "planCode", type: "string" },
  { col: "M", row: "6", title: "Tên sản phẩm", key: "prdtDesc", type: "string" },
  { col: "N", row: "6", title: "Ngày nộp HSYCBH", key: "submitDate", type: "format-date" },
  { col: "O", row: "6", title: "Tình trạng HĐ", key: "policyStatus", type: "string" },
  { col: "P", row: "6", title: "Phí BH định kỳ", key: "basicPremium", type: "number" },
  { col: "Q", row: "6", title: "Định kỳ đóng phí", key: "frequency", type: "string" },
  { col: "R", row: "6", title: "Phí BH quy năm", key: "totalPremium", type: "number" },
  { col: "S", row: "6", title: "Số tiền bảo hiểm", key: "sumAssured", type: "number" },
  { col: "T", row: "6", title: "Cases Count", key: "caseCount", type: "string" },
  { col: "U", row: "6", title: "APE", key: "ape", type: "number" },
  { col: "V", row: "6", title: "Thời điểm nộp HSYCBH", key: "submitedTime", type: "format-date-time" },
  { col: "W", row: "6", title: "Bên mua BH", key: "fullNameOw", type: "string" },
];
