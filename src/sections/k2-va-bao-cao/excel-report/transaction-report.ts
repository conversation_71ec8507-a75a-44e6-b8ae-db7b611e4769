import { ExcelBricsConfig } from ".";

export const transaction_report_config: ExcelBricsConfig[] = [
  { row: "6", col: "A", title: "STT", key: "", type: "order" },
  { title: "<PERSON><PERSON><PERSON> tác", key: "partner", row: "6", col: "B", type: "string" },
  { title: "Mã TVTC", key: "agentCode", row: "6", col: "C", type: "string" },
  { title: "Tên TVTC", key: "agentName", row: "6", col: "D", type: "string" },
  { title: "<PERSON><PERSON> hợp đồng", key: "policyNumber", row: "6", col: "E", type: "string" },
  { title: "<PERSON><PERSON><PERSON> nộp HSYCBH", key: "submitDate", row: "6", col: "F", type: "date" },
  { title: "<PERSON><PERSON><PERSON> phát hành lần đầu", key: "firstIssueDate", row: "6", col: "G", type: "date" },
  { title: "<PERSON><PERSON><PERSON> xác nhận ACK", key: "ackDate", row: "6", col: "H", type: "date" },
  { title: "<PERSON>ày nộp bản gốc TXN", key: "awplDate", row: "6", col: "I", type: "date" },
  { title: "Ngày xác nhận ACK + 21", key: "ackPassDate", row: "6", col: "J", type: "date" },
  { title: "Tình trạng HĐ", key: "policyStatus", row: "6", col: "K", type: "string" },
  { title: "Năm hợp đồng", key: "policyYear", row: "6", col: "L", type: "string" },
  { title: "Bên mua BH", key: "poName", row: "6", col: "M", type: "string" },
  { title: "Ngày giao dịch", key: "premiumDate", row: "6", col: "N", type: "date" },
  { title: "Phí BH định kỳ", key: "ip", row: "6", col: "O", type: "number" },
  { title: "Phí BH thực nộp", key: "premiumAmount", row: "6", col: "P", type: "number" },
  { title: "Phí đầu tư thêm", key: "topupAmount", row: "6", col: "Q", type: "number" },
  { title: "Sản phẩm", key: "productCode", row: "6", col: "R", type: "string" },
  { title: "Định kỳ đóng phí", key: "paymentMode", row: "6", col: "S", type: "string" },
  { title: "Ngày đến hạn đóng phí", key: "paidToDate", row: "6", col: "T", type: "date" },
];
