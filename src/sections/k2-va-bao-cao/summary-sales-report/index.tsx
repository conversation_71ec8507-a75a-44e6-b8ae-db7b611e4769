import { EMPTY_DATA, ERROR, ERROR_API_MESSAGE, WARNING } from "@constants/message";
import { TableNodeBody, TableNodeConfig, TableNodeHeader } from "@custom-types/config-table";
import { getReportSummaryDaily, getReportSummaryMtd, getReportSummaryYtd } from "api/k2";
import { Alert } from "components/alert";
import FormSearchTemplate from "components/form-search-template";
import DateInput from "components/input-date";
import MonthInput from "components/input-month";
import YearInput from "components/input-year";
import LoadingSection from "components/loading";
import RadioButton from "components/radio";
import TableNode from "components/table-node";
import useActionApi from "hooks/use-action-api";
import { useAppSelector } from "hooks/use-redux";
import moment from "moment";
import { useCallback, useEffect, useMemo, useState } from "react";
import { getDateFrom } from "services/untils";
import { Col, Row } from "styles";
import SummarySalesReportMtdDetail from "./mtd-detail";
import { SummarySaleReportWrapper } from "./styled";
import { getUpToDateMomentFormSearch } from "../constant";
import { exportReportBricsExcel } from "../excel-report";
import useWindowResize from "hooks/use-window-resize";

type SearchType = "daily" | "mtd" | "ytd";

const headerDaily: TableNodeHeader[][] = [
  [
    { label: "Đối tác", type: "string", rowSpan: 2, isSticky: true },
    { label: "Gross", type: "string", colSpan: 3, textAlign: "center" },
    { label: "Cancel in FL/CF/AFI Terminated", type: "string", colSpan: 3, textAlign: "center" },
    { label: "Net", type: "string", colSpan: 3, textAlign: "center" },
    { label: "Pending - MTD (Realtime)", type: "string", colSpan: 2, textAlign: "center" },
    { label: "Submit", type: "string", colSpan: 2, textAlign: "center" },
  ],
  [
    { label: "CC (1)", type: "string" },
    { label: "APE (2)", type: "string" },
    { label: "FYP (3)", type: "string" },
    { label: "CC (4)", type: "string" },
    { label: "APE (5)", type: "string" },
    { label: "FYP (6)", type: "string" },
    { label: "CC (7 = 1 - 4)", type: "string" },
    { label: "APE (8 = 2 - 5)", type: "string" },
    { label: "FYP (9 = 3 - 6)", type: "string" },
    { label: "CC (10)", type: "string" },
    { label: "APE (11)", type: "string" },
    { label: "CC (12)", type: "string" },
    { label: "APE (13)", type: "string" },
  ],
];

const headerMtd: TableNodeHeader[][] = [
  [
    { label: "Đối tác", type: "string", rowSpan: 3, isSticky: true },
    { label: "Traditional Product", type: "string", colSpan: 14, textAlign: "center" },
    { label: "Online Product (Net APE)", type: "string", colSpan: 6, textAlign: "center" },
    { label: "Manpower", type: "string", colSpan: 6, textAlign: "center" },
  ],
  [
    { label: "Gross", type: "string", colSpan: 3, textAlign: "center" },
    { label: "Cancel in FL/CF/AFI Terminated", type: "string", colSpan: 3, textAlign: "center" },
    { label: "Net", type: "string", colSpan: 3, textAlign: "center" },
    { label: "Pending - MTD (Realtime)", type: "string", colSpan: 2, textAlign: "center" },
    { label: "Submit", type: "string", colSpan: 2, textAlign: "center" },
    { label: "Rider/FYP (14)", type: "string", textAlign: "center", rowSpan: 2 },
    { label: "Cancer care", type: "string", textAlign: "center" },
    { label: "Big 3", type: "string", textAlign: "center" },
    { label: "PA", type: "string", textAlign: "center" },
    { label: "Lady First", type: "string", textAlign: "center" },
    { label: "Digital UL", type: "string", textAlign: "center", colSpan: 2 },
    { label: "Active Agent (20)", type: "string", textAlign: "center", rowSpan: 2 },
    { label: "Ratio (21 = 20 / 24)", type: "string", textAlign: "center", rowSpan: 2 },
    { label: "Recruited (22)", type: "string", textAlign: "center", rowSpan: 2 },
    { label: "Terminated (23)", type: "string", textAlign: "center", rowSpan: 2 },
    { label: "Total Manpower (24)", type: "string", textAlign: "center", rowSpan: 2 },
    { label: "SA Manpower (25)", type: "string", textAlign: "center", rowSpan: 2, colSpan: 2 },
  ],
  [
    { label: "CC (1)", type: "string" },
    { label: "APE (2)", type: "string" },
    { label: "FYP (3)", type: "string" },
    { label: "CC (4)", type: "string" },
    { label: "APE (5)", type: "string" },
    { label: "FYP (6)", type: "string" },
    { label: "CC (7 = 1 - 4)", type: "string" },
    { label: "APE (8 = 2 - 5)", type: "string" },
    { label: "FYP (9 = 3 - 6)", type: "string" },
    { label: "CC (10)", type: "string" },
    { label: "APE (11)", type: "string" },
    { label: "CC (12)", type: "string" },
    { label: "APE (13)", type: "string" },
    { label: "APE (15)", type: "string" },
    { label: "APE (16)", type: "string" },
    { label: "APE (17)", type: "string" },
    { label: "APE (18)", type: "string" },
    { label: "CC (19)", type: "string" },
    { label: "APE (19)", type: "string" },
  ],
];

const headerYtd: TableNodeHeader[][] = [
  [
    { label: "Đối tác", type: "string", rowSpan: 3, isSticky: true },
    { label: "Gross", type: "string", colSpan: 3, textAlign: "center" },
    { label: "Cancel in FL/CF/AFI Terminated", type: "string", colSpan: 3, textAlign: "center" },
    { label: "Net", type: "string", colSpan: 3, textAlign: "center" },
    { label: "Manpower", type: "string", colSpan: 4, textAlign: "center" },
  ],
  [
    { label: "CC (1)", type: "string" },
    { label: "APE (2)", type: "string" },
    { label: "FYP (3)", type: "string" },
    { label: "CC (4)", type: "string" },
    { label: "APE (5)", type: "string" },
    { label: "FYP (6)", type: "string" },
    { label: "CC (7 = 1 - 4)", type: "string" },
    { label: "APE (8 = 2 - 5)", type: "string" },
    { label: "FYP (9 = 3 - 6)", type: "string" },
    { label: "Recruited (10)", type: "string" },
    { label: "Terminated (11)", type: "string" },
    { label: "Total Manpower (12)", type: "string" },
    { label: "SA Manpower (13)", type: "string" },
  ],
];

const bodyDaily: TableNodeBody[] = [
  {
    label: "Đối tác",
    key: "agentInfo",
    type: "string",
    isNode: true,
    isSticky: true,
  },
  { label: "Gross", type: "string", key: "", isTitle: true },
  { label: "CC (1)", type: "number", key: "ccGross" },
  { label: "APE (2)", type: "number", key: "apeGross" },
  { label: "FYP (3)", type: "number", key: "fypGross" },
  { label: "Cancel in FL/CF/AFI Terminated", type: "string", key: "", isTitle: true },
  { label: "CC (4)", type: "number", key: "ccCancel" },
  { label: "APE (5)", type: "number", key: "apeCancel" },
  { label: "FYP (6)", type: "number", key: "fypCancel" },
  { label: "Net", type: "string", key: "", isTitle: true },
  { label: "CC (7 = 1 - 4)", type: "number", key: "ccNet" },
  { label: "APE (8 = 2 - 5)", type: "number", key: "apeNet" },
  { label: "FYP (9 = 3 - 6)", type: "number", key: "fypNet" },
  { label: "Pending - MTD (Realtime)", type: "string", key: "", isTitle: true },
  { label: "CC (10)", type: "number", key: "ccPending" },
  { label: "APE (11)", type: "number", key: "apePending" },
  { label: "Submit", type: "string", key: "", isTitle: true },
  { label: "CC (12)", type: "number", key: "ccSubmit" },
  { label: "APE (13)", type: "number", key: "apeSubmit" },
];

const bodyYtd: TableNodeBody[] = [
  {
    label: "Đối tác",
    key: "agentInfo",
    type: "string",
    isNode: true,
    isSticky: true,
  },
  { label: "Gross", type: "string", key: "", isTitle: true },
  { label: "CC (1)", type: "number", key: "ccGross" },
  { label: "APE (2)", type: "number", key: "apeGross" },
  { label: "FYP (3)", type: "number", key: "fypGross" },
  { label: "Cancel in FL/CF/AFI Terminated", type: "string", key: "", isTitle: true },
  { label: "CC (4)", type: "number", key: "ccCancel" },
  { label: "APE (5)", type: "number", key: "apeCancel" },
  { label: "FYP (6)", type: "number", key: "fypCancel" },
  { label: "Net", type: "string", key: "", isTitle: true },
  { label: "CC (7 = 1 - 4)", type: "number", key: "ccNet" },
  { label: "APE (8 = 2 - 5)", type: "number", key: "apeNet" },
  { label: "FYP (9 = 3 - 6)", type: "number", key: "fypNet" },
  { label: "Manpower", type: "string", key: "", isTitle: true },
  { label: "Recruited (10)", type: "number", key: "recruitedInYear" },
  { label: "Terminated (11)", type: "number", key: "terminatedInYear" },
  { label: "Total Manpower (12)", type: "number", key: "totalAgentNotSA" },
  { label: "SA Manpower (13)", type: "number", key: "totalAgentSA" },
];

const headerTable: Record<string, TableNodeHeader[][]> = {
  daily: headerDaily,
  mtd: headerMtd,
  ytd: headerYtd,
};

function formatDataSummarySaleReport(data: any) {
  function spreadObject(list: any) {
    list?.forEach((item: any) => {
      const data = item;
      item.agentInfo = data?.partner;

      item.agentActiveInMonth = data?.agentActiveInMonth;
      item.agentId = data?.agentId;
      item.agentName = data?.agentName;
      item.agentStatus = data?.agentStatus;
      item.apeBig3Net = data?.apeBig3Net;
      item.apeCancel = data?.apeCancel;
      item.apeCancerCareNet = data?.apeCancerCareNet;
      item.apeDULNet = data?.apeDULNet;
      item.apeGross = data?.apeGross;
      item.apeLadyFirstNet = data?.apeLadyFirstNet;
      item.apeNet = data?.apeNet;
      item.apePANet = data?.apePANet;
      item.apePending = data?.apePending;
      item.apeSubmit = data?.apeSubmit;
      item.ccCancel = data?.ccCancel;
      item.ccDULNet = data?.ccDULNet;
      item.ccGross = data?.ccGross;
      item.ccNet = data?.ccNet;
      item.ccPending = data?.ccPending;
      item.ccSubmit = data?.ccSubmit;
      item.fypCancel = data?.fypCancel;
      item.fypGross = data?.fypGross;
      item.fypNet = data?.fypNet;
      item.fypRiderNet = data?.fypRiderNet;
      item.fypRiderRatio = data?.fypRiderRatio;
      item.parentId = data?.parentId;
      item.partner = data?.partner;
      item.ratioAgentActive = data?.ratioAgentActive;
      item.recruitedInMonth = data?.recruitedInMonth;
      item.terminatedInMonth = data?.terminatedInMonth;
      item.totalAgentNotSA = data?.totalAgentNotSA;
      item.totalAgentSA = data?.totalAgentSA;
    });
  }
  spreadObject(data);

  return data;
}

const SummarySalesReportSection = () => {
  const [formSearch, setFormSearch] = useState({
    date: "",
    month: "",
    year: "",
  });
  const [data, setData] = useState<any[]>([]);
  const [type, setType] = useState<SearchType>("daily");
  const [error, setError] = useState({
    date: null,
    month: null,
    year: null,
  });
  const [header, setHeader] = useState<TableNodeHeader[][]>(headerTable["daily"]);
  const [body, setBody] = useState<TableNodeBody[]>([]);
  const [openDetailMtd, setOpenDetailMtd] = useState({
    show: false,
    data: null,
    formSearch: {
      month: "",
      year: "",
    },
  });
  const [upToDate, setUpToDate] = useState(moment().subtract(1, "day").format("DD/MM/YYYY"));

  const {
    user: { username },
    loading: { getSummarySaleReportLoading },
  } = useAppSelector((state) => state.rootReducer);

  const actionGetReportSummaryDaily = useActionApi(getReportSummaryDaily);
  const actionGetReportSummaryMtd = useActionApi(getReportSummaryMtd);
  const actionGetReportSummaryYtd = useActionApi(getReportSummaryYtd);

  const { width } = useWindowResize();

  //BODY
  const bodyMtd: TableNodeBody[] = [
    {
      label: "Đối tác",
      key: "agentInfo",
      type: "string",
      isNode: true,
      isSticky: true,
      primary: true,
      onClick: (payload) =>
        setOpenDetailMtd({
          show: true,
          data: payload,
          formSearch: {
            month: formSearch.month.split("/")[0],
            year: formSearch.month.split("/")[1],
          },
        }),
    },
    { label: "Traditional Product", key: "", type: "string", isTitle: true },
    { label: "Gross", key: "", type: "string", isSubTitle: true },
    { label: "CC (1)", type: "number", key: "ccGross" },
    { label: "APE (2)", type: "number", key: "apeGross" },
    { label: "FYP (3)", type: "number", key: "fypGross" },
    { label: "Cancel in FL/CF/AFI Terminated", key: "", type: "string", isSubTitle: true },
    { label: "CC (4)", type: "number", key: "ccCancel" },
    { label: "APE (5)", type: "number", key: "apeCancel" },
    { label: "FYP (6)", type: "number", key: "fypCancel" },
    { label: "Net", key: "", type: "string", isSubTitle: true },
    { label: "CC (7 = 1 - 4)", type: "number", key: "ccNet" },
    { label: "APE (8 = 2 - 5)", type: "number", key: "apeNet" },
    { label: "FYP (9 = 3 - 6)", type: "number", key: "fypNet" },
    { label: "Pending - MTD (Realtime)", key: "", type: "string", isSubTitle: true },
    { label: "CC (10)", type: "number", key: "ccPending" },
    { label: "APE (11)", type: "number", key: "apePending" },
    { label: "Submit", key: "", type: "string", isSubTitle: true },
    { label: "CC (12)", type: "number", key: "ccSubmit" },
    { label: "APE (13)", type: "number", key: "apeSubmit" },
    { label: "Rider/FYP (14)", type: "number", key: "fypRiderNet" },
    { label: "Online Product (Net APE)", key: "", type: "string", isTitle: true },
    { label: "Cancer care", key: "", type: "string", isSubTitle: true },
    { label: "APE (15)", type: "number", key: "apeCancerCareNet" },
    { label: "Big 3", key: "", type: "string", isSubTitle: true },
    { label: "APE (16)", type: "number", key: "apeBig3Net" },
    { label: "PA", key: "", type: "string", isSubTitle: true },
    { label: "APE (17)", type: "number", key: "apePANet" },
    { label: "Lady First", key: "", type: "string", isSubTitle: true },
    { label: "APE (18)", type: "number", key: "apeLadyFirstNet" },
    { label: "Digital UL", key: "", type: "string", isSubTitle: true },
    { label: "CC (19)", type: "number", key: "ccDULNet" },
    { label: "APE (19)", type: "number", key: "apeDULNet" },
    { label: "Manpower", key: "", type: "string", isTitle: true },
    { label: "Active Agent (20)", type: "number", key: "agentActiveInMonth" },
    { label: "Ratio (21 = 20 / 24)", type: "number", key: "ratioAgentActive" },
    { label: "Recruited (22)", type: "number", key: "recruitedInMonth" },
    { label: "Terminated (23)", type: "number", key: "terminatedInMonth" },
    { label: "Total Manpower (24)", type: "number", key: "totalAgentNotSA" },
    { label: "SA Manpower (25)", type: "number", key: "totalAgentSA" },
  ];

  const bodyTable: Record<string, TableNodeBody[]> = {
    daily: bodyDaily,
    mtd: bodyMtd,
    ytd: bodyYtd,
  };

  const config: TableNodeConfig = useMemo(() => {
    return {
      header: header,
      body: body as TableNodeBody[],
    };
  }, [header, body]);

  const disableSubmit = useMemo(() => {
    const conditions: Record<SearchType, boolean> = {
      daily: !formSearch.date,
      mtd: !formSearch.month,
      ytd: !formSearch.year,
    };

    return conditions[type] || Object.values(error).findIndex((value) => value) > -1;
  }, [type, formSearch, error]);

  const handleChangeData = (key: string, value: any) => {
    setFormSearch((pre) => ({
      ...pre,
      [key]: value,
    }));

    if (key === "date") {
      if (value.length < 10) {
        setError((err) => ({ ...err, date: "Vui lòng nhập đúng định dạng: dd/mm/yyyy" }));
      } else {
        setError((err) => ({ ...err, date: null }));
      }
    }

    if (key === "month") {
      if (value.length < 7) {
        setError((err) => ({ ...err, month: "Vui lòng nhập đúng định dạng: mm/yyyy" }));
      } else {
        if (moment(value, "MM/YYYY").isAfter(moment(), "month")) {
          setError((err) => ({
            ...err,
            month: "Dữ liệu được cập nhật đến thời điểm hiện tại. Vui lòng không chọn tháng/năm trong tương lai.",
          }));
        } else {
          setError((err) => ({ ...err, month: null }));
        }
      }
    }

    if (key === "year") {
      if (value.length < 4) {
        setError((err) => ({ ...err, year: "Vui lòng nhập đúng định dạng: yyyy" }));
      } else {
        setError((err) => ({ ...err, year: null }));
      }
    }
  };

  const handleChangeType = (type: SearchType) => {
    setType(type);
    setError({
      date: null,
      month: null,
      year: null,
    });
  };

  const handleSubmit = useCallback(() => {
    if (type === "daily") {
      setUpToDate(formSearch.date);
      actionGetReportSummaryDaily({
        body: {
          agentCode: username,
          day: formSearch.date.split("/")[0],
          month: formSearch.date.split("/")[1],
          year: formSearch.date.split("/")[2],
        },
        loading: {
          type: "local",
          name: "getSummarySaleReportLoading",
        },
      })
        .then(({ data }) => {
          if (data?.length === 0) {
            Alert(WARNING, EMPTY_DATA);
          }
          setData(formatDataSummarySaleReport(data));
        })
        .catch((err) => {
          setData([]);
          console.log(err);
          Alert(ERROR, ERROR_API_MESSAGE);
        })
        .finally(() => {
          setHeader(headerTable["daily"]);
          setBody(width > 768 ? bodyTable["daily"].filter((e) => !e.isTitle && !e.isSubTitle) : bodyTable["daily"]);
        });
    }
    if (type === "mtd") {
      const selectedMonth = formSearch.month.split("/");
      setUpToDate(getUpToDateMomentFormSearch(selectedMonth[0], selectedMonth[1]));
      actionGetReportSummaryMtd({
        body: {
          agentCode: username,
          day: "",
          month: formSearch.month.split("/")[0],
          year: formSearch.month.split("/")[1],
        },
        loading: {
          type: "local",
          name: "getSummarySaleReportLoading",
        },
      })
        .then(({ data }) => {
          if (data?.length === 0) {
            Alert(WARNING, EMPTY_DATA);
          }
          setData(formatDataSummarySaleReport(data));
        })
        .catch((err) => {
          setData([]);
          console.log(err);
          Alert(ERROR, ERROR_API_MESSAGE);
        })
        .finally(() => {
          setHeader(headerTable["mtd"]);
          setBody(width > 768 ? bodyTable["mtd"].filter((e) => !e.isTitle && !e.isSubTitle) : bodyTable["mtd"]);
        });
    }

    if (type === "ytd") {
      const isCurrentYear = formSearch.year === moment().format("YYYY");
      const preToday = moment().subtract(1, "day").format("DD/MM/YYYY");
      setUpToDate(isCurrentYear ? preToday : moment(formSearch.year, "YYYY").endOf("year").format("DD/MM/YYYY"));
      actionGetReportSummaryYtd({
        body: {
          agentCode: username,
          day: "",
          month: moment().format("MM"),
          year: formSearch.year,
        },
        loading: {
          type: "local",
          name: "getSummarySaleReportLoading",
        },
      })
        .then(({ data }) => {
          if (data?.length === 0) {
            Alert(WARNING, EMPTY_DATA);
          }
          setData(formatDataSummarySaleReport(data));
        })
        .catch((err) => {
          setData([]);
          console.log(err);
          Alert(ERROR, ERROR_API_MESSAGE);
        })
        .finally(() => {
          setHeader(headerTable["ytd"]);
          setBody(width > 768 ? bodyTable["ytd"].filter((e) => !e.isTitle && !e.isSubTitle) : bodyTable["ytd"]);
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [username, type, bodyTable, formSearch, width]);

  const handleDownloadReport = useCallback(() => {
    switch (type) {
      case "daily":
        exportReportBricsExcel({
          data: data,
          reportType: "summary-sales-report-daily",
          reportDate: formSearch.date,
          agentCode: username,
        });
        break;
      case "mtd":
        exportReportBricsExcel({
          data: data,
          reportType: "summary-sales-report-mtd",
          reportDate: formSearch.month,
          agentCode: username,
        });
        break;
      case "ytd":
        exportReportBricsExcel({
          data: data,
          reportType: "summary-sales-report-ytd",
          reportDate: formSearch.year,
          agentCode: username,
        });
        break;
      default:
        return;
    }
  }, [type, data, formSearch, username]);

  useEffect(() => {
    setBody(bodyTable["daily"]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const initialValues = {
      date: "",
      month: "",
      year: "",
    };

    switch (type) {
      case "daily":
        initialValues.date = moment().subtract(1, "d").format("DD/MM/YYYY");
        break;
      case "mtd":
        initialValues.month = getDateFrom("MM/YYYY");
        break;
      case "ytd":
        initialValues.year = getDateFrom("YYYY");
        break;
      default:
        break;
    }

    setFormSearch(initialValues);
  }, [type]);

  return (
    <SummarySaleReportWrapper>
      <FormSearchTemplate
        title="Thông tin"
        className="mb-24"
        loading={Boolean(getSummarySaleReportLoading)}
        disableSubmit={disableSubmit}
        onSubmit={handleSubmit}
        extendButton={{
          title: "Tải báo cáo",
          disableSubmit: Boolean(getSummarySaleReportLoading) || data?.length === 0,
          onSubmit: handleDownloadReport,
        }}
      >
        <p className="body-4 mb-20 color-grey-darkest">Tính đến ngày: {upToDate}</p>
        <Row spaceBetween={8} rowGap={24} rowGapMb={16}>
          <Col md={4}>
            <RadioButton
              label="Daily"
              haveBorder
              selected={type === "daily"}
              onChange={() => handleChangeType("daily")}
            />
          </Col>
          <Col md={4}>
            <RadioButton label="MTD" haveBorder selected={type === "mtd"} onChange={() => handleChangeType("mtd")} />
          </Col>
          <Col md={4}>
            <RadioButton label="YTD" haveBorder selected={type === "ytd"} onChange={() => handleChangeType("ytd")} />
          </Col>
        </Row>
        <Row spaceBetween={8} rowGapMb={16}>
          <Col md={4}>
            <DateInput
              placeholder="Chọn Ngày"
              value={formSearch.date}
              disabled={type !== "daily"}
              error={error.date}
              disableCurrentDate={true}
              enableFuture={false}
              onChange={(value) => handleChangeData("date", value as any)}
            />
          </Col>
          <Col md={4}>
            <MonthInput
              placeholder="Tháng"
              value={formSearch.month}
              error={error.month}
              disabled={type !== "mtd"}
              onChange={(value) => handleChangeData("month", value)}
            />
          </Col>
          <Col md={4}>
            <YearInput
              placeholder="Chọn Năm"
              disabled={type !== "ytd"}
              value={formSearch.year}
              error={error.year}
              onChange={(value) => handleChangeData("year", value as any)}
            />
          </Col>
        </Row>
      </FormSearchTemplate>
      <div className="relative">
        <LoadingSection loading={Boolean(getSummarySaleReportLoading)} isFullContent />
        <TableNode config={config} data={data} isPrimaryTitleMobile={type === "mtd"} stickyIndex={1} />
      </div>
      <SummarySalesReportMtdDetail
        detailMtd={openDetailMtd}
        upToDate={upToDate}
        onClose={() =>
          setOpenDetailMtd({
            show: false,
            data: null,
            formSearch: {
              month: "",
              year: "",
            },
          })
        }
      />
    </SummarySaleReportWrapper>
  );
};

export default SummarySalesReportSection;
