import moment from "moment";

export const getUpToDateMomentFormSearch = (month: string, year: string) => {
  let selectDate = month + "/" + year;
  let upToDateMoment = "";
  const isCurrentMonth = selectDate === moment().format("MM/YYYY");

  if (isCurrentMonth) {
    upToDateMoment = moment().subtract(1, "day").format("DD/MM/YYYY");
  } else {
    upToDateMoment = moment(selectDate, "MM/YYYY").endOf("month").format("DD/MM/YYYY");
  }

  return upToDateMoment;
};
