import styled from "styled-components";
import { device } from "styles/media";

export const DetailCandidateWrapper = styled.div`
  padding: 24px;
  position: relative;

  display: flex;
  gap: 16px;

  @media ${device.mobile} {
    flex-direction: column;
    padding: 16px;
  }
`;

export const InfoCandidateLeft = styled.div`
  width: 278px;
  padding: 24px 16px;
  background: ${({ theme }) => theme.color.status.primary_5};
  border-radius: 16px;
  max-height: 690px;

  display: flex;
  flex-direction: column;
  align-items: center;

  position: sticky;
  top: 80px;

  .info {
    word-break: break-word;
  }

  @media ${device.mobile} {
    width: 100%;
    position: unset;
    align-items: flex-start;
  }
`;

export const AvatarCandidate = styled.div`
  width: 120px;
  height: 120px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
  }

  @media ${device.mobile} {
    align-self: center;
  }
`;

export const InfoCandidateRight = styled.div`
  flex: 1;

  background: white;
  border-radius: 16px;
  padding: 16px;

  @media ${device.mobile} {
    width: 100%;
  }
`;

export const CommentWrapper = styled.div``;

export const TitleComment = styled.h6`
  @media ${device.mobile} {
    font-size: 16px;
  }
`;
