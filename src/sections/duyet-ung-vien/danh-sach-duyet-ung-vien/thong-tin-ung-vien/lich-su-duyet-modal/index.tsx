import { DataTable, TableConfig } from "@custom-types/config-table";
import Status from "components/status";
import Table from "components/table";
import TableMobile from "components/table-mobile";
import { useMemo } from "react";
import { formatValueTable } from "services/format-value";
import { HistoryAcceptedWrapper } from "./styled";
import { ApprovalLog } from "@custom-types/ere-approval";
import { colorHistoryAcceptsEre } from "../../constant-color-status";
import moment from "moment";

const config: TableConfig[] = [
  { label: "Người duyệt", key: "fullName", show: true },
  { label: "Trạng thái", key: "action", show: true },
  { label: "Thời gian", key: "date", show: true },
  { label: "Ghi chú", key: "comment", show: true },
];

const HistoryAcceptModal = ({ data }: { data: ApprovalLog[] }) => {
  const formatValueTableHistory = (data: any, config: TableConfig) => {
    switch (config.key) {
      case "date":
        return moment(data[config.key]).format("DD/MM/YYYY HH:mm A");
      case "action":
        return (
          <Status
            color={colorHistoryAcceptsEre[data[config.key]]}
            label={colorHistoryAcceptsEre[data[config.key]]?.label}
          />
        );
      default:
        return formatValueTable(data, config);
    }
  };

  const formatTableData: DataTable[][] = useMemo(
    () =>
      data?.map((d: any) =>
        config.map((config) => ({
          config: config,
          node: formatValueTableHistory(d, config),
          originData: d[config.key],
        }))
      ) ?? [],
    [data]
  );
  return (
    <HistoryAcceptedWrapper>
      <Table config={config} showConfig={false} showPagination={false} data={formatTableData} />
      <TableMobile config={config} data={formatTableData} />
    </HistoryAcceptedWrapper>
  );
};

export default HistoryAcceptModal;
