import { DataRenderInput } from "screens/duyet-ung-vien/danh-sach-duyet-ung-vien/thong-tin-ung-vien";
import { Col, Row } from "styles";
import { formatValue } from "../../../../../services/format-value";
import { ItemInputData } from "./styled";

interface SectionDetailProps {
  dataInput?: DataRenderInput[];
  title?: string;
}

const InputBlock = ({ dataInput, title }: SectionDetailProps) => {
  return (
    <>
      <h6 className="h7 color-primary mb-16 mt-16">{title}</h6>
      <Row rowGap={16} rowGapMb={16} isBlock>
        {dataInput.map((item, index) => (
          <Col md={item.md} key={index}>
            <ItemInputData>
              <label className="label-4">{item.label}</label>
              <h6 className="h7 mt-10">{formatValue(item.value, item.type)}</h6>
            </ItemInputData>
          </Col>
        ))}
      </Row>
    </>
  );
};

export default InputBlock;
