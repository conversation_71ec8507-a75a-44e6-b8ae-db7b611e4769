import { DataTable, TableConfig } from "@custom-types/config-table";
import { ConfigEreData, RelationshipCandidate } from "@custom-types/ere-approval";
import Table from "components/table";
import TableMobile from "components/table-mobile";
import { useCallback, useMemo } from "react";
import { getLabelOfList } from "sections/duyet-ung-vien/danh-sach-duyet-ung-vien/utils";
import { formatValueTable } from "services/format-value";

//mối quan hệ thân nhân
const configTableRelation: TableConfig[] = [
  { label: "Họ tên người thân", key: "fullName", show: true },
  { label: "<PERSON>ố<PERSON> quan hệ", key: "relationshipCode", show: true },
  { label: "Số điện thoại", key: "phone", show: true },
  { label: "Vị trí công việc", key: "jobTitle", show: true },
];

const RelationshipInfoEre = ({
  ereConfig,
  dataDetail,
}: {
  ereConfig: ConfigEreData;
  dataDetail: RelationshipCandidate[];
}) => {
  const formatValueTableRelation = useCallback(
    (data: any, config: TableConfig) => {
      switch (config.key) {
        case "relationshipCode":
          return getLabelOfList(ereConfig?.relationshipList, data[config.key]);
        // case "jobTitle":
        //   return getLabelOfList(ereConfig?.jobList, data[config.key]);
        default:
          return formatValueTable(data, config);
      }
    },
    [ereConfig?.relationshipList]
  );

  const formatTableRelation: DataTable[][] = useMemo(
    () =>
      dataDetail?.map((d: any) =>
        configTableRelation.map((config) => ({
          config: config,
          node: formatValueTableRelation(d, config),
          originData: d[config.key],
        }))
      ) ?? [],
    [dataDetail, formatValueTableRelation]
  );

  return (
    <div className="mt-16">
      <h6 className="h7 color-primary">Mối quan hệ nhân thân</h6>
      <div className="mt-16">
        <Table config={configTableRelation} data={formatTableRelation} showConfig={false} showPagination={false} />
        <TableMobile config={configTableRelation} data={formatTableRelation} />
      </div>
    </div>
  );
};

export default RelationshipInfoEre;
