import styled from "styled-components";

export const ItemInputData = styled.div`
  border: 1px solid ${({ theme }) => theme.color.status.grey};
  border-radius: 12px;
  padding: 16px;

  h6 {
    color: ${({ theme }) => theme.color.text.body} !important;
  }

  img {
    width: 51px;
    height: 48px;
    border-radius: 6px;
  }

  label {
    color: ${({ theme }) => theme.color.status.grey_darkest};
  }
`;
