import { CoiFormCandidate, CoiQuest, CoiRef, ConfigEreData } from "@custom-types/ere-approval";
import SeparateLine from "components/separate-line";
import { useMemo } from "react";
import BenefitQuestionBlock from "./benefit-block";
import BusinessLinkQuestionBlock from "./business-link-block";
import LeaderOutsideQuestionBlock from "./leader-outside-block";
import OfficialQuestionBlock from "./official-block";
import { CommitList, ContentSection } from "./styled";
import { useAppSelector } from "hooks/use-redux";

interface BenefitDeclarationSectionProps {
  coiFormData: CoiFormCandidate;
  ereConfig: ConfigEreData;
}

export interface FormatCoiQuest extends CoiQuest {
  subQuestData: CoiQuest[];
  coiRef: CoiRef[];
}
interface FormatCoiForm {
  businessLinkBenefits: FormatCoiQuest;
  leaderOutside: FormatCoiQuest;
  official: FormatCoiQuest;
  ownerBenefit: FormatCoiQuest;
  [k: string]: FormatCoiQuest;
}

const commitList = [
  "Tô<PERSON>, dưới sự hướng dẫn và hỗ trợ của nhà tuyển dụng, Tôi đã tự mình kê khai và cung cấp tất cả các thông tin, hồ sơ theo yêu cầu của FWD Việt Nam thông qua ứng dụng <span class=inline-block>“e-Recruit”</span>. Thông tin đã khai báo thể hiện tại Bộ Hồ Sơ Tuyển Dụng bao gồm nhưng không giới hạn: Thông tin đăng ký Tư Vấn Tài Chính, Bản chụp giấy tờ tùy thân(CCCD, Chứng từ xác nhận cư trú (nếu có)), Quyết định nghỉ việc, Bằng cấp liên quan và các giấy tờ khác được nộp kèm.",
  "Tôi đã đọc lại toàn bộ các thông tin kê khai, cung cấp cho FWD Việt Nam thông qua ứng dụng <span class=inline-block>“e-Recruit”</span> và xác nhận các thông tin này hoàn toàn chính xác, đúng sự thật. Tôi cam kết sẽ chịu hoàn toàn trách nhiệm trước pháp luật đối với những thông tin đã cung cấp và hiểu rằng các thông tin này sẽ được FWD Việt Nam dùng làm căn cứ cho việc thẩm định, tuyển dụng và giao kết Hợp đồng đại lý bảo hiểm với Tôi.",
  "Số điện thoại được Tôi cung cấp cho FWD Việt Nam trong Bộ Hồ Sơ Tuyển Dụng thuộc quyền sở hữu, quản lý và sử dụng hợp pháp duy nhất của Tôi. Tôi đồng ý sử dụng số điện thoại này thực hiện giao dịch với FWD Việt Nam, bao gồm cả việc nhận mã xác thực dùng một lần (OTP) từ FWD Việt Nam để thực hiện việc nộp Bộ Hồ Sơ Tuyển Dụng qua ứng dụng <span class=inline-block>“e-Recruit”</span>.",
  "Tôi đồng ý và chấp thuận cho phép FWD Việt Nam được toàn quyền thu thập, sử dụng, phân tích, chia sẻ, chuyển giao cho bên thứ ba bất kỳ các thông tin, hình ảnh của Tôi được cung cấp cho FWD Việt Nam qua ứng dụng <span class=inline-block>“e-Recruit”</span> nhằm mục đích xử lý, kiểm tra, thẩm định, đào tạo và thực hiện các thủ tục tuyển dụng, giao kết hợp đồng đại lý bảo hiểm theo quy trình của FWD Việt Nam.",
  "Tôi hiểu rõ và đồng ý nộp Bộ Hồ Sơ Tuyển Dụng cho FWD Việt Nam thông qua ứng dụng <span class=inline-block>“e-Recruit”</span> và cam đoan chịu mọi trách nhiệm pháp lý đối với việc này.",
];

const BenefitDeclarationSection = ({ coiFormData, ereConfig }: BenefitDeclarationSectionProps) => {
  const {
    user: { channel },
  } = useAppSelector((state) => state.rootReducer);

  const formatDataCoiForm: FormatCoiForm = useMemo(() => {
    const coiForm = coiFormData?.coiQuest;
    const coiRef = coiFormData?.coiRef;
    const keys = ["ownerBenefit", "leaderOutside", "businessLinkBenefits", "official"];

    const groupCoiData = coiForm?.reduce((acc, item) => {
      const existingItem = acc.find((i) => i.questId === item.questId);
      if (existingItem) {
        //nếu id trùng thì kiểm tra subQuestId
        // nếu khác null thì tạo mảng subQuestData (nếu chưa có) và push vào,nếu có rồi thì lấy mảng hiện tại tiếp tục push data vào
        if (item.subQuestId !== null) {
          existingItem.subQuestData = existingItem.subQuestData || [];
          existingItem.subQuestData.push({ ...item });
        }
      } else {
        acc.push({
          questId: item.questId,
          subQuestId: item.subQuestId,
          questName: item.questName,
          answer: item.answer,
        });
      }

      //filter data coiRef theo questId tương ứng
      return acc.map((i) => ({
        ...i,
        coiRef: coiRef?.filter((d) => d.questId === i.questId),
      }));
    }, []);

    //convert to Object follow key
    return Object.fromEntries(keys.map((key, index) => [key, groupCoiData?.[index]])) as FormatCoiForm;
  }, [coiFormData]);

  return (
    <>
      <h6 className="h7 color-primary mt-16">
        {channel === "BANCA"
          ? "Bảng kê khai xung đột lợi ích dành cho Tư vấn tài chính Kênh phân phối qua Ngân hàng"
          : "Bảng kê khai xung đột lợi ích dành cho Tư vấn tài chính"}
      </h6>
      <ContentSection>
        <BenefitQuestionBlock coiFormData={formatDataCoiForm?.ownerBenefit} config={ereConfig} />
        <SeparateLine color="#DBDFE1" height={1} width="100%" className="mt-16 mb-16" />
        <LeaderOutsideQuestionBlock coiFormData={formatDataCoiForm?.leaderOutside} config={ereConfig} />
        <SeparateLine color="#DBDFE1" height={1} width="100%" className="mt-16 mb-16" />
        <BusinessLinkQuestionBlock coiFormData={formatDataCoiForm?.businessLinkBenefits} config={ereConfig} />
        <SeparateLine color="#DBDFE1" height={1} width="100%" className="mt-16 mb-16" />
        <OfficialQuestionBlock coiFormData={formatDataCoiForm?.official} config={ereConfig} />
        {coiFormData?.coiSign?.conflictionNote && (
          <>
            <label className="label-1 mt-16">
              Bất kỳ sự xung đột lợi ích tiềm ẩn hoặc thực tế khác mà bạn nhận biết được?
            </label>
            <h6 className="h7 mt-8">{coiFormData.coiSign.conflictionNote}</h6>
          </>
        )}
        <SeparateLine color="#DBDFE1" height={1} width="100%" className="mt-16 mb-16" />
        <label className="label-1">5. Cam kết của ứng viên</label>
        <CommitList>
          {commitList.map((d, i) => (
            <li key={i}>
              <p className="body-5" dangerouslySetInnerHTML={{ __html: d }} />
            </li>
          ))}
        </CommitList>
      </ContentSection>
    </>
  );
};

export default BenefitDeclarationSection;
