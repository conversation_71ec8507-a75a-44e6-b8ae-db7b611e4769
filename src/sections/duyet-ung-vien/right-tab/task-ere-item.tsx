import Icons from "components/icons";
import { FlexBox } from "styles";
import { DateTaskWrap, EreItemWrapper } from "./styled";
import { formatValue } from "services/format-value";
import Status from "components/status";
import { colorTableStatusEre } from "../danh-sach-duyet-ung-vien/constant-color-status";

interface StatusProps {
  data: {
    name: string;
    class: string;
    date: string;
    status: string;
  };
  onViewDetail: () => void;
}

const TaskEreItem = ({ data, onViewDetail }: StatusProps) => {
  return (
    <EreItemWrapper onClick={onViewDetail} className="pointer">
      <FlexBox alignItem="center" justifyContent="space-between">
        <FlexBox direction="column" gap={4}>
          <h6 className="h7">{data?.name}</h6>
          <p className="description body-5">Lớp: {data?.class}</p>
          <Status color={colorTableStatusEre[data.status]} label={colorTableStatusEre[data.status].label} />
        </FlexBox>
        <DateTaskWrap>
          <Icons icon="icon-time-past" width={18} height={18} />
          <p className="description body-6">{formatValue(data?.date, "date")}</p>
        </DateTaskWrap>
        <Icons icon="arrow-right" />
      </FlexBox>
    </EreItemWrapper>
  );
};

export default TaskEreItem;
