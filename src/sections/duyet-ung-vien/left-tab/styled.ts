import { PriorityStyled } from "sections/home/<USER>/styled";
import styled from "styled-components";
import { HideByOpacityStyle } from "styles";
import { device } from "styles/media";

export const MainEreWrapper = styled.div`
  padding: 24px 0px 16px 16px;

  width: 55%;
  height: calc(100 * var(--vh));

  display: flex;
  flex-direction: column;

  @media ${device.mobile} {
    width: 100%;
    height: 100%;
    padding: 16px;

    .title {
      font-size: 16px;
    }
  }
`;

export const EreCard = styled.div`
  margin-top: 16px;
  width: 100%;
  height: 266px;

  position: relative;

  background: ${({ theme }) => theme.color.status.primary_20};
  border-radius: 16px;

  @media ${device.mobile} {
    height: 163px;
  }
`;

export const EreMainCard = styled.div`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);

  display: flex;
  align-items: center;

  padding: 12px 27px;
  z-index: 11;
  backdrop-filter: blur(0.8px);

  width: 320px;

  @media ${device.mobile} {
    width: 190px;
    padding: 0;
    margin-left: 12px;
    top: 45%;
    backdrop-filter: blur(0px);
    padding: 0 6px;

    h6 {
      font-size: 14px;
    }

    button {
      min-height: 32px !important;
      height: unset;
      max-width: 140px !important;
      padding: 8px !important;
      font-size: 14px;
    }

    #contract-card {
      width: 32px !important;
      height: 32px !important;
    }
  }
`;

export const SvgLine1 = styled.div`
  position: absolute;
  bottom: 0;
  z-index: 9;

  background-color: ${({ theme }) => theme.color.status.primary_50};

  height: 28px;
  width: 100%;

  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
`;

export const SvgLine3 = styled.div`
  position: absolute;
  top: 0;
  right: 0;

  width: 83.762px;
  height: 138.094px;

  background: ${({ theme }) => theme.color.status.primary_5};

  border-bottom-left-radius: 16px;
  border-top-right-radius: 16px;
`;

export const ImgTree = styled.div`
  position: absolute;
  top: 16px;
  right: 32px;
  z-index: 10;

  @media ${device.mobile} {
    width: 142.174px;
    height: 130.89px;

    top: 16px;
    right: 0px;
    img {
      width: 100%;
      height: 100%;
    }
  }
`;

export const StatusDocumentWrapper = styled.div`
  width: 100%;
  background: ${({ theme }) => theme.color.status.grey_50};
  padding: 20px 16px;
  border-radius: 16px;
`;

export const StatusRecruit = styled.div`
  padding: 20px;
  margin-top: 24px;

  border-radius: 16px;
  border: 1px solid ${({ theme }) => theme.color.status.grey};

  @media ${device.mobile} {
    padding: 16px;
  }
`;

export const ButtonTabWrap = styled.div`
  display: flex;
  gap: 16px;
  width: 100%;

  margin-top: 16px;
  margin-bottom: 20px;
`;

export const ButtonTab = styled.div<{ active: boolean }>`
  width: 100%;
  padding: 10px 16px;

  border: ${({ active, theme }) =>
    active ? `2px solid ${theme.color.status.primary}` : `2px solid ${theme.color.status.grey}`};
  color: ${({ active, theme }) => (active ? `${theme.color.status.primary}` : "unset")};
  background-color: ${({ active, theme }) => (active ? `${theme.color.status.primary_5}` : "#fff")};

  border-radius: 20px;
  cursor: pointer;

  display: flex;
  justify-content: center;

  label {
    cursor: pointer;
  }
`;

export const BarChartWrapper = styled.div`
  width: 100%;
`;

export const CardItemWait = styled.div<{ color?: any; hide?: boolean; status?: string }>`
  width: calc((100% / 3) - 12px);
  min-width: calc((100% / 3) - 12px);
  max-width: calc((100% / 3) - 12px);

  margin: 6px;

  display: flex;
  flex-direction: column;
  position: relative;

  border-radius: 12px;
  padding: 16px;
  background: ${({ theme }) => theme.color.status.white};

  cursor: pointer;

  ${({ hide }) => (hide ? HideByOpacityStyle : null)}

  p {
    margin: 8px 0 4px 0;
  }

  h3 {
    color: ${({ theme }) => theme.color.status.primary};
  }

  @media ${device.mobile} {
    padding: 12px 8px;

    p {
      font-size: 14px;
    }

    h3 {
      font-size: 25px;
    }
  }

  ${({ status }) => (status === "priority" ? PriorityStyled : null)}
`;

export const OverLayMainMobile = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;

  background: white;
  z-index: -1;
`;
