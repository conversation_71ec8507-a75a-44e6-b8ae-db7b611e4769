import React, { useMemo } from "react";
import { FlexBox, Spacing } from "styles";
import Icons from "components/icons";
import {
  EreCard,
  EreMainCard,
  ImgTree,
  BarChartWrapper,
  ButtonTab,
  ButtonTabWrap,
  CardItemWait,
  StatusDocumentWrapper,
  StatusRecruit,
  MainEreWrapper,
  SvgLine1,
  SvgLine3,
  OverLayMainMobile,
} from "./styled";
import { ButtonPrimary } from "styles/buttons";
import Carousel from "components/carousel";
import { CardProps } from "sections/home/<USER>";
import { CardContractItem, Header } from "sections/home/<USER>/styled";
import { formatNumber } from "services/untils";
import { range } from "lodash";
import dynamic from "next/dynamic";
const EChartsReact = dynamic(() => import("echarts-for-react"), { ssr: false });
import useWindowResize from "hooks/use-window-resize";
import ScrollBar from "components/scroll-bar";

interface LeftTabContentEreProps {
  dataChart: any;
  currTabChart: string;
  className: string;
  onChangeTabChart: (value: string) => void;
}

const LeftTabContentEre = ({ dataChart, currTabChart, className, onChangeTabChart }: LeftTabContentEreProps) => {
  const { width } = useWindowResize();

  const font = (size: number, weight: number) => {
    return {
      fontSize: size,
      fontWeight: weight,
      fontFamily: "FWD",
      color: "#636566",
    };
  };

  const cardList: CardProps[] = useMemo(() => {
    return [
      {
        icon: "icon-clock",
        label: "Chờ duyệt",
        value: 5,
        status: "priority",
      },
      {
        icon: "icon-info",
        label: "Bổ sung hồ sơ",
        value: 10,
        status: "normal",
      },
      {
        icon: "icon-document-plus",
        label: "Từ chối",
        value: 10,
        status: "normal",
      },
      {
        icon: "icon-document-check",
        label: "Đã duyệt",
        value: 10,
        status: "normal",
      },
    ];
  }, []);

  const option = useMemo(() => {
    return {
      textStyle: {
        ...font(12, 400),
      },
      grid: {
        top: width > 768 ? "20" : "16",
        left: width > 768 ? "16" : "0",
        right: width > 768 ? "16" : "0",
        bottom: width > 768 ? "16" : "0",
        containLabel: true,
      },

      xAxis: {
        type: "category",
        data: ["Chờ duyệt", "Bổ sung hồ sơ", "Từ chối", "Đã duyệt"],
        axisTick: {
          lineStyle: {
            width: 0,
          },
        },
        axisLabel: {
          interval: 0,
          rotate: 0,
          margin: 15,
          textStyle: {
            fontSize: width > 768 ? 12 : 10,
          },
        },
        axisPointer: {
          type: "shadow",
        },
        axisLine: {
          lineStyle: {
            width: 1,
          },
        },
      },
      yAxis: {
        type: "value",
        splitLine: {
          lineStyle: {
            color: "#F8F9F9",
          },
        },
      },
      series: [
        {
          data: dataChart,
          type: "bar",
          barWidth: width > 768 ? 52 : 42,
        },
      ],
    };
  }, [width, dataChart]);

  return (
    <MainEreWrapper className={className}>
      <OverLayMainMobile className="hide-desktop" />

      <ScrollBar height="100%" scrollX={false}>
        <FlexBox alignItem="center" justifyContent="space-between">
          <h6 className="title">Tuyển dụng</h6>
          <FlexBox alignItem="center" className="pointer">
            <Icons icon="icon-download-solid" />
            <h6 className="h7 primary">Tải tài liệu</h6>
          </FlexBox>
        </FlexBox>
        <EreCard>
          <EreMainCard>
            <FlexBox direction="column" gap={16}>
              <FlexBox alignItem="center">
                <img
                  src={`${process.env.basePath}/img/duyet-ung-vien/contract-card.svg`}
                  alt="contract-card"
                  id="contract-card"
                />
                <h6>Đăng ký ứng viên mới ngay hôm nay!</h6>
              </FlexBox>
              <ButtonPrimary size="medium">Đăng ký ứng viên mới</ButtonPrimary>
            </FlexBox>
          </EreMainCard>
          <ImgTree>
            <img src={`${process.env.basePath}/img/duyet-ung-vien/tree-card.svg`} alt="bgr" id="tree" />
          </ImgTree>
          <SvgLine1 />
          <SvgLine3 />
        </EreCard>
        <h6 className="mt-24 mb-16 title">Tình trạng hồ sơ</h6>
        <StatusDocumentWrapper>
          <div className="hide-desktop">
            <Carousel count={Math.ceil(cardList.length / 3)}>
              <>
                {cardList.map((item, index) => (
                  <CardItemWait key={index} status={item.status}>
                    <Header>
                      <Icons icon={item.icon} width={24} height={24} />
                    </Header>
                    <p>{item.label}</p>
                    <Spacing />
                    <h3>{formatNumber(item.value)}</h3>
                  </CardItemWait>
                ))}
                {cardList.length > 3
                  ? range(0, (cardList.length % 3) + 1).map((_, index) => (
                      <CardItemWait key={index} hide></CardItemWait>
                    ))
                  : null}
              </>
            </Carousel>
          </div>
          <div className="hide-mobile">
            <FlexBox gap={16}>
              {cardList.map((item, index) => (
                <CardContractItem key={index} status={item.status}>
                  <Header>
                    <Icons icon={item.icon} width={24} height={24} />
                  </Header>
                  <p>{item.label}</p>
                  <Spacing />
                  <h3>{formatNumber(item.value)}</h3>
                </CardContractItem>
              ))}
            </FlexBox>
          </div>
        </StatusDocumentWrapper>
        <StatusRecruit>
          <h6 className="title">Tình trạng tuyển dụng</h6>
          <ButtonTabWrap>
            <ButtonTab active={currTabChart === "week"} onClick={() => onChangeTabChart("week")}>
              <label>Tuần này</label>
            </ButtonTab>
            <ButtonTab active={currTabChart === "month"} onClick={() => onChangeTabChart("month")}>
              <label>Tháng này</label>
            </ButtonTab>
          </ButtonTabWrap>
          <BarChartWrapper>
            <EChartsReact option={option} notMerge={true} lazyUpdate={true} style={{ width: "100%" }} />
          </BarChartWrapper>
        </StatusRecruit>
      </ScrollBar>
    </MainEreWrapper>
  );
};

export default LeftTabContentEre;
