/* eslint-disable no-unused-vars */
import { DataTable, TableConfig } from "@custom-types/config-table";
import Icons from "components/icons";
import { useEffect, useMemo, useRef } from "react";
import { ButtonDownload } from "sections/bieu-mau-va-ung-dung/bieu-mau/styled";
import { formatValueTable } from "services/format-value";
import { TitleTraining, TrainScheduleWrapper } from "./styled";

interface TrainScheduleProps {}

const fakeData = [
  {
    name: "<PERSON><PERSON> sách Phòng Khám Liên Kết FWD",
    date: "22/4/2023",
  },
  {
    name: "BCH Bệnh tim mạch ",
    date: "22/4/2022",
  },
  {
    name: "BCH về tài chính (Dành cho TVTC)",
    date: "31/05/2021",
  },
];

const config: TableConfig[] = [
  {
    label: "<PERSON><PERSON><PERSON> cập nhật",
    key: "date",
    show: true,
    type: "date",
  },
  {
    label: "Tê<PERSON> lịch huấn luyện",
    key: "name",
    show: true,
  },
  {
    label: " ",
    key: "download",
    show: true,
    type: "function",
  },
];
const TrainSchedule = ({}: TrainScheduleProps) => {
  const renderFuncHeaderMobile = (data: any) => {
    return (
      <ButtonDownload size="tiny" withIcon className="p-6">
        <Icons icon="download-icon" />
      </ButtonDownload>
    );
  };

  const formatValueTableTraining = (data: any, config: TableConfig) => {
    switch (config.key) {
      case "download":
        return (
          <ButtonDownload size="tiny" className="p-6" withIcon>
            <Icons icon="download-icon" />
          </ButtonDownload>
        );
      default:
        return formatValueTable(data, config);
    }
  };

  const formatDataTable: DataTable[][] = useMemo(
    () =>
      fakeData
        ? fakeData.map((d: any) =>
            config.map((config) => ({
              config: config,
              node: formatValueTableTraining(d, config),
              originData: d[config.key],
              renderFuncHeaderMobile: config.type === "function" ? renderFuncHeaderMobile(d) : null,
            }))
          )
        : [],
    []
  );

  const ref = useRef(null);

  useEffect(() => {
    ref.current.click();
  }, []);

  var ScheduleMenuController;

  // app.controller("ScheduleMenuController", function ($scope, $filter, $http, $rootScope, $location, $window) {
  //   var auto_refresh;

  //   $scope.init = function () {
  //     var config = {
  //       headers: {
  //         userName: $rootScope.agentCode,
  //         userRole: "",
  //         token: $rootScope.token,
  //         language: $rootScope.language,
  //       },
  //     };

  //     var lang;

  //     if ($rootScope.language == "E") {
  //       lang = "en";
  //     } else if ($rootScope.language == "B") {
  //       lang = "id";
  //     } else if ($rootScope.language == "V") {
  //       lang = "vi";
  //     }

  //     $("#frmScheduleToken").val($rootScope.token);

  //     $("#frmSchedule").attr("action", opencms_root_link);

  //     if ($rootScope.channelCd === "AGENCY") {
  //       $("#frmScheduleUrl").val("/opencms/Agency/cms_agency_training_schedule.html?__locale=" + lang);
  //     } else if ($rootScope.channelCd === "BANCA") {
  //       $("#frmScheduleUrl").val("/opencms/Banca/cms_banca_training_schedule.html?__locale=" + lang);
  //     } else if ($rootScope.channelCd === "BROKER") {
  //       $("#frmScheduleUrl").val("/opencms/Broker/cms_broker_training_schedule.html?__locale=" + lang);
  //     }

  //     $("#frmSchedule").submit();
  //   };

  //   $scope.init();
  // });

  const token = "";
  const url = "/opencms/Agency/cms_agency_training_schedule.html?__locale=";

  return (
    <TrainScheduleWrapper>
      <TitleTraining className="h7 mt-20 mb-16">
        Kết nối “HỌC VIÊN BẢO HIỂM FWD - KÊNH ĐÀO TẠO TRỰC TUYẾN” (AOL)
      </TitleTraining>
      <div className="container">
        <form
          id="frmSchedule"
          name="frmSchedule"
          style={{ display: "none", height: "100%" }}
          method="post"
          target="frmSchedule"
          action="https://portal.fwd.com.vn/opencms/opencms/portalEntry.jsp"
        >
          <input type="text" id="frmScheduleToken" name="token" value="" />
          <input type="text" id="frmScheduleUrl" name="url" value={url} />
          <input ref={ref} type="submit" name="submit_pls" />
        </form>

        <iframe name="frmSchedule" src="" width="100%" frameBorder="0" className="download"></iframe>
      </div>
      {/* <Table data={formatDataTable} config={config} showConfig={false} />
      <TableMobile data={formatDataTable} config={config} /> */}
    </TrainScheduleWrapper>
  );
};

export default TrainSchedule;
