{"name": "fwd-cube-sap", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3013", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"axios": "^1.1.3", "crypto-js": "^4.1.1", "echarts": "^5.4.0", "echarts-for-react": "^3.0.2", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "i": "^0.3.7", "jimp": "0.16.1", "lodash": "^4.17.21", "lottie-react": "^2.4.0", "md5": "^2.3.0", "moment": "^2.29.4", "moment-timezone": "^0.5.38", "next": "13.0.2", "npm": "^9.6.2", "prettier": "^3.0.3", "qrcode.react": "^3.1.0", "query-string": "7.1.1", "react": "18.2.0", "react-beautiful-dnd": "^13.1.1", "react-cookie": "^4.1.1", "react-dom": "18.2.0", "react-input-mask": "^2.0.4", "react-number-format": "^5.1.4", "react-popper-tooltip": "2.10.1", "react-redux": "^8.0.5", "react-toastify": "^9.1.1", "react-zoom-pan-pinch": "^3.6.1", "redux": "^4.2.0", "redux-saga": "^1.2.1", "simplebar-react": "^2.4.3", "styled-components": "^5.3.6", "typescript": "4.8.4", "xlsx": "^0.18.5"}, "devDependencies": {"@reduxjs/toolkit": "^1.8.6", "@types/crypto-js": "^4.1.1", "@types/file-saver": "^2.0.5", "@types/lodash": "^4.14.187", "@types/md5": "^2.3.2", "@types/node": "18.11.9", "@types/react": "18.0.24", "@types/react-beautiful-dnd": "^13.1.2", "@types/react-dom": "18.0.8", "@types/react-input-mask": "^3.0.1", "@types/styled-components": "^5.1.26", "eslint": "^8.49.0", "eslint-config-next": "^13.4.19", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}