import { createGlobalStyle } from 'styled-components';

export const GlobalStyle = createGlobalStyle`
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html {
    font-size: 16px;
    scroll-behavior: smooth;
  }

  body {
    font-family: ${({ theme }) => theme.typography.fontFamily};
    font-size: ${({ theme }) => theme.typography.fontSize.md};
    line-height: 1.6;
    color: ${({ theme }) => theme.color.text.primary};
    background-color: ${({ theme }) => theme.color.background};
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  #root {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  a {
    color: inherit;
    text-decoration: none;
  }

  button {
    cursor: pointer;
    border: none;
    background: none;
    font-family: inherit;
  }

  input, textarea, select {
    font-family: inherit;
    font-size: inherit;
  }

  img {
    max-width: 100%;
    height: auto;
  }

  /* Scrollbar styles */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: ${({ theme }) => theme.color.surface};
  }

  ::-webkit-scrollbar-thumb {
    background: ${({ theme }) => theme.color.text.disabled};
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: ${({ theme }) => theme.color.text.secondary};
  }

  /* Focus styles */
  *:focus {
    outline: 2px solid ${({ theme }) => theme.color.primary};
    outline-offset: 2px;
  }

  /* React Toastify custom styles */
  .Toastify__toast-container {
    font-family: ${({ theme }) => theme.typography.fontFamily};
  }

  .Toastify__toast {
    border-radius: ${({ theme }) => theme.borderRadius.md};
  }

  .Toastify__toast--success {
    background-color: ${({ theme }) => theme.color.success};
  }

  .Toastify__toast--error {
    background-color: ${({ theme }) => theme.color.error};
  }

  .Toastify__toast--warning {
    background-color: ${({ theme }) => theme.color.warning};
  }

  .Toastify__toast--info {
    background-color: ${({ theme }) => theme.color.info};
  }
`;
