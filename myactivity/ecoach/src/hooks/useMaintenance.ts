import { useState, useEffect } from "react";

const useMaintenance = () => {
  const [isMaintenanceInProgress, setIsMaintenanceInProgress] = useState(false);

  useEffect(() => {
    // For now, always return false - no maintenance
    // This can be connected to actual maintenance API later
    setIsMaintenanceInProgress(false);
  }, []);

  return { isMaintenanceInProgress };
};

export default useMaintenance;
