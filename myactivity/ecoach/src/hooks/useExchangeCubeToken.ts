import { useEffect, useState } from "react";
import { useAppDispatch } from "../store/index";
import { setToken } from "../store/ecoachSlice";
import { getVerifyCubeToken } from "../api/ecoach";
import { useActionApi } from "./useActionApi";

export const useExchangeCubeToken = () => {
  const dispatch = useAppDispatch();
  const [cubeToken, setCubeToken] = useState("");

  const actionVerifyCubeToken = useActionApi(getVerifyCubeToken);

  useEffect(() => {
    // Temporarily hardcode the JWT token
    const hardcodedToken = "eyJhbGciOiJIUzUxMiJ9.eyJhdXRob3IiOlt7ImF1dGhvcml0eSI6IlJPTEVfRVpJIn1dLCJuYW1lIjoiMDg5MTAwMDMiLCJpZCI6MzAsImV4cCI6MTc1MTUzMzExNCwiaWF0IjoxNzUxNTI0MTE0LCJlbWFpbCI6IiIsImp0aSI6IjEuMTAuMCIsInVzZXJuYW1lIjoiMDg5MTAwMDMifQ.M8UEVLTPwcKFZSlHnZKhTUlqQE4nT7OBUxI6Qhi-wz-BCnBMOFh_E27fMdPq7V7UhCuqfh518d4wOQcYvxTLCA";

    dispatch(setToken(hardcodedToken));
    setCubeToken(hardcodedToken);

    // Comment out the actual API call for now
    // actionVerifyCubeToken.execute().then((response) => {
    //   const { data } = response;
    //   dispatch(setToken(data.jwt_token));
    //   setCubeToken(data.jwt_token);
    // }).catch((error) => {
    //   console.error("Failed to exchange cube token:", error);
    // });
  }, []);

  return { cubeToken };
};
