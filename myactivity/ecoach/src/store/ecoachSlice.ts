import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface EcoachState {
  token: string | null;
  isAuthenticated: boolean;
  configuration: any;
  loading: boolean;
  error: string | null;
}

const initialState: EcoachState = {
  token: null,
  isAuthenticated: false,
  configuration: null,
  loading: false,
  error: null,
};

const ecoachSlice = createSlice({
  name: 'ecoach',
  initialState,
  reducers: {
    setToken: (state, action: PayloadAction<string>) => {
      state.token = action.payload;
      state.isAuthenticated = true;
    },
    clearToken: (state) => {
      state.token = null;
      state.isAuthenticated = false;
    },
    setupConfiguration: (state, action: PayloadAction<any>) => {
      state.configuration = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const { setToken, clearToken, setupConfiguration, setLoading, setError } = ecoachSlice.actions;
export default ecoachSlice.reducer;
