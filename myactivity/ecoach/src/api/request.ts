import axios from "axios";
import { store } from "../store/index";
import { setToken } from "../store/ecoachSlice";

// Create axios instance
const request = axios.create({
  baseURL: import.meta.env.DEV ? "/" : (import.meta.env.VITE_API_ECOACH || "https://uat.guru.fwd.com/trainer"),
  timeout: 30000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor
request.interceptors.request.use(
  async (config) => {
    // Add cache control
    config.headers["cache-control"] = "no-cache";

    // Handle ecoach API authentication
    if (config.url?.startsWith("/ecoach-api")) {
      // Skip token verification for the verify token endpoint itself
      if (config.url.includes("auth/verify_vn_cookie")) {
        config.headers["country"] = "vn";
        return config;
      }

      let cubeToken = store.getState().ecoach.cubeToken;

      // If no token, we'll let the hook handle the token exchange
      // This is different from the original where it was done in the interceptor
      if (cubeToken) {
        config.headers["Authorization"] = `Bearer ${cubeToken}`;
      }

      config.headers["country"] = "vn";

      // In production, remove the /ecoach-api prefix since baseURL points to the trainer API
      if (!import.meta.env.DEV) {
        config.url = config.url.replace("/ecoach-api", "");
      }
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
request.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Handle unauthorized
      console.error("Unauthorized access");
    }

    return Promise.reject(error);
  }
);

export default request;
