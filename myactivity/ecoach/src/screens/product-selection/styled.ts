import styled from "styled-components";
import { device } from "styles/media";

export const TOTAL_ITEMS_PER_ROW = 3;
export const cardContainerPadding = 16;
export const cardPadding = 16;

interface GridProps {
  className?: string;
  columns?: string | number;
  gap?: number;
  columnGap?: number;
  rowGap?: number;
  height?: string;
  minRowHeight?: string;
  flow?: string;
  rows?: string | number;
  justifyContent?: string;
  alignContent?: string;
  maxWidthChildren?: boolean;
  mbColumns?: number;
}

const layoutGenerate = (value: any) => (typeof value === "number" ? `repeat(${value}, 1fr)` : value);

export const PageContainer = styled.div<{ paddingBottom?: number }>`
  flex: 1;
  margin-top: 24px;
  padding: ${cardContainerPadding}px;
  padding-bottom: ${({ paddingBottom }) => paddingBottom};
`;

export const ProductContainer = styled.div<GridProps>`
  display: grid;
  height: ${({ height = "auto" }) => height};

  grid-auto-flow: ${({ flow = "row" }) => flow};
  grid-auto-rows: ${({ minRowHeight }) => minRowHeight && `minmax(${minRowHeight}, auto)`};

  grid-template-columns: ${({ columns = 12 }) => layoutGenerate(columns)};
  grid-template-rows: ${({ rows }) => layoutGenerate(rows)};

  grid-gap: ${({ gap }) => gap}px;
  column-gap: ${({ columnGap }) => columnGap}px;
  row-gap: ${({ rowGap }) => rowGap}px;

  ${({ justifyContent }) => justifyContent && `justify-content: ${justifyContent}`};
  ${({ alignContent }) => alignContent && `align-content: ${alignContent}`};

  ${({ maxWidthChildren }) =>
    maxWidthChildren &&
    `
  div{
    max-width:100%;
  }
  `}

  @media ${device.mobile} {
    grid-template-columns: ${({ mbColumns }) => layoutGenerate(mbColumns)};
  }
`;

export const TitleContainer = styled.div`
  width: 100%;
  padding: 2rem;
  text-align: left;

  @media ${device.tablet} {
    width: 50%;
  }
`;

export const Title = styled.h6`
  color: ${({ theme }) => theme.color.status.dark_green_50};
  margin-top: 1rem;
  margin-bottom: 1.5rem;
  font-weight: bold;
`;

export const ProductImageContainer = styled.div`
  width: 100%;
  height: 100%;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  overflow: hidden;
`;

export const ProductImage = styled.div<{ src: string }>`
  width: 100%;
  height: 100%;

  background: ${({ src }) => `url(${src})`};
  background-position: center center;
  background-size: cover;

  @media ${device.tablet} {
    margin-left: 8px;
  }
`;

export const ProductCardContainer = styled.div<{
  isSelected: boolean;
  disabled?: boolean;
}>`
  width: 100%;
  min-height: 190px;
  max-height: 400px;
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: ${({ isSelected, theme }) =>
    isSelected ? theme.color.status.primary_15 : theme.color.status.white};
  border-radius: 12px;
  border: 2px solid ${({ isSelected, theme }) => (isSelected ? theme.color.status.primary : theme.color.status.grey)};
  padding: 0;
  cursor: pointer;
`;

export const ButtonContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  padding: 24px 0;

  @media ${device.mobile} {
    & > button {
      width: 100% !important;
      max-width: 100% !important;
    }
  }
`;

export const H6 = styled.h6<{ isSelected: boolean }>`
  font-weight: bold;
  text-align: left;
  margin-bottom: 1.5rem;
  color: ${({ isSelected, theme }) => (isSelected ? theme.color.status.primary : theme.color.status.dark_green_100)};
`;

export const SmallLabel = styled.h6`
  font-weight: normal;
  color: #a4a4a4;
  text-align: left;
`;

export const Spacer = styled.div<{ height: number }>`
  height: ${({ height }) => height}px;
`;

export const Guide = styled.a`
  padding: 0;
  display: flex;
  align-items: center;

  h6 {
    padding-left: 8px;
  }

  @media ${device.mobile} {
    margin-left: 8px;
  }
`;
