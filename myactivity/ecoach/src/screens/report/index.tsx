import React, { useCallback, useEffect, useState } from "react";
import { useRouter } from "next/router";
import { useSearchParams } from "next/navigation";
import { ReportLoading } from "../../components/report/ReportLoading";
import { Container, Main, MainContent, MainContentInner, MainContentLoading, MainLoading } from "./styled";
import ModalFullPage from "../../../../components/modal-full-page";
import { Report2 } from "../../components/report/Report2";
import LoadingSection from "../../../../components/loading";
import ReportReady from "../../components/report/ReportReady";
import { useGetConversationData } from "../../hooks/useGetConversationData";
import FeedbackModal from "../../components/modal/FeedbackModal";

const ReportScreen: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const conversationId = searchParams.get("conversationId") as string;
  const isFromVideoCall = searchParams.get("ref") === "call";
  const isFromHistoryList = searchParams.get("ref") === "history";
  const [feedbackVisible, setFeedbackVisible] = useState<boolean | undefined>();

  const {
    data: reportInfo,
    isLoading,
    getConversationReportLoading,
    isTimeout,
    showReportReady,
    setShowReportReady,
  } = useGetConversationData(conversationId);

  useEffect(() => {
    if (isTimeout) {
      router.push("/ecoach");
    }
  }, [isTimeout]);

  const goBack = useCallback(() => {
    if (isFromHistoryList) {
      router.back();
    } else {
      router.push("/ecoach");
    }
  }, [isFromHistoryList]);

  const handleUserUpdate = () => setFeedbackVisible(true);

  if ((isFromVideoCall && isLoading) || feedbackVisible) {
    return (
      <ModalFullPage show={true} title="Báo cáo" onClose={() => router.push("/ecoach")}>
        <Container className="bg-[#fffbf6] min-h-screen">
          <MainLoading className="flex flex-col items-center max-w-screen-2xl mx-auto">
            <MainContentLoading className="bg-white flex flex-col items-center w-full">
              <ReportLoading />
              <FeedbackModal
                conversationId={conversationId}
                handleUserUpdate={handleUserUpdate}
                visible={feedbackVisible || feedbackVisible === undefined}
                setVisible={setFeedbackVisible}
                title="Bạn thấy phiên làm việc như thế nào"
                feedbackType="ROLE_PLAY"
              />
            </MainContentLoading>
          </MainLoading>
        </Container>
      </ModalFullPage>
    );
  }

  if (showReportReady) {
    return (
      <ModalFullPage show={true} title="Báo cáo" onClose={() => router.push("/ecoach")}>
        <Container className="bg-[#fffbf6] min-h-screen">
          <MainLoading className="flex flex-col items-center max-w-screen-2xl mx-auto">
            <MainContentLoading className="bg-white flex flex-col items-center w-full">
              <ReportReady hideReadyReport={() => setShowReportReady(false)} />
            </MainContentLoading>
          </MainLoading>
        </Container>
      </ModalFullPage>
    );
  }

  if (isLoading || Boolean(getConversationReportLoading)) {
    return (
      <ModalFullPage show={true} title="Báo cáo" onClose={() => goBack()}>
        <Container>
          <div style={{ paddingTop: "10%" }}>
            <LoadingSection loading={true} />
          </div>
        </Container>
      </ModalFullPage>
    );
  }

  return (
    <ModalFullPage show={true} title="Báo cáo" onClose={() => goBack()}>
      <Main className="bg-[#fffbf6] min-h-screen">
        <MainContent className="flex flex-col items-center max-w-screen-md lg:max-w-screen-2xl mx-auto">
          <MainContentInner className="flex flex-col items-center w-full h-full">
            <Report2 reportDetails={reportInfo} isFromHistoryList={isFromHistoryList} />
          </MainContentInner>
        </MainContent>
      </Main>
    </ModalFullPage>
  );
};

export { ReportScreen };
