import React from 'react';
import styled from 'styled-components';

const Container = styled.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: linear-gradient(135deg, #183028 0%, #2d5a47 100%);
  color: white;
`;

const Title = styled.h1`
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-align: center;
`;

const Subtitle = styled.p`
  font-size: 1.2rem;
  margin-bottom: 2rem;
  text-align: center;
  opacity: 0.9;
`;

const ButtonContainer = styled.div`
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
`;

const ActionButton = styled.button`
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: white;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }
`;

const HomePage: React.FC = () => {
  return (
    <Container>
      <Title>Welcome to Ecoach</Title>
      <Subtitle>Practice your sales skills with AI-powered training</Subtitle>
      
      <ButtonContainer>
        <ActionButton>Start Quickfire Session</ActionButton>
        <ActionButton>Start Full Experience</ActionButton>
        <ActionButton>View Session History</ActionButton>
        <ActionButton>View Guidelines</ActionButton>
      </ButtonContainer>
    </Container>
  );
};

export default HomePage;
