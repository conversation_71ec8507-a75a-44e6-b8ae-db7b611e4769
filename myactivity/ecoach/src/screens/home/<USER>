import React, { useEffect } from "react";
import styled from "styled-components";
import { useNavigate } from "react-router-dom";
import { H3, H6, H7, Body, colors, sizes, Spacer } from "@components/CubeBaseElement";
import { useAppSelector, useAppDispatch } from "@store/index";
import { setUser } from "@store/appSlice";
import { device } from "@styles/media";
import { useExchangeCubeToken } from "@hooks/useExchangeCubeToken";
import { useGetEcoachConfigurationData } from "@hooks/useGetEcoachConfigurationData";
import { useReportHistoryLatest } from "@hooks/useReportHistoryLatest";
import useMaintenance from "@hooks/useMaintenance";
import FullScreenPage from "@components/FullScreenPage";
import MaintenancePage from "@components/Maintenance";
import NavigationCard, { NavigationCardType } from "@components/cards/NavigationCard";
import SessionQuickFireCard from "@components/cards/SessionQuickFireCard";
import CloseIcon from "@assets/icons/CloseIcon";
import GoDownIcon from "@assets/icons/GoDownIcon";
import { Rate } from "@components/icons/Rate";
import {
  avatarMobile,
  avatarTablet,
  cafeBGMobile,
  cafeBGTablet,
  goDown
} from "@assets/index";

const Container = styled.div`
  position: relative;
  width: 100%;
  height: 100vh;
  overflow-y: auto;
  background: linear-gradient(
    180deg,
    rgba(1, 1, 1, 0.5) 5.61%,
    rgba(1, 1, 1, 0) 16.81%,
    rgba(1, 1, 1, 0) 27.69%,
    #010101 52.64%
  );

  @media ${device.noMobile} {
    background: linear-gradient(
      0deg,
      #000 47%,
      rgba(0, 0, 0, 0) 65.55%,
      rgba(0, 0, 0, 0) 88.41%,
      rgba(0, 0, 0, 0.8) 100%
    );
  }
`;

const Header = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${sizes[4]}px;
  z-index: 10;
`;

const HeaderButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: ${sizes[2]}px;
  padding: ${sizes[2]}px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

const ContentWrapper = styled.div`
  display: flex;
  flex-direction: column;
  padding: ${sizes[4]}px;
  padding-top: ${sizes[16]}px;
  min-height: 100vh;
`;

const AvatarSection = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: ${sizes[8]}px;
`;

const AvatarImage = styled.img`
  width: 120px;
  height: 120px;
  margin-bottom: ${sizes[4]}px;

  @media ${device.noMobile} {
    width: 150px;
    height: 150px;
  }
`;

const WelcomeText = styled(H3)`
  color: ${colors.white};
  text-align: center;
  margin-bottom: ${sizes[2]}px;
`;

const SubText = styled(H6)`
  color: ${colors.fwdGrey[50]};
  text-align: center;
  margin-bottom: ${sizes[8]}px;
`;

const NavigationSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${sizes[4]}px;
  margin-bottom: ${sizes[8]}px;

  @media ${device.noMobile} {
    flex-direction: row;
    gap: ${sizes[6]}px;
  }
`;

const SessionHistorySection = styled.div`
  margin-top: ${sizes[8]}px;
`;

const SectionTitle = styled(H6)`
  color: ${colors.white};
  margin-bottom: ${sizes[4]}px;
  display: flex;
  align-items: center;
  gap: ${sizes[2]}px;
`;

const SessionGrid = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${sizes[3]}px;

  @media ${device.noMobile} {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: ${sizes[4]}px;
  }
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: ${sizes[8]}px;
  text-align: center;
`;

const EmptyStateText = styled(Body)`
  color: ${colors.fwdGrey[50]};
`;

const ScrollIndicator = styled.div`
  position: fixed;
  bottom: ${sizes[4]}px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${sizes[1]}px;
  z-index: 10;
`;

const ScrollText = styled(Body)`
  color: ${colors.fwdGrey[50]};
  font-size: 12px;
`;

const ScrollIcon = styled.img`
  width: 24px;
  height: 24px;
  animation: bounce 2s infinite;

  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    60% {
      transform: translateY(-5px);
    }
  }
`;

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.app);
  const { isTrainerGuruEnabled } = useAppSelector((state) => state.ecoach);

  // Initialize TGEntryPointCard logic
  const { cubeToken } = useExchangeCubeToken();
  const { ecoachConfig } = useGetEcoachConfigurationData(!!cubeToken);
  const { data: sessionHistory, loading: historyLoading } = useReportHistoryLatest(3);
  const { isMaintenanceInProgress } = useMaintenance();

  useEffect(() => {
    // Initialize user data if needed
    if (!user.name) {
      dispatch(setUser({
        channel: "AGENCY", // Allow TG card show for both AGENCY + BANCA channel
        email: "<EMAIL>",
        name: "Demo User",
        username: "demo",
        designation: "Agent",
        designationDesc: "Sales Agent",
      }));
    }
  }, [dispatch, user.name]);

  // TGEntryPointCard logic - check if ecoach should be available
  useEffect(() => {
    if (cubeToken && ecoachConfig) {
      console.log("ecoachConfig", ecoachConfig);
    }
  }, [cubeToken, ecoachConfig]);

  // Check if user can access ecoach (from TGEntryPointCard logic)
  const canAccessEcoach = () => {
    if (!cubeToken || !ecoachConfig) {
      return false;
    }

    // Allow TG card show for both AGENCY + BANCA channel
    if (!(user.channel === "BANCA" || user.channel === "AGENCY")) {
      return false;
    }

    if (!isTrainerGuruEnabled) {
      return false;
    }

    return true;
  };

  const handleQuickfirePress = () => {
    navigate("/product-selection?type=quickfire");
  };

  const handleSalesRolePlayPress = () => {
    navigate("/product-selection?type=full");
  };

  const handleFeedbackPress = () => {
    // Navigate to feedback page
    navigate("/feedback");
  };

  const handleClosePress = () => {
    // Navigate back or close app
    navigate("/");
  };

  const getBackgroundImage = () => {
    // Use window width to determine device type
    const isMobile = window.innerWidth <= 768;
    return isMobile ? cafeBGMobile : cafeBGTablet;
  };

  const getAvatarImage = () => {
    // Use window width to determine device type
    const isMobile = window.innerWidth <= 768;
    return isMobile ? avatarMobile : avatarTablet;
  };

  // Show maintenance page if in maintenance mode
  if (isMaintenanceInProgress) {
    return <MaintenancePage />;
  }

  // Show loading state while checking access
  if (!cubeToken) {
    return (
      <FullScreenPage backgroundImage={getBackgroundImage()}>
        <Container>
          <Header>
            <HeaderButton onClick={handleFeedbackPress}>
              <Rate fill={colors.white} width={20} height={20} />
            </HeaderButton>
            <HeaderButton onClick={handleClosePress}>
              <CloseIcon fill={colors.white} size={20} />
            </HeaderButton>
          </Header>
          <ContentWrapper>
            <AvatarSection>
              <AvatarImage src={getAvatarImage()} alt="Avatar" />
              <WelcomeText>Đang tải...</WelcomeText>
              <SubText>Đang kiểm tra quyền truy cập...</SubText>
            </AvatarSection>
          </ContentWrapper>
        </Container>
      </FullScreenPage>
    );
  }

  // Show access denied if user cannot access ecoach
  if (!canAccessEcoach()) {
    return (
      <FullScreenPage backgroundImage={getBackgroundImage()}>
        <Container>
          <Header>
            <HeaderButton onClick={handleFeedbackPress}>
              <Rate fill={colors.white} width={20} height={20} />
            </HeaderButton>
            <HeaderButton onClick={handleClosePress}>
              <CloseIcon fill={colors.white} size={20} />
            </HeaderButton>
          </Header>
          <ContentWrapper>
            <AvatarSection>
              <AvatarImage src={getAvatarImage()} alt="Avatar" />
              <WelcomeText>Truy cập bị hạn chế</WelcomeText>
              <SubText>
                Ecoach không khả dụng cho loại tài khoản của bạn hoặc hiện đang bị vô hiệu hóa.
              </SubText>
            </AvatarSection>
          </ContentWrapper>
        </Container>
      </FullScreenPage>
    );
  }

  return (
    <FullScreenPage backgroundImage={getBackgroundImage()}>
      <Container>
        <Header>
          <HeaderButton onClick={handleFeedbackPress}>
            <Rate fill={colors.white} width={20} height={20} />
          </HeaderButton>
          <HeaderButton onClick={handleClosePress}>
            <CloseIcon fill={colors.white} size={20} />
          </HeaderButton>
        </Header>

        <ContentWrapper>
          <AvatarSection>
            <AvatarImage src={getAvatarImage()} alt="Avatar" />
            <WelcomeText>Chào {user.name || "bạn"}</WelcomeText>
            <SubText>Hãy sẵn sàng để bán hàng</SubText>
          </AvatarSection>

          <NavigationSection>
            <NavigationCard
              navigationCardType={NavigationCardType.SalesRolePlay}
              onPress={handleSalesRolePlayPress}
            />
            <NavigationCard
              navigationCardType={NavigationCardType.Quickfire}
              onPress={handleQuickfirePress}
            />
          </NavigationSection>

          <SessionHistorySection>
            <SectionTitle>
              Lịch sử phiên gần đây
              {historyLoading && <Body color={colors.fwdGrey[50]} style={{ marginLeft: '8px' }}>Đang tải...</Body>}
            </SectionTitle>

            {sessionHistory && sessionHistory.length > 0 ? (
              <SessionGrid>
                {sessionHistory.map((session, index) => (
                  <SessionQuickFireCard
                    key={session.conversation_id}
                    session={session}
                    sessionNumber={index + 1}
                  />
                ))}
              </SessionGrid>
            ) : (
              <EmptyState>
                <EmptyStateText>
                  Chưa có phiên nào được ghi lại. Hãy bắt đầu phiên đầu tiên của bạn!
                </EmptyStateText>
              </EmptyState>
            )}
          </SessionHistorySection>

          <ScrollIndicator>
            <ScrollText>Cuộn để xem thêm</ScrollText>
            <ScrollIcon src={goDown} alt="Scroll down" />
          </ScrollIndicator>
        </ContentWrapper>
      </Container>
    </FullScreenPage>
  );
};

export default HomePage;
