import React, { use<PERSON><PERSON>back, useEffect, use<PERSON>emo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { useRouter } from "next/router";
import { useSearchParams } from "next/navigation";
import Loading from "../../../../components/loading";
import CircularProgressTimer, { DEFAULT_CALL_TIME, TWO_MIN_CALL_TIME } from "../../components/ProgressTimer";
import { CircleEllipsis } from "../../components/icons/CircleEllipsis";
import { MicOff } from "../../components/icons/MicOff";
import { MicOn } from "../../components/icons/MicOn";
import { XIcon } from "../../components/icons/Close";
import { EllipsisAnimated } from "../../components/EllipsisAnimated";
import {
  ActionButtonContainer,
  AvatarName,
  AvatarThinkingContainer,
  Close<PERSON>uttonWrapper,
  ContentContainer,
  <PERSON>er<PERSON>iew,
  <PERSON><PERSON><PERSON>,
  LoadingContainer,
  <PERSON><PERSON><PERSON><PERSON>onWrapper,
  StatusBarImage,
  VideoCallPageContainer,
} from "./styled";
import { checkAvatarAvailability } from "../../api/ecoach";
import useActionApi from "../../../../hooks/use-action-api";
import WebRTC from "../../components/webrtc";
import { nanoid } from "nanoid";
import VideoStreaming from "../../components/video-streaming";
import { DifficultType, MissionCompleteScore, ProductFlowType } from "../../@custom-types/ecoach";
import { MissionFailModal } from "screens/ecoach/components/modal/MissionFailModal";
import LeavingModal from "../../components/modal/LeavingModal";
import MissionCompleteModal from "../../components/modal/MissionCompleteModal";
import { useAppSelector } from "../../../../hooks/use-redux";
import LeavingSoSoonModal from "../../components/modal/LeavingSoSoonModal";
import WelcomeTooltipModal from "../../components/modal/WelcomeTooltipModal";
import CountdownCircleModal from "../../components/modal/CountdownCircleModal";
import AvatarNotReady from "../../components/AvatarNotReady";
import FullScreenPage from "../../components/FullScreenPage";
import {
  emptyStatus,
  heart,
  heartAni,
  heartBroken,
  stageEightStatus,
  stageFiveStatus,
  stageFourStatus,
  stageOneStatus,
  stageSevenStatus,
  stageSixStatus,
  stageTwoStatus,
} from "../../assets";
import Lottie from "lottie-react";

const VideoCallScreen: React.FC = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const {
    rootReducer: {
      loading: { checkAvatarAvailabilityLoading },
    },
    ecoachReducer: { quickfireCompletedHearts },
  } = useAppSelector((state) => state);

  const productFlowType = (searchParams.get("productFlowType") || "") as ProductFlowType;
  const productSelectionCode = searchParams.get("productSelectionCode") || "";
  const difficultType = searchParams.get("difficultType") || "";

  const [muted, setMuted] = useState(false);
  const [thinking, setThinkingState] = useState(false);
  const [isAvailable, setIsAvailable] = useState(true);
  const [avatarIsReady, setAvatarIsReady] = useState(false);
  const [avatarUuid, setAvatarUuid] = useState("");
  const [heartScore, setHeartScore] = useState(0);
  const [previousHeartScore, setPreviousHeartScore] = useState(0);
  const [iframeSrc, setIframeSrc] = useState("");
  const [websocket, setWebSocket] = useState<WebSocket | null>(null);
  const [avatarName, setAvatarName] = useState(searchParams.get("avatar") || "Vinh");
  const [endCallFlag, setEndCallFlag] = useState(false);
  const [leavingModalVisible, setLeavingModalVisible] = useState(false);
  const [showTimeOutModal, setShowTimeOutModal] = useState(false);
  const [showFailModal, setShowFailModal] = useState(false);
  const [welcomeTooltipVisible, setWelcomeTooltipVisible] = useState(false);
  const [showCountDownCircle, setShowCountDownCircle] = useState(true);
  const [conversationId, setConversationId] = useState<string>("");
  const websocketRef = useRef<{ close: (done?: () => void) => void; retake: (conversationId: string) => void }>(null);

  const [showHeartBroken, setShowHeartBroken] = useState(false);
  const [showHeart, setShowHeart] = useState(false);
  const actionCheckAvatarAvailability = useActionApi<{ voice_call_available: boolean }>(checkAvatarAvailability);

  const [reTryTime, setReTryTime] = useState(0);
  const [totalTime, setTotalTime] = useState(
    productFlowType === ProductFlowType.QUICKFIRE ? TWO_MIN_CALL_TIME : DEFAULT_CALL_TIME
  );

  const isQuickfireProduct = useMemo(() => productFlowType === ProductFlowType.QUICKFIRE, [productFlowType]);
  const isLowScore = useMemo(() => {
    return heartScore < quickfireCompletedHearts;
  }, [heartScore, quickfireCompletedHearts]);

  useEffect(() => {
    setConversationId(nanoid());
  }, []);

  useEffect(() => {
    actionCheckAvatarAvailability({
      loading: {
        type: "local",
        name: "checkAvatarAvailabilityLoading",
      },
    })
      .then((response) => {
        setIsAvailable(true);
        if (response?.data?.voice_call_available) {
          setIsAvailable(true);
        } else {
          setIsAvailable(false);
          setTimeout(() => {
            router.push("/ecoach");
          }, 3000);
        }
      })
      .catch(() => {
        router.push("/ecoach");
      });
  }, []);

  const toggleMute = useCallback(() => {
    setMuted((prev) => !prev);
  }, []);

  // Effect for keypress event
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent): void => {
      if (event.code === "Space") {
        event.preventDefault();
        toggleMute();
      }
    };

    // Add event listener
    window.addEventListener("keydown", handleKeyPress);

    // Cleanup
    return () => {
      window.removeEventListener("keydown", handleKeyPress);
    };
  }, [muted]); // Depend on muted state

  const onSocketConnected = useCallback(() => {
    setWelcomeTooltipVisible(true);
  }, []);

  const resetTotalTime = () => {
    setTotalTime(productFlowType === ProductFlowType.QUICKFIRE ? TWO_MIN_CALL_TIME : DEFAULT_CALL_TIME);
  };

  const cleanUp = () => {
    setConversationId("");
    setEndCallFlag(false);
    setShowFailModal(false);
    setShowTimeOutModal(false);
    setAvatarIsReady(false);
    setHeartScore(0);
    setWebSocket(null);
    setWelcomeTooltipVisible(false);
    resetTotalTime();
  };

  const reTake = () => {
    cleanUp();
    setShowCountDownCircle(true);
    setReTryTime(reTryTime + 1);

    const newConversationId = nanoid();
    setConversationId(newConversationId);

    websocketRef.current?.retake(newConversationId);
  };

  const goToHome = () => {
    cleanUp();
    websocketRef.current?.close(() => {
      router.push("/ecoach");
    });
  };

  const endCallAndGoReport = () => {
    cleanUp();
    websocketRef.current?.close(() => {
      router.push(`/ecoach/report?${searchParams.toString()}&conversationId=${conversationId}&ref=call`);
    });
  };

  const tryFullSessionExperience = () => {
    if (isLowScore) {
      reTake();
    } else {
      cleanUp();

      websocketRef.current?.close(() => {
        const params = new URLSearchParams(searchParams.toString());
        params.set("productFlowType", ProductFlowType.FULL_EXPERIENCE);

        router.push(`/ecoach/product-selection?${params.toString()}`);
      });
    }
  };

  const getStatusBarImage = () => {
    if (difficultType === DifficultType.Beginner) {
      if (heartScore >= 80) {
        return stageEightStatus;
      } else if (heartScore >= 70) {
        return stageSevenStatus;
      } else if (heartScore >= 60) {
        return stageSixStatus;
      } else if (heartScore >= 50) {
        return stageFiveStatus;
      } else if (heartScore >= 40) {
        return stageFourStatus;
      } else if (heartScore >= 30) {
        return stageTwoStatus;
      } else if (heartScore >= 20) {
        return stageOneStatus;
      } else {
        return emptyStatus;
      }
    } else {
      if (heartScore >= 140) {
        return stageEightStatus;
      } else if (heartScore >= 122) {
        return stageSevenStatus;
      } else if (heartScore >= 105) {
        return stageSixStatus;
      } else if (heartScore >= 87) {
        return stageFiveStatus;
      } else if (heartScore >= 52) {
        return stageFourStatus;
      } else if (heartScore >= 35) {
        return stageTwoStatus;
      } else if (heartScore >= 17) {
        return stageOneStatus;
      } else {
        return emptyStatus;
      }
    }
  };

  useEffect(() => {
    if (!conversationId) return;
    if (heartScore - previousHeartScore >= 5) {
      setShowHeart(true);
    }
    if (previousHeartScore - heartScore >= 5) {
      setShowHeartBroken(true);
      setTimeout(() => {
        setShowHeartBroken(false);
      }, 1500);
    }
    setPreviousHeartScore(heartScore);
  }, [conversationId, heartScore, previousHeartScore]);

  if (!isAvailable && !checkAvatarAvailabilityLoading) {
    return <AvatarNotReady />;
  }

  return (
    <FullScreenPage>
      {websocket && conversationId ? (
        <VideoCallPageContainer>
          {isQuickfireProduct && showHeartBroken && (
            <Lottie animationData={heartBroken} autoPlay={false} loop={false} />
          )}
          {isQuickfireProduct && showHeart && <Lottie animationData={heartAni} autoPlay={true} loop={false} />}
          {avatarIsReady && avatarUuid && (
            <VideoStreaming
              isFocused
              conversationId={conversationId}
              isTimeOut={showTimeOutModal}
              avatarId={avatarUuid || ""}
            />
          )}

          {isQuickfireProduct ? (
            <HeaderView>
              <StatusBarImage src={getStatusBarImage()} alt={""} />
              <HeartImage src={heart} alt={""} />
            </HeaderView>
          ) : null}

          <ContentContainer>
            {thinking ? (
              <AvatarThinkingContainer>
                <EllipsisAnimated>
                  <CircleEllipsis stroke="white" />
                </EllipsisAnimated>
                <div>{avatarName} đang suy nghĩ</div>
              </AvatarThinkingContainer>
            ) : (
              <></>
            )}

            <AvatarName>{avatarName}</AvatarName>

            <ActionButtonContainer>
              <CircularProgressTimer
                setLeavingModalVisible={setLeavingModalVisible}
                setTimeOutStatus={setShowTimeOutModal}
                timeRemainingNotification={isQuickfireProduct ? 30 : 60}
                totalTime={totalTime}
                reTryTime={reTryTime}
              />
              <MicButtonWrapper onClick={toggleMute}>
                {muted ? <MicOff stroke={"none"} fill={"black"} /> : <MicOn stroke={"none"} fill={"black"} />}
              </MicButtonWrapper>
              <CloseButtonWrapper onClick={() => setShowFailModal(true)}>
                <XIcon stroke={"white"} fill={"none"} />
              </CloseButtonWrapper>
            </ActionButtonContainer>
          </ContentContainer>
        </VideoCallPageContainer>
      ) : (
        <LoadingContainer>
          <Loading loading={true} />
        </LoadingContainer>
      )}

      {showCountDownCircle && (
        <CountdownCircleModal
          initialCount={5}
          onComplete={() => setShowCountDownCircle(false)}
          visible={showCountDownCircle}
        />
      )}

      {!isQuickfireProduct && showTimeOutModal && <MissionFailModal reTake={reTake} onExitRole={endCallAndGoReport} />}

      {isQuickfireProduct && leavingModalVisible && (
        <LeavingModal
          avatarName={avatarName}
          leavingMessage={"Bạn còn 30 giây nữa!"}
          onClose={() => setLeavingModalVisible(false)}
        />
      )}
      {isQuickfireProduct && welcomeTooltipVisible && !showCountDownCircle && (
        <WelcomeTooltipModal avatarName={avatarName} onClose={() => setWelcomeTooltipVisible(false)} />
      )}

      {!isQuickfireProduct && difficultType === DifficultType.Beginner && (
        <div>
          {endCallFlag && heartScore >= MissionCompleteScore.Beginner && (
            <MissionCompleteModal endCall={endCallAndGoReport} />
          )}
          {showFailModal && heartScore >= MissionCompleteScore.Beginner && (
            <MissionCompleteModal endCall={endCallAndGoReport} />
          )}
          {showFailModal && heartScore < MissionCompleteScore.Beginner && (
            <MissionFailModal reTake={reTake} onExitRole={endCallAndGoReport} />
          )}
        </div>
      )}
      {!isQuickfireProduct && difficultType === DifficultType.Expert && (
        <div>
          {endCallFlag && heartScore >= MissionCompleteScore.Expert && (
            <MissionCompleteModal endCall={endCallAndGoReport} />
          )}
          {showFailModal && heartScore >= MissionCompleteScore.Expert && (
            <MissionCompleteModal endCall={endCallAndGoReport} />
          )}
          {showFailModal && heartScore < MissionCompleteScore.Expert && (
            <MissionFailModal reTake={reTake} onExitRole={endCallAndGoReport} />
          )}
        </div>
      )}

      {isQuickfireProduct && (
        <div>
          {(endCallFlag || showTimeOutModal) && (
            <MissionCompleteModal
              showButton={true}
              primaryButtonText={isLowScore ? "Thử lại" : "Thử nhập vai"}
              secondaryButtonText={"Thoát khỏi chế độ trả lời nhanh"}
              onOkButton={tryFullSessionExperience}
              onExitRole={goToHome}
              score={heartScore}
              missCompleteScore={quickfireCompletedHearts}
            />
          )}
        </div>
      )}
      {isQuickfireProduct && showFailModal && (
        <LeavingSoSoonModal avatarName={avatarName} reTake={reTake} onExitRole={goToHome} />
      )}

      {isAvailable && conversationId ? (
        <WebRTC
          ref={websocketRef}
          conversationId={conversationId || ""}
          muted={muted}
          productFlowType={productFlowType}
          productSelectionCode={productSelectionCode}
          isTimeOut={showTimeOutModal}
          onSocketConnected={onSocketConnected}
          setAvatarIsReady={setAvatarIsReady}
          setAvatarUuid={setAvatarUuid}
          setThinkingState={setThinkingState}
          setWebSocket={setWebSocket}
          setHeartScore={setHeartScore}
          setAvatarName={setAvatarName}
          setEndCallFlag={setEndCallFlag}
        />
      ) : (
        <div></div>
      )}
    </FullScreenPage>
  );
};

export { VideoCallScreen };
