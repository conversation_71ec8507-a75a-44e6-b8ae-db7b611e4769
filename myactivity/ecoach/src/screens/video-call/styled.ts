import styled from "styled-components";
import { device } from "styles/media";
import { sizes } from "../../components/CubeBaseElement";
import Image from "next/image";

export const VideoCallPageContainer = styled.div`
  position: "relative";
`;

export const FrameContainer = styled.div`
  overflow: hidden;
  height: 90 dvh;
  align-content: "center";
`;

export const AvatarIframe = styled.iframe`
  height: 100vh;
  transform: translateX(-33.33%);
  overflow: hidden;
  width: 100%;

  @media ${device.noMobile} {
    transform: translateX(0);
  }
`;

export const HeaderView = styled.div({
  flexDirection: "row",
  paddingLeft: sizes[4],
  paddingTop: sizes[4],
  justifyContent: "flex-start",
  width: "100%",
  position: "absolute",
  display: "flex",
  top: 40,
  left: "4%",
});

export const StatusBarImage = styled(Image)(() => ({
  width: 180,
  height: 21,
}));

export const HeartImage = styled(Image)(() => ({
  width: 20,
  height: 17,
  marginLeft: 10,
  marginTop: sizes[1],
}));
export const LoadingContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  height: 80vh;
`;

export const ActionButtonWrapper = styled.div<{ textColor?: string; backgroundColor?: string; hoverColor?: string }>`
  border-radius: 9999px;
  padding: 0.5rem;
  width: 4rem;
  min-width: 4rem;
  height: 4rem;
  display: flex;
  color: ${({ textColor = "black" }) => textColor};
  background-color: ${({ backgroundColor = "white" }) => backgroundColor};
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;

  &:hover {
    color: #000;
    background-color: ${({ hoverColor = "white" }) => hoverColor};

    svg {
      fill: ${({ hoverColor = "white" }) => hoverColor};
    }
  }
`;

export const MicButtonWrapper = styled(ActionButtonWrapper)`
  color: ${({ textColor = "black" }) => textColor};
  background-color: ${({ backgroundColor = "white" }) => backgroundColor};

  &:hover {
    background-color: ${({ hoverColor = "#dedede" }) => hoverColor};

    svg {
      fill: ${({ hoverColor = "#333" }) => hoverColor};
    }
  }
`;

export const CloseButtonWrapper = styled(ActionButtonWrapper)`
  color: ${({ textColor = "white" }) => textColor};
  background-color: ${({ backgroundColor = "rgb(239, 68, 6)" }) => backgroundColor};

  &:hover {
    background-color: ${({ hoverColor = "rgb(252,82,82)" }) => hoverColor};

    svg {
      fill: ${({ hoverColor = "rgb(255,235,235)" }) => hoverColor};
    }
  }
`;

export const ActionButtonContainer = styled.div`
  columns: 2;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 0.5rem;
  margin-right: 0.5rem;
  gap: 0.5rem;

  @media ${device.noMobile} {
    margin-right: calc(1rem);
    margin-left: calc(1rem);
  }
`;

export const AvatarThinkingContainer = styled.div`
  display: flex;
  margin-left: 0.5rem;
  margin-right: 0.5rem;
  justify-content: center;
  color: white;
`;
export const AvatarName = styled.div`
  text-align: center;
  font-weight: bold;
  color: white;
  margin-bottom: 1rem;
`;

export const ContentContainer = styled.div`
  position: absolute;
  bottom: 2.5rem;
  right: 50%;
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  transform: translateX(50%);
  min-width: 250px;
`;
