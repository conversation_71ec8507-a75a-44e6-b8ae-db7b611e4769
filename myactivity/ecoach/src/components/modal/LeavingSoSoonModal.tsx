import React, { useCallback, useState } from "react";
import styled from "styled-components";
import { colors, H2, H6, sizes } from "../CubeBaseElement";
import { device } from "../../../../styles/media";
import Modal from "./BaseModal";
import { ButtonPrimary, ButtonSecondary } from "../../../../styles/buttons";

const CenterView = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  //width: windowWidth - sizes[4] * 2,
  height: 450px;
  max-width: 380px;
  border-radius: ${sizes[4]}px;
  padding: ${sizes[6]}px;
  border: 5px solid ${colors.white};
`;

const Container = styled.div`
  display: flex;
  flex: 1;
  //background-color: rgba(24, 48, 40, 0.95);
  border-radius: ${sizes[4]}px;
  overflow: hidden;
  justify-content: center;
  align-items: center;

  @media ${device.mobile} {
    margin-left: ${sizes[1]}px;
    margin-right: ${sizes[1]}px;
  }
`;

const Spacer = styled.div<{ height: number }>`
  height: ${({ height }) => height}px;
`;

type ModalProps = {
  title?: string;
  avatarName: string;
  reTake: () => void;
  onExitRole: () => void;
};

const LeavingSoSoonModal = ({ title, avatarName, reTake, onExitRole }: ModalProps) => {
  const [modalVisible, setModalVisible] = useState(true);

  const tryAgain = useCallback(() => {
    setModalVisible(false);
    reTake();
  }, [reTake]);

  return (
    <Modal show={modalVisible} size="ssm" title="" titleAlign="center">
      <Container>
        <CenterView>
          <H2 fontWeight="bold" color={colors.white} style={{ textAlign: "center", paddingBottom: `${sizes[8]}px` }}>
            {title || "Rời đi sớm vậy sao?"}
          </H2>
          <H6
            fontWeight={"normal"}
            color={colors.fwdGrey[20]}
            style={{ textAlign: "center", paddingTop: sizes[4], paddingBottom: sizes[4] }}
          >
            {avatarName} buồn khi thấy bạn rời đi :(
          </H6>
          <Spacer height={sizes[4]} />
          <ButtonSecondary onClick={tryAgain}>Thử lại</ButtonSecondary>
          <Spacer height={sizes[4]} />
          <ButtonPrimary onClick={onExitRole}>Thoát khỏi chế độ trả lời nhanh</ButtonPrimary>
        </CenterView>
      </Container>
    </Modal>
  );
};

export default LeavingSoSoonModal;
