import React, { useState } from "react";
import styled from "styled-components";
import { colors, sizes } from "../CubeBaseElement";
import { ConversationFeedBackType } from "../../@custom-types/ecoach";
import Modal from "./BaseModal";
import CloseIcon from "../../assets/icons/CloseIcon";
import { FeedbackSubmitted } from "../feedback/FeedbackSubmitted";
import { FeedbackForm } from "../feedback/FeedbackForm";

const Container = styled.div`
  flex-direction: column;
  align-items: center;
  justify-content: center;
  display: flex;
  flex: 1;
  width: 100%;
  height: 100%;
`;

const CenterView = styled.div`
  width: 343px;
  display: flex;
  flex-direction: column;
  align-self: center;
  justify-content: center;
  border-radius: ${sizes[4]}px;
  padding-top: ${sizes[5]}px;
  background-color: ${colors.white};
  padding: ${sizes[4]}px;
`;

type RolePlayQualityFeedbackProps = {
  conversationId?: string;
  visible: boolean;
  setVisible: (visible: boolean) => void;
  handleUserUpdate?: (selectedStar?: number) => void;
  starRating?: number;
  title?: string;
  feedbackType?: ConversationFeedBackType;
};

const IconView = styled.div`
  width: 343px;
  display: flex;
  align-self: center;
  justify-content: flex-end;
`;
const IconButton = styled.div`
  display: flex;
  border-radius: ${sizes[5]}px;
  padding-bottom: ${sizes[2]}px;
  cursor: pointer;
`;

const FeedbackModal = ({
  conversationId,
  visible,
  setVisible,
  handleUserUpdate,
  starRating,
  title,
  feedbackType,
}: RolePlayQualityFeedbackProps) => {
  const [feedbackSubmitted, setFeedbackIsSubmitted] = useState<boolean>(false);

  const hideFeedbackModal = () => {
    if (visible) {
      setVisible(false);
    }
  };

  const onSubmitted = (value: boolean) => {
    setFeedbackIsSubmitted(value);
    setTimeout(() => {
      setVisible(false);
      setFeedbackIsSubmitted(false);
    }, 2000);
  };

  return (
    <Modal show={visible}>
      <Container>
        {!feedbackSubmitted && (
          <IconView>
            <IconButton onClick={hideFeedbackModal}>
              <CloseIcon fill={colors.fwdOrange[100]} size={26} />
            </IconButton>
          </IconView>
        )}
        <CenterView>
          {feedbackSubmitted ? (
            <FeedbackSubmitted showGoHomeButton={false} />
          ) : (
            <FeedbackForm
              selectedStar={starRating || 0}
              conversationId={conversationId}
              title={title || "Bạn thấy phiên làm việc như thế nào"}
              feedbackType={feedbackType || "ROLE_PLAY"}
              onSubmitted={onSubmitted}
              handleUserUpdate={handleUserUpdate}
            />
          )}
        </CenterView>
      </Container>
    </Modal>
  );
};

export default FeedbackModal;
