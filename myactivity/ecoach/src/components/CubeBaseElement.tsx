import styled from "styled-components";

interface TextProps {
  fontWeight?: string;
  color?: string;
}

export const H1 = styled.span<{ fontWeight?: string; color?: string }>((props) => ({
  fontSize: 61,
  // lineHeight: 76.25,
  fontWeight: props.fontWeight,
  color: props.color,
}));

export const H2 = styled.span<{ fontWeight?: string; color?: string }>((props) => ({
  fontSize: 49,
  // lineHeight: 61.25,
  fontWeight: props.fontWeight,
  color: props.color,
}));

export const H3 = styled.span<{ fontWeight?: string; color?: string }>((props) => ({
  fontSize: 39,
  // lineHeight: 48.75,
  fontWeight: props.fontWeight,
  color: props.color,
}));

export const H4 = styled.span<{ fontWeight?: string; color?: string }>((props) => ({
  fontSize: 31,
  // lineHeight: 38.75,
  fontWeight: props.fontWeight,
  color: props.color,
}));

export const H5 = styled.span<{ fontWeight?: string; color?: string }>((props) => ({
  fontSize: 25,
  // lineHeight: 31.25,
  fontWeight: props.fontWeight,
  color: props.color,
}));

export const H6 = styled.span<{ fontWeight?: string; color?: string }>((props) => ({
  fontSize: 20,
  // lineHeight: 25,
  fontWeight: props.fontWeight,
  color: props.color,
}));

export const H7 = styled.span<{ fontWeight?: string; color?: string }>((props) => ({
  fontSize: 16,
  // lineHeight: 20,
  fontWeight: props.fontWeight,
  color: props.color,
}));

export const H8 = styled.span<{ fontWeight?: string; color?: string }>((props) => ({
  fontSize: 14,
  // lineHeight: 17.5,
  fontWeight: props.fontWeight,
  color: props.color,
}));

export const ExtraLargeBody = styled.span<{ fontWeight?: string; color?: string }>((props) => ({
  fontSize: 20,
  // lineHeight: 30,
  fontWeight: props.fontWeight,
  color: props.color,
}));
export const LargeBody = styled.span<{ fontWeight?: string; color?: string }>((props) => ({
  fontSize: 16,
  // lineHeight: 24,
  fontWeight: props.fontWeight,
  color: props.color,
}));
export const Body = styled.span<{ fontWeight?: string; color?: string }>((props) => ({
  fontSize: 14,
  // lineHeight: 21,
  fontWeight: props.fontWeight,
  color: props.color,
}));
export const SmallBody = styled.span<{ fontWeight?: string; color?: string }>((props) => ({
  fontSize: 12,
  // lineHeight: 18,
  fontWeight: props.fontWeight,
  color: props.color,
}));

export const LargeLabel = styled.span<{ fontWeight?: string; color?: string }>((props) => ({
  fontSize: 16,
  // lineHeight: 20,
  fontWeight: props.fontWeight,
  color: props.color,
}));

export const Label = styled.span<{ fontWeight?: string; color?: string }>((props) => ({
  fontSize: 14,
  // lineHeight: 17.5,
  fontWeight: props.fontWeight,
  color: props.color,
}));
export const SmallLabel = styled.span<{ fontWeight?: string; color?: string }>((props) => ({
  fontSize: 12,
  // lineHeight: 15,
  fontWeight: props.fontWeight,
  color: props.color,
}));
export const ExtraSmallLabel = styled.span<{ fontWeight?: string; color?: string }>((props) => ({
  fontSize: 10,
  // lineHeight: 12.5,
  fontWeight: props.fontWeight,
  color: props.color,
}));

export const Spacer = styled.div<{ height: number }>((props) => ({
  height: props.height,
}));

export const sizes = new Proxy<Record<number, number>>(
  {},
  {
    get: (_, key) => {
      const numKey = Number(key);
      if (isNaN(numKey)) {
        throw new Error("Key must be a number");
      }
      return numKey * 4;
    },
  }
);
export const colors = {
  fwdOrange: {
    100: "#E87722",
    50: "#F3BB90",
    20: "#FAE4D3",
    5: "#FEF9F4",
  },
  fwdDarkGreen: {
    100: "#183028",
    50: "#859D99",
    20: "#CED8D6",
  },
  black: "#000000",
  fwdGreyDarkest: "#636566",
  fwdGreyDarker: "#8B8E8F",
  fwdGreyDark: "#B3B6B8",
  fwdGrey: {
    100: "#DBDFE1",
    50: "#EDEFF0",
    20: "#F8F9F9",
  },
  white: "#FFFFFF",
  whiteTransparent: "rgba(255, 255, 255, 0)",
  fwdYellow: {
    100: "#FED141",
    50: "#B6E6D8",
    20: "#E2F5F0",
  },
  fwdLightGreen: {
    100: "#6ECEB2",
    50: "#B6E6D8",
    20: "#E2F5F0",
  },
  fwdBlue: {
    100: "#0097A9",
    50: "#7FCBD4",
    20: "#CCEAEE",
  },
  alertRed: "#B30909",
  alertRedLight: "#FEF3F3",
  alertGreen: "#03824F",
  alertGreenLight: "#F1F8F6",
};

const elevation = [
  {
    shadowColor: "#000000",
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  {
    shadowColor: "#000000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1,
    elevation: 1,
  },
  {
    shadowColor: "#000000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
  {
    shadowColor: "#000000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  {
    shadowColor: "#000000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.23,
    shadowRadius: 2.62,
    elevation: 4,
  },
  {
    shadowColor: "#000000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  {
    shadowColor: "#000000",
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.27,
    shadowRadius: 4.65,
    elevation: 6,
  },
  {
    shadowColor: "#000000",
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.29,
    shadowRadius: 4.65,
    elevation: 7,
  },
  {
    shadowColor: "#000000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  {
    shadowColor: "#000000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.32,
    shadowRadius: 5.46,
    elevation: 9,
  },
  {
    shadowColor: "#000000",
    shadowOffset: {
      width: 0,
      height: 5,
    },
    shadowOpacity: 0.34,
    shadowRadius: 6.27,
    elevation: 10,
  },
  {
    shadowColor: "#000000",
    shadowOffset: {
      width: 0,
      height: 5,
    },
    shadowOpacity: 0.36,
    shadowRadius: 6.68,
    elevation: 11,
  },
  {
    shadowColor: "#000000",
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.37,
    shadowRadius: 7.49,
    elevation: 12,
  },
];

export const getElevation = (level: number) => {
  return {
    shadowColor: elevation[level].shadowColor,
    shadowOffset: {
      width: elevation[level].shadowOffset.width,
      height: elevation[level].shadowOffset.height,
    },
    shadowOpacity: elevation[level].shadowOpacity,
    shadowRadius: elevation[level].shadowRadius,
    borderRadius: elevation[level].elevation,
  };
};
