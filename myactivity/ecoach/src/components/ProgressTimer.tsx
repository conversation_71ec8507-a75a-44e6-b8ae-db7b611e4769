import React, { useEffect, useRef, useState } from "react";

type CircularProgressTimerProps = {
  size?: number;
  strokeWidth?: number;
  setLeavingModalVisible?: React.Dispatch<React.SetStateAction<boolean>>;
  setTimeOutStatus?: React.Dispatch<React.SetStateAction<boolean>>;
  timeRemainingNotification?: number;
  totalTime?: number;
  reTryTime: number; // Add key prop for reset countdown
};

export const TWO_MIN_CALL_TIME = 2 * 60 + 5;
export const DEFAULT_CALL_TIME = 8 * 60 + 5;

const CircularProgressTimer: React.FC<CircularProgressTimerProps> = ({
  size = 64,
  strokeWidth = 6,
  setLeavingModalVisible,
  setTimeOutStatus,
  timeRemainingNotification = 30,
  totalTime = TWO_MIN_CALL_TIME,
  reTryTime,
}) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const [timeLeft, setTimeLeft] = useState<number>(totalTime);
  const [progress, setProgress] = useState<number>(circumference);
  const timerRef = useRef<any>();

  useEffect(() => {
    setTimeLeft(totalTime);
  }, [reTryTime, totalTime]);

  useEffect(() => {
    if (totalTime) {
      setTimeLeft(totalTime);
    }
  }, [totalTime]);

  useEffect(() => {
    timerRef.current = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime <= 1) {
          setTimeOutStatus?.(true);
          clearInterval(timerRef.current);
          return 0;
        }
        if (prevTime - 1 === timeRemainingNotification) {
          setLeavingModalVisible?.(true);
        }
        return prevTime - 1;
      });
    }, 1000);

    return () => clearInterval(timerRef.current);
  }, [totalTime, timeRemainingNotification, reTryTime]);

  useEffect(() => {
    setProgress((timeLeft / totalTime) * circumference);
  }, [timeLeft, circumference, totalTime]);

  const minutes = Math.floor(timeLeft / 60);
  const seconds = timeLeft % 60;

  const isLessThanAMinute = timeLeft < timeRemainingNotification;
  const strokeColor = isLessThanAMinute ? "#DC3D43" : "#E87722";
  const textColor = isLessThanAMinute ? "#DC3D43" : "#183028";

  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <svg width={size} height={size} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64">
        <g transform={"rotate(-90 32 32)"} origin={`${size / 2}, ${size / 2}`} fill={"#fff"}>
          <circle cx={size / 2} cy={size / 2} r={radius} strokeWidth={strokeWidth} />
          <circle
            stroke={strokeColor}
            cx={size / 2}
            cy={size / 2}
            r={radius}
            strokeWidth={strokeWidth}
            strokeDasharray={`${circumference} ${circumference}`}
            strokeDashoffset={progress}
            strokeLinecap="round"
          />
        </g>
      </svg>
      <div
        style={{
          color: textColor,
          fontWeight: "bold",
          fontSize: "1.1rem",
          position: "absolute",
          letterSpacing: "-0.3px",
        }}
      >
        {`${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`}
      </div>
    </div>
  );
};

export default CircularProgressTimer;
