import React from "react";
import styled from "styled-components";
import { Body, H6, sizes, SmallLabel } from "../CubeBaseElement";
import { cheveronRightInCircle, mobileQFCard, mobileRPCard, tabletQFCard, tabletRPCard } from "../../assets";
import { device } from "../../styles/media";
import useWindowResize from "../../hooks/useWindowResize";

const Container = styled.div<{ backgroundColor: string }>`
  height: 270px;
  display: flex;
  flex: 1;
  flex-direction: column;
  padding: ${sizes[4]}px;
  border-radius: ${sizes[4]}px;
  position: relative;
  cursor: pointer;
  background-color: ${({ backgroundColor }) => backgroundColor};
  border: 1px solid #f3bb90;
  width: 166px;
  @media ${device.noMobile} {
    height: 144px;
    width: 100%;
  }
`;

const CardImage = styled.img`
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100px;
`;

const Circle = styled.img`
  margin-top: ${sizes[2]}px;
  width: 32px;
`;

const Tag = styled.div`
  position: absolute;
  display: flex;
  top: -${sizes[3]}px;
  left: ${sizes[3]}px;
  width: 52px;
  height: ${sizes[6]}px;
  border-radius: ${sizes[5]}px;
  background-color: white;
  align-items: center;
  justify-content: center;
`;

const Title = styled(H6)`
  width: 133px;

  @media ${device.noMobile} {
    width: 100%;
  }
`;

const Description = styled(Body)`
  width: 139px;
  margin-bottom: 65px;
  @media ${device.noMobile} {
    width: 213px;
    margin-bottom: 0px;
  }
`;

export enum NavigationCardType {
  Quickfire = "quickfire",
  SalesRolePlay = "salesRolePlay",
}

type NavigationCardProps = {
  navigationCardType: NavigationCardType;
  onPress: () => void;
};

const NavigationCard = ({ navigationCardType, onPress }: NavigationCardProps) => {
  const isQF = navigationCardType === NavigationCardType.Quickfire;
  const { width } = useWindowResize();
  const isMobile = width <= 768;
  const backgroundColor = isQF ? "rgba(92, 35, 0, 0.6)" : "rgba(183, 71, 1, 0.6)";
  const timeText = isQF ? "2 phút" : "8 phút";
  const modeText = isQF ? "Chế Độ Phản Xạ Nhanh" : "Chế Độ Nhập Vai";
  const descriptionText = isQF
    ? "Trả lời câu hỏi nhanh và chính xác nhất trong  "
    : "Nhập vai và thực hành kỹ năng bán hàng trong  ";
  const durationText = isQF ? "2 phút " : "8 phút";

  let cardImg = tabletRPCard;
  if (isMobile) {
    if (isQF) {
      cardImg = mobileQFCard;
    } else {
      cardImg = mobileRPCard;
    }
  } else {
    if (isQF) {
      cardImg = tabletQFCard;
    } else {
      cardImg = tabletRPCard;
    }
  }
  return (
    <Container backgroundColor={backgroundColor} onClick={onPress}>
      <Tag>
        <SmallLabel fontWeight="bold" color={"#B74701"}>
          {timeText}
        </SmallLabel>
      </Tag>
      <Title fontWeight="bold" color={"white"}>
        {modeText}
      </Title>
      <Description fontWeight="normal" color={"white"}>
        {descriptionText}
        <Body fontWeight="bold" color={"#FEE8A0"}>
          {durationText}
        </Body>
      </Description>
      <Circle src={cheveronRightInCircle} alt="" />
      <CardImage src={cardImg} alt="" />
    </Container>
  );
};

export default NavigationCard;
