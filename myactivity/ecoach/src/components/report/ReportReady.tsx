import React from "react";
import Image from "next/image";
import styled from "styled-components";
import { colors, H7, sizes } from "../CubeBaseElement";
import { reportDocument } from "../../assets";
import { ButtonPrimary } from "../../../../styles/buttons";

const Container = styled.div`
  margin-top: ${sizes[30]}px;
  flex: 1;
  padding: ${sizes[4]}px;
  gap: ${sizes[7]}px;
  width: 100%;
  align-items: center;
  display: flex;
  flex-direction: column;
`;

const PreparingText = styled(H7)`
  color: ${colors.fwdDarkGreen[100]};
  padding-bottom: ${sizes[4]}px;
`;

type ReportReadyProps = {
  hideReadyReport: () => void;
};

const ReportReady = ({ hideReadyReport }: ReportReadyProps) => {
  return (
    <Container>
      <PreparingText fontWeight={"bold"} style={{ textAlign: "center" }} color={colors.fwdDarkGreen[100]}>
        Báo cáo của bạn đã sẵn sàng!
      </PreparingText>
      <Image src={reportDocument} width={165} height={165} alt={"img"} />
      <ButtonPrimary onClick={hideReadyReport} style={{ width: 200 }}>
        Xem báo cáo của tôi
      </ButtonPrimary>
    </Container>
  );
};

export default ReportReady;
