import React from "react";
import { useRouter } from "next/router";
import { useSearchParams } from "next/navigation";
import { IconGroupFriends, Star, StarFill, StarHalf, WeatherIcon } from "screens/ecoach/components/icons";
import {
  BtnGroupView,
  CurrentScore,
  DifficultyTypeTitle,
  HeartScore,
  HorizontalView,
  IconGroupFriends2Container,
  IconGroupFriendsContainer,
  IconSkateBoardContainer,
  LeftView,
  MIN_SCORE,
  OverallScoreContainer,
  OverallScoreContainerMobile,
  PageImageBackground,
  PrimaryScoreContainer,
  ReportContent,
  RightView,
  ScoreBar,
  ScoreBarContainer,
  ScoreBarTrack,
  ScoreContent,
  ScoreItem,
  ScoreProductBar,
  ScoreProductTitle,
  ScoreSection,
  ScoreSectionBorder,
  ScoreSectionContainer,
  ScoreSectionInner,
  ScoreText,
  SeeMoreContainer,
  SeeMoreContent,
  Spacer,
  StarContainer,
  TotalScore,
  WeatherIconContainer,
  YourReportText,
} from "./styled2";
import { extractScore } from "../../ultils/extractScore";
import { colors, getElevation, H8, sizes, SmallBody, SmallLabel } from "../CubeBaseElement";
import { SkillDetail } from "../../@custom-types/ecoach";
import reportBackground2 from "../../assets/reportBackground2.png";
import { IconGroupFriends2 } from "../icons/GroupFriends2Icon";
import { ColleaguesIcon } from "../icons/ColleaguesIcon";
import { SkateBoardIcon } from "../icons/SkateBoardIcon";
import { ButtonPrimary, ButtonSecondary } from "../../../../styles/buttons";
import IconWarning from "../../../../components/icons/icon-warning";
import { ReportDetails } from "./ReportDetails";
import moment from "moment";
import Icons from "../icons/ChevronRight";
import { URLSearchParams } from "next/dist/compiled/@edge-runtime/primitives/url";
import ReportFeedbackModal from "../modal/ReportFeedbackModal";
import { BtnGroupContainer } from "../../screens/report/styled";

const getTitleText = (skill: string) => {
  if (skill === "customer_relationship") {
    return "Xây Dựng Mối Quan Hệ";
  }
  if (skill === "customer_discovery") {
    return "Khám Phá Khách Hàng";
  }
  if (skill === "applied_product_knowledge") {
    return "Kiến Thức Sản Phẩm";
  }
  if (skill === "objection_handling_closing") {
    return "Phản Đối Và Kết Thúc";
  }

  return "Phát Biểu Và Phân Tích";
};

const renderStar = (overallScore: number) => {
  if (overallScore < 14)
    return (
      <>
        <Star />
        <Star />
        <Star />
      </>
    );
  else if (overallScore >= 14 && overallScore < 28)
    return (
      <>
        <StarHalf />
        <Star />
        <Star />
      </>
    );
  else if (overallScore >= 28 && overallScore < 42)
    return (
      <>
        <StarFill />
        <Star />
        <Star />
      </>
    );
  else if (overallScore >= 42 && overallScore < 56)
    return (
      <>
        <StarFill />
        <StarHalf />
        <Star />
      </>
    );
  else if (overallScore >= 56 && overallScore < 70)
    return (
      <>
        <StarFill />
        <StarFill />
        <Star />
      </>
    );
  else if (overallScore >= 70 && overallScore < 84)
    return (
      <>
        <StarFill />
        <StarFill />
        <StarHalf />
      </>
    );
  else if (overallScore >= 84)
    return (
      <>
        <StarFill />
        <StarFill />
        <StarFill />
      </>
    );
  return (
    <>
      <Star />
      <Star />
      <Star />
    </>
  );
};

const renderScoreBar = (skill: any, scoreString: string) => {
  if (!scoreString) {
    return null;
  }

  const score = extractScore(scoreString);
  const color =
    skill === "objection_handling_closing"
      ? colors.fwdBlue[100]
      : skill === "customer_relationship"
      ? colors.fwdLightGreen[100]
      : skill === "communication_skills"
      ? colors.fwdBlue[50]
      : skill === "applied_product_knowledge"
      ? colors.fwdLightGreen[50]
      : colors.fwdLightGreen[50];

  return (
    <ScoreBar color={color}>
      <ScoreBarTrack color={color} score={score} />
    </ScoreBar>
  );
};

type ReportProps = {
  reportDetails: {
    report_is_ready: string;
    conversation_id: string;
    created_at: string;
    datetime: string;
    difficulty: string;
    duration: number;
    product_selection?: string;
    session_number?: number;
    report: ReportDetails;
  };
  isFromHistoryList?: boolean;
};

const Report2: React.FC<ReportProps> = ({ reportDetails, isFromHistoryList }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const conversationId = searchParams.get("conversationId") as string;

  const goHistoryScreen = () => {
    router.push(`/ecoach/session-history`);
  };

  const tryAgain = () => {
    const urlParams = new URLSearchParams(searchParams.toString());

    urlParams.delete("conversationId");
    urlParams.delete("ref");

    router.push(`/ecoach/video-call?${urlParams.toString()}`);
    // reTake?.();
  };

  const convertSecondsToMins = (duration: number) => {
    const minutes = Math.floor(duration / 60);
    const seconds = (duration % 60).toFixed(2); // To get two decimal places for seconds
    return `${minutes} ${"phút"} ${seconds} ${"giây"}`;
  };

  const goDetailSummaryPage = (skill: string, skillSet: { [key: string]: SkillDetail }[], event: any) => {
    event.preventDefault();

    router.push(`/ecoach/report/details?${searchParams.toString()}&skill=${skill}`);
  };

  const renderBottomText = (skill: any, scoreString: string) => {
    if (!scoreString) {
      return null;
    }

    const score = extractScore(scoreString);

    return (
      <SeeMoreContainer>
        <SeeMoreContent>
          <SmallBody fontWeight={"bold"} color={colors.fwdOrange[100]} style={{ textAlign: "left" }}>
            {score < 50 ? "Xem cách bạn có thể làm tốt hơn" : "Xem thêm chi tiết"}
          </SmallBody>
          {score < 50 && skill === "applied_product_knowledge" && <IconWarning width={13} fill={colors.alertRed} />}
        </SeeMoreContent>
        <Icons.ChevronRight />
      </SeeMoreContainer>
    );
  };

  const renderTitle = (skill: string) => {
    return (
      <H8 fontWeight={"600"} color={colors.fwdGrey[50]} style={{ display: "inline-block", lineHeight: "18px" }}>
        {getTitleText(skill)}
      </H8>
    );
  };

  const { conversation_id, datetime, difficulty, duration, report } = reportDetails || {};

  const {
    overall_score,
    customer_relationship_score,
    skill_set_details,
    customer_discovery_score,
    applied_product_knowledge_score,
    objection_handling_closing_score,
    communication_skills_score,
  } = report || {};

  // const overall_score = `${Math.round(Math.random() * 100)}`;
  // const customer_relationship_score = `${Math.round(Math.random() * 100)}`;
  // const customer_discovery_score = `${Math.round(Math.random() * 100)}`;
  // const applied_product_knowledge_score = `${Math.round(Math.random() * 100)}`;
  // const objection_handling_closing_score = `${Math.round(Math.random() * 100)}`;
  // const communication_skills_score = `${Math.round(Math.random() * 100)}`;

  const skillScores: { [key: string]: string } = {
    objection_handling_closing: objection_handling_closing_score,
    customer_relationship: customer_relationship_score,
    customer_discovery: customer_discovery_score,
    communication_skills: communication_skills_score,
  };

  return (
    <div>
      <PageImageBackground src={reportBackground2} alt={"loading"} width={8000} priority={true} />
      <WeatherIconContainer>
        <WeatherIcon />
      </WeatherIconContainer>
      <div style={{ flex: 1, marginTop: sizes[4] }}>
        <ReportContent>
          <HorizontalView>
            <YourReportText fontWeight={"bold"}>Báo cáo của bạn</YourReportText>
          </HorizontalView>
          <HorizontalView style={{ justifyContent: "flex-start", gap: sizes[8] }}>
            <LeftView>
              <HorizontalView>
                <SmallLabel fontWeight="bold" color={colors.fwdDarkGreen[50]}>
                  Phiên làm việc
                </SmallLabel>
                <SmallLabel fontWeight="normal" color={colors.fwdDarkGreen[50]}>
                  {conversation_id}
                </SmallLabel>
              </HorizontalView>

              <HorizontalView>
                <DifficultyTypeTitle fontWeight={"normal"}>Độ khó</DifficultyTypeTitle>
                <H8 fontWeight="bold" color={colors.fwdDarkGreen[100]}>
                  {difficulty === "1" ? "Người Mới Bắt Đầu" : "Chuyên gia"}
                </H8>
              </HorizontalView>
            </LeftView>
            <RightView>
              <HorizontalView>
                <SmallLabel fontWeight="bold" color={colors.fwdDarkGreen[50]}>
                  Ngày và Giờ
                </SmallLabel>
                <SmallLabel fontWeight="normal" color={colors.fwdDarkGreen[50]}>
                  {moment(datetime).locale("vi").format("DD MMM YYYY")}
                </SmallLabel>
              </HorizontalView>

              <HorizontalView>
                <H8 fontWeight="normal" color={colors.fwdDarkGreen[100]}>
                  Thời lượng
                </H8>
                <H8 fontWeight="bold" color={colors.fwdDarkGreen[100]}>
                  {convertSecondsToMins(duration)}
                </H8>
              </HorizontalView>
            </RightView>
          </HorizontalView>

          {overall_score && skill_set_details && (
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                gap: sizes[4],
                marginTop: sizes[4],
              }}
            >
              <OverallScoreContainer
                style={{
                  ...getElevation(5),
                }}
              >
                <StarContainer>{renderStar(extractScore(overall_score))}</StarContainer>
                <ScoreText fontWeight={"bold"} color={colors.fwdDarkGreen[50]}>
                  TỔNG ĐIỂM
                </ScoreText>
                <HeartScore>
                  <CurrentScore color={colors.white} fontWeight={"bold"}>
                    {extractScore(overall_score)}
                  </CurrentScore>
                  <TotalScore fontWeight={"normal"} color={"#8B9793"}>
                    /100
                  </TotalScore>
                </HeartScore>
                {extractScore(overall_score) < MIN_SCORE ? (
                  <IconGroupFriends2Container>
                    <IconGroupFriends2 />
                  </IconGroupFriends2Container>
                ) : (
                  <IconGroupFriendsContainer>
                    <IconGroupFriends />
                  </IconGroupFriendsContainer>
                )}
              </OverallScoreContainer>

              <ScoreSectionContainer
                style={{
                  ...getElevation(5),
                }}
              >
                <ScoreSectionBorder>
                  <ScoreSection>
                    <OverallScoreContainerMobile
                      style={{
                        ...getElevation(5),
                      }}
                    >
                      <StarContainer>{renderStar(extractScore(overall_score))}</StarContainer>
                      <ScoreText fontWeight={"bold"} color={colors.fwdDarkGreen[50]}>
                        TỔNG ĐIỂM
                      </ScoreText>
                      <HeartScore>
                        <CurrentScore color={colors.white} fontWeight={"bold"}>
                          {extractScore(overall_score)}
                        </CurrentScore>
                        <TotalScore fontWeight={"normal"} color={"#8B9793"}>
                          /100
                        </TotalScore>
                      </HeartScore>
                    </OverallScoreContainerMobile>

                    <PrimaryScoreContainer>
                      <ScoreSectionInner
                        score={applied_product_knowledge_score}
                        key={"applied_product_knowledge"}
                        borderWidth={2}
                        onClick={(event) =>
                          goDetailSummaryPage(
                            "applied_product_knowledge",
                            skill_set_details?.applied_product_knowledge,
                            event
                          )
                        }
                      >
                        <ScoreProductTitle>{renderTitle("applied_product_knowledge")}</ScoreProductTitle>
                        <ScoreProductBar>
                          <ScoreBarContainer>
                            {renderScoreBar("applied_product_knowledge", applied_product_knowledge_score)}
                            <div>
                              <H8 fontWeight={"bold"} color={colors.fwdGrey[50]}>
                                {extractScore(applied_product_knowledge_score)}
                              </H8>
                            </div>
                          </ScoreBarContainer>
                          {renderBottomText("applied_product_knowledge", applied_product_knowledge_score)}
                        </ScoreProductBar>
                      </ScoreSectionInner>
                    </PrimaryScoreContainer>

                    <ScoreContent>
                      {Object.entries(skillScores).map(
                        ([skill, scoreString]) =>
                          skill !== "applied_product_knowledge" && (
                            <ScoreItem key={skill}>
                              <ScoreSectionInner
                                borderColor={"#DBDFE1"}
                                marginBottom={0}
                                onClick={(event) =>
                                  goDetailSummaryPage(skill, (skill_set_details as any)?.[skill], event)
                                }
                              >
                                <ScoreProductTitle>{renderTitle(skill)}</ScoreProductTitle>
                                <ScoreProductBar>
                                  <ScoreBarContainer>
                                    {renderScoreBar(skill, scoreString)}
                                    <div>
                                      <H8 fontWeight={"bold"} color={colors.fwdGrey[50]}>
                                        {extractScore(scoreString)}
                                      </H8>
                                    </div>
                                  </ScoreBarContainer>
                                  {renderBottomText(skill, scoreString)}
                                </ScoreProductBar>
                              </ScoreSectionInner>
                            </ScoreItem>
                          )
                      )}
                    </ScoreContent>
                  </ScoreSection>
                </ScoreSectionBorder>

                {extractScore(overall_score) < MIN_SCORE ? (
                  <IconSkateBoardContainer>
                    <ColleaguesIcon />
                  </IconSkateBoardContainer>
                ) : (
                  <IconSkateBoardContainer>
                    <SkateBoardIcon />
                  </IconSkateBoardContainer>
                )}
              </ScoreSectionContainer>
            </div>
          )}

          {!isFromHistoryList && <Spacer height={sizes[12]} />}

          {!isFromHistoryList && (
            <BtnGroupContainer>
              <BtnGroupView>
                <ButtonPrimary onClick={tryAgain} style={{ flex: 1 }}>
                  Hãy thử lại
                </ButtonPrimary>
                <ButtonSecondary onClick={goHistoryScreen} style={{ flex: 1 }}>
                  Xem lịch sử
                </ButtonSecondary>
              </BtnGroupView>
              <ReportFeedbackModal conversationId={conversationId} />
            </BtnGroupContainer>
          )}
        </ReportContent>
      </div>
    </div>
  );
};

export { Report2 };
