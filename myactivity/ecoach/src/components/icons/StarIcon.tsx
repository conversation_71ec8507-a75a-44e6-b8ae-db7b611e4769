import React from "react";
import { SvgIconProps } from "../../assets/icons/SvgIconProps";

const StarIcon = (props: SvgIconProps): JSX.Element => {
  return (
    <svg width={props.width || props.size} height={props.height || props.size} viewBox="0 0 24 24" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.897 16.134c.149 0 .298.037.434.112l3.327 1.826-.632-3.845c-.054-.328.05-.663.277-.893l2.661-2.71-3.692-.56a.947.947 0 01-.71-.541l-1.665-3.522-1.665 3.522a.948.948 0 01-.71.542l-3.692.56 2.662 2.709c.227.23.33.565.276.893l-.632 3.845 3.328-1.826a.896.896 0 01.433-.112zm5.18 4.986a.9.9 0 01-.434-.112l-4.583-2.515-4.582 2.515a.9.9 0 01-.992-.082 1.03 1.03 0 01-.372-.978l.873-5.307-3.697-3.762a1.042 1.042 0 01-.245-1.028.961.961 0 01.765-.684l5.12-.776 2.287-4.839A.94.94 0 0112.06 3a.94.94 0 01.844.552l2.287 4.839 5.12.776a.96.96 0 01.763.684c.111.364.016.763-.244 1.027l-3.695 3.763.871 5.307c.061.374-.082.753-.372.978a.904.904 0 01-.557.194z"
        fill={props.fill}
      />
    </svg>
  );
};

export default StarIcon;
