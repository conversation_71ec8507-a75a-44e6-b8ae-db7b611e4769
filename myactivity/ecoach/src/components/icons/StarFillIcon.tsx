import React from "react";
import { SvgIconProps } from "../../assets/icons/SvgIconProps";

const StarFillIcon = (props: SvgIconProps): JSX.Element => {
  return (
    <svg width={props.width || props.size} height={props.height || props.size} viewBox="0 0 24 24" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.922 21.116c-.157 0-.314-.037-.46-.112l-4.857-2.515-4.859 2.515a1 1 0 01-1.445-1.06l.925-5.306-3.918-3.761a1 1 0 01.55-1.711l5.428-.777 2.425-4.838a1 1 0 011.787 0l2.425 4.838 5.427.777a1 1 0 01.551 1.71l-3.918 3.762.925 5.307a1 1 0 01-.986 1.171z"
        fill={props.fill}
      />
    </svg>
  );
};

export default StarFillIcon;
