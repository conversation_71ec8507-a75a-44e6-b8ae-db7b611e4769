import React from "react";
import { SvgIconProps } from "../../assets/icons/SvgIconProps";

const TickCircleFillIcon = (props: SvgIconProps): JSX.Element => {
  return (
    <svg width={props.width || props.size} height={props.height || props.size} viewBox="0 0 24 24" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 23c6.075 0 11-4.925 11-11S18.075 1 12 1 1 5.925 1 12s4.925 11 11 11zm5-14.254l-.31-.333a1.251 1.251 0 00-1.858 0l-4.585 4.925-1.079-1.16a1.251 1.251 0 00-1.858 0l-.31.333L10.248 16 17 8.746z"
        fill={props.fill}
      />
    </svg>
  );
};

export default TickCircleFillIcon;
