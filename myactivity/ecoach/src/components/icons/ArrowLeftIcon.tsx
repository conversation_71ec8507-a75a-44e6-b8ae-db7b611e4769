import React from "react";
import { SvgIconProps } from "../../assets/icons/SvgIconProps";

const ArrowLeftIcon = (props: SvgIconProps): JSX.Element => {
  return (
    <svg width={props.width || props.size} height={props.height || props.size} viewBox="0 0 24 24" fill="none">
      <g id="arrow-left">
        <path
          id="Icon"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M21.4139 11.0846H6.82888L13.1209 4.79263L12.7679 4.43913C12.1819 3.85362 11.2319 3.85362 10.6464 4.43913L2.99988 12.0851L10.6464 19.7316C11.2319 20.3171 12.1819 20.3171 12.7679 19.7316L13.1209 19.3781L6.82738 13.0846H21.4139V11.0846Z"
          fill={props.fill}
        />
      </g>
    </svg>
  );
};

export default ArrowLeftIcon;
