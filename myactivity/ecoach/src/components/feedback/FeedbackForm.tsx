import React, { useCallback, useEffect, useMemo, useState } from "react";
import styled from "styled-components";
import { ConversationFeedbackPayload, ConversationFeedBackType } from "../../@custom-types/ecoach";
import { StarRating } from "./StarRating";
import { colors, LargeBody, sizes, SmallLabel } from "../CubeBaseElement";
import { ImprovementSection } from "./ImprovementSection";
import InputFieldSet from "../../../../components/input-fileldset";
import { ButtonPrimary } from "../../../../styles/buttons";
import useActionApi from "../../../../hooks/use-action-api";
import { submitConversationFeedback } from "../../api/ecoach";

type FeedbackFormProps = {
  selectedStar: number;
  handleUserUpdate?: (selectedStar?: number) => void;
  conversationId?: string;
  centerItem?: boolean;
  title?: string;
  feedbackType?: ConversationFeedBackType;
  onSubmitted?: (value: boolean) => void;
};

const FeedbackForm: React.FC<FeedbackFormProps> = ({
  selectedStar,
  handleUserUpdate,
  conversationId,
  onSubmitted,
  centerItem = true,
  feedbackType = "REPORT",
  title,
}) => {
  const [starRating, setStarRating] = React.useState<number>(selectedStar);
  const [feedbackReason, setFeedbackReason] = useState<null | string>(null);
  const [comment, setComment] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const sendAppFeedbackRequest = useActionApi<ConversationFeedbackPayload, any>(submitConversationFeedback);

  useEffect(() => {
    setStarRating(selectedStar);
  }, [selectedStar]);

  const isSubmitActive = useMemo(
    () => (starRating === 5 && !!comment) || (starRating !== 0 && feedbackReason !== null && !!comment),
    [comment, starRating, feedbackReason]
  );

  const handleChange = useCallback(
    (value: number) => {
      setStarRating(value);
      handleUserUpdate?.(value);
    },
    [handleUserUpdate]
  );

  const handleFeedbackReasonChange = useCallback(
    (value: string) => {
      setFeedbackReason(value);
      handleUserUpdate?.(selectedStar);
    },
    [handleUserUpdate, selectedStar]
  );
  const handleUpdateComment = useCallback(
    (e: { target: { value: string } }) => {
      setComment(e.target.value);
      handleUserUpdate?.();
    },
    [handleUserUpdate]
  );

  const handleSubmit = useCallback(async () => {
    if (!isSubmitActive) {
      return;
    }
    setError("");
    setIsLoading(true);

    try {
      const comments = [];

      if (starRating < 5) {
        comments.push(
          `${starRating < 3 ? "Bạn không thích điều gì" : "Chúng tôi có thể cải thiện điều gì"}: ${feedbackReason}`
        );
      }

      comments.push(`Comments: ${comment}`);
      const success = await sendAppFeedbackRequest({
        body: {
          conversation_id: conversationId,
          star: starRating,
          feedback_type: feedbackType || "REPORT",
          comments: comments,
        },
        loading: { type: "local", name: "sendAppFeedbackRequest" },
      });
      // console.log("FeedbackForm success", success.data);
      if (!success?.data) {
        setError("Có lỗi xảy ra khi gửi phản hồi");
        return;
      }

      setComment("");
      setStarRating(0);
      onSubmitted?.(true);
    } catch (ex) {
      console.log("FeedbackForm error", ex);
      setError("Có lỗi xảy ra khi gửi phản hồi");
    } finally {
      setIsLoading(false);
    }
  }, [isSubmitActive, onSubmitted, conversationId, feedbackType, comment, starRating, feedbackReason]);

  return (
    <Container>
      <StarRating title={title} centerItem={centerItem} defaultValue={starRating} onChange={handleChange} />
      {starRating > 0 ? (
        <div>
          <Divider />
          <div>
            {starRating < 5 && (
              <ImprovementSection
                starRating={starRating}
                value={feedbackReason}
                onChange={handleFeedbackReasonChange}
              />
            )}
            <Content>
              {starRating >= 5 && (
                <TellUsMoreContainer>
                  <LargeBody fontWeight="bold" style={{ alignItems: "center" }}>
                    Mời bạn chia sẻ thêm về trải nghiệm của mình
                  </LargeBody>
                </TellUsMoreContainer>
              )}
              <InputContainer>
                <InputFieldSet
                  id="username"
                  name="Bình luận"
                  value={comment}
                  placeholder="Bình luận"
                  onChange={handleUpdateComment}
                  disabled={isLoading}
                />
              </InputContainer>

              <ButtonPrimary onClick={handleSubmit} disabled={!isSubmitActive} size={"large"} maxWidth={true}>
                <LargeBody fontWeight="bold" style={{ alignItems: "center" }}>
                  Gửi
                </LargeBody>
              </ButtonPrimary>
              <SmallLabel color={colors.alertRed}>{error}</SmallLabel>
            </Content>
          </div>
        </div>
      ) : null}
    </Container>
  );
};

const Container = styled.div`
  display: flex;
  flex-direction: column;
  padding-top: ${sizes[4]}px;
  padding-bottom: ${sizes[4]}px;
`;

const Divider = styled.div`
  margin: ${sizes[5]}px -${sizes[4]}px;
  height: 1px;
  background-color: ${colors.fwdGrey[100]};
`;

const TellUsMoreContainer = styled.div`
  margin-top: -${sizes[4]}px;
  margin-bottom: ${sizes[1]}px;
  display: flex;
  flex-direction: row;
`;

const Content = styled.div`
  width: 100%;
  margin-top: ${sizes[4]}px;
`;

const InputContainer = styled.div`
  margin-top: ${sizes[8]}px;
  margin-bottom: ${sizes[6]}px;
`;

export { FeedbackForm };
