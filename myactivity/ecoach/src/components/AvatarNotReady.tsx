import React from "react";
import { useRouter } from "next/router";
import Image from "next/image";
import { girlListenMusic } from "../assets";
import { colors, H6, sizes } from "./CubeBaseElement";
import styled from "styled-components";
import ModalFullPage from "../../../components/modal-full-page";

const CenterView = styled.div`
  justify-content: center;
  height: 261px;
  border-radius: ${sizes[4]}px;
  padding: ${sizes[6]}px;
  background-color: ${colors.white};
  align-items: center;
  display: flex;
  flex-direction: column;
`;

const Container = styled.div`
  display: flex;
  background-color: #fff;
  justify-content: center;
  align-items: center;
  flex: 1;
  height: calc(100vh - 56px);
  width: 100%;
`;

const GirlImage = styled(Image)`
  width: 145px;
  height: 138px;
`;

const NotReadyText = styled(H6)`
  color: ${colors.fwdOrange[100]};
  text-align: center;
  margin-top: 1rem;
`;

const AvatarNotReady = () => {
  const router = useRouter();

  const onDismiss = () => {
    router.push("/ecoach");
  };

  return (
    <ModalFullPage show={true} title="" onClose={onDismiss}>
      <Container onClick={onDismiss}>
        <CenterView>
          <GirlImage src={girlListenMusic} width={145} height={139} alt={"avatar"} />
          <NotReadyText fontWeight={"bold"} color={colors.fwdOrange[100]}>
            Trợ lý ảo chưa sẵn sàng, vui lòng quay lại và thử lại sau!
          </NotReadyText>
        </CenterView>
      </Container>
    </ModalFullPage>
  );
};

export default AvatarNotReady;
