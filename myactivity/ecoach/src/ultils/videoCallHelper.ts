import { DifficultType } from "../@custom-types/ecoach";

type ParamType = {
  difficultType: DifficultType;
  productSelectionCode: string;
  productFlowType: string;
  agentName: string;
};

type ResultType = {
  startMessage: string;
  levelSelection: string;
  productSelection: string; // set-for-life, set-for-health, quickfire
  profile: string;
  enableFastModel: string;
  wsAudioOutput: boolean;
};

export const getWebSocketParam = async (routerParams: ParamType) => {
  const { difficultType, productSelectionCode, productFlowType, agentName } = routerParams;

  let productSelection = productSelectionCode;

  if (productSelectionCode === "set_for_life" || productSelectionCode === "set_for_health") {
    productSelection = productSelectionCode.replaceAll("_", "-");
  }

  const result: ResultType = {
    startMessage: "",
    levelSelection: difficultType === DifficultType.Expert ? "2" : "1",
    productSelection,
    enableFastModel: "True",
    profile: JSON.stringify({
      agent_name: agent<PERSON><PERSON>,
      yoe: "0 - 1",
      level: "Trainee",
      gender: "Male",
    }),
    wsAudioOutput: true,
  };

  for (const key in result) {
    if (result[key as keyof ResultType] === null || result[key as keyof ResultType] === undefined) {
      delete result[key as keyof ResultType];
    }
  }

  return result;
};
