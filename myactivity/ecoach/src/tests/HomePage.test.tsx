import React from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import { ThemeProvider } from 'styled-components';
import HomePage from '../screens/home/<USER>';
import appSlice from '../store/appSlice';
import ecoachSlice from '../store/ecoachSlice';
import { theme } from '../styles/theme';

// Mock the hooks to avoid API calls in tests
jest.mock('../hooks/useExchangeCubeToken', () => ({
  useExchangeCubeToken: () => ({
    cubeToken: 'mock-token-123'
  })
}));

jest.mock('../hooks/useGetEcoachConfigurationData', () => ({
  useGetEcoachConfigurationData: () => ({
    ecoachConfig: {
      isQuickfire: true,
      isTrainerGuruEnabled: true,
      quickfireVideoUrl: null,
      homepageBackground: null,
      quickfireCompletedHearts: 3,
      productConfig: {},
      quickfireProductConfig: {}
    }
  })
}));

const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      app: appSlice,
      ecoach: ecoachSlice,
    },
    preloadedState: {
      app: {
        user: {
          channel: 'AGENCY',
          email: '<EMAIL>',
          name: 'Test User',
          username: 'testuser',
          designation: 'Agent',
          designationDesc: 'Sales Agent',
        }
      },
      ecoach: {
        isQuickfire: true,
        isTrainerGuruEnabled: true,
        quickfireVideoUrl: null,
        quickfireCompletedHearts: 3,
        cubeToken: 'mock-token-123',
        conversationsData: {},
      },
      ...initialState
    }
  });
};

const renderWithProviders = (component: React.ReactElement, store = createTestStore()) => {
  return render(
    <Provider store={store}>
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          {component}
        </ThemeProvider>
      </BrowserRouter>
    </Provider>
  );
};

describe('HomePage - TGEntryPointCard Logic Integration', () => {
  test('renders welcome message when user has access', () => {
    renderWithProviders(<HomePage />);
    
    expect(screen.getByText('Welcome to Ecoach')).toBeInTheDocument();
    expect(screen.getByText(/Practice your sales skills/)).toBeInTheDocument();
  });

  test('shows access restricted for non-AGENCY/BANCA channels', () => {
    const store = createTestStore({
      app: {
        user: {
          channel: 'OTHER',
          email: '<EMAIL>',
          name: 'Test User',
          username: 'testuser',
          designation: 'Agent',
          designationDesc: 'Sales Agent',
        }
      }
    });

    renderWithProviders(<HomePage />, store);
    
    expect(screen.getByText('Access Restricted')).toBeInTheDocument();
    expect(screen.getByText(/not available for your account type/)).toBeInTheDocument();
  });

  test('shows access restricted when trainer guru is disabled', () => {
    const store = createTestStore({
      ecoach: {
        isQuickfire: true,
        isTrainerGuruEnabled: false,
        quickfireVideoUrl: null,
        quickfireCompletedHearts: 3,
        cubeToken: 'mock-token-123',
        conversationsData: {},
      }
    });

    renderWithProviders(<HomePage />, store);
    
    expect(screen.getByText('Access Restricted')).toBeInTheDocument();
  });

  test('renders action buttons when access is granted', () => {
    renderWithProviders(<HomePage />);
    
    expect(screen.getByText('Start Quickfire Session')).toBeInTheDocument();
    expect(screen.getByText('Start Full Experience')).toBeInTheDocument();
    expect(screen.getByText('View Session History')).toBeInTheDocument();
    expect(screen.getByText('View Guidelines')).toBeInTheDocument();
  });
});
